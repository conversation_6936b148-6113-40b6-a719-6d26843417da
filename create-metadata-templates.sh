#!/bin/bash

# Create Metadata Service Templates
echo "🔧 Creating Metadata Service Templates"
echo "====================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Create metadata fetch template
print_info "Creating metadata fetch template..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/templates/metadata_fetch.html << '"'"'EOF'"'"'
{% extends "layout.html" %}
{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-3 col-lg-2 sidebar">
            {% include 'sidebar.html' %}
        </div>
        <div class="col-sm-9 col-lg-10 main">
            <h1>{{_("Fetch Metadata")}} - {{ book.title }}</h1>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{_("Current Book Information")}}</h4>
                        </div>
                        <div class="panel-body">
                            <p><strong>{{_("Title")}}:</strong> {{ book.title }}</p>
                            <p><strong>{{_("Authors")}}:</strong> 
                                {% for author in book.authors %}
                                    {{ author.name }}{% if not loop.last %}, {% endif %}
                                {% endfor %}
                            </p>
                            <p><strong>{{_("Tags")}}:</strong> 
                                {% for tag in book.tags %}
                                    <span class="label label-default">{{ tag.name }}</span>
                                {% endfor %}
                            </p>
                            <p><strong>{{_("Series")}}:</strong> 
                                {% for series in book.series %}
                                    {{ series.name }}
                                {% endfor %}
                            </p>
                            <p><strong>{{_("Publishers")}}:</strong> 
                                {% for publisher in book.publishers %}
                                    {{ publisher.name }}{% if not loop.last %}, {% endif %}
                                {% endfor %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            <h4>{{_("Fetch External Metadata")}}</h4>
                        </div>
                        <div class="panel-body">
                            <form id="metadata-fetch-form">
                                <input type="hidden" id="book_id" value="{{ book.id }}">
                                
                                <div class="form-group">
                                    <label for="search_title">{{_("Title for Search")}}</label>
                                    <input type="text" class="form-control" id="search_title" 
                                           value="{{ book.title }}" placeholder="Book title">
                                </div>
                                
                                <div class="form-group">
                                    <label for="search_author">{{_("Author for Search")}}</label>
                                    <input type="text" class="form-control" id="search_author" 
                                           value="{% for author in book.authors %}{{ author.name }}{% if not loop.last %}, {% endif %}{% endfor %}" 
                                           placeholder="Author name">
                                </div>
                                
                                <div class="form-group">
                                    <label for="search_isbn">{{_("ISBN (if known)")}}</label>
                                    <input type="text" class="form-control" id="search_isbn" 
                                           placeholder="ISBN-10 or ISBN-13">
                                </div>
                                
                                <button type="button" class="btn btn-info" onclick="previewMetadata()">
                                    {{_("Preview Metadata")}}
                                </button>
                                <button type="button" class="btn btn-primary" onclick="fetchAndUpdate()">
                                    {{_("Fetch & Update")}}
                                </button>
                                <a href="/book/{{ book.id }}" class="btn btn-default">{{_("Back to Book")}}</a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="preview-section" class="row" style="display: none; margin-top: 20px;">
                <div class="col-md-12">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4>{{_("Metadata Preview")}}</h4>
                        </div>
                        <div class="panel-body" id="preview-content">
                            <!-- Preview content will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="result-section" class="row" style="display: none; margin-top: 20px;">
                <div class="col-md-12">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>{{_("Update Results")}}</h4>
                        </div>
                        <div class="panel-body" id="result-content">
                            <!-- Results will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function previewMetadata() {
    const title = document.getElementById("search_title").value;
    const author = document.getElementById("search_author").value;
    const isbn = document.getElementById("search_isbn").value;
    
    if (!title && !author && !isbn) {
        alert("Please provide at least a title, author, or ISBN");
        return;
    }
    
    const data = { title, author, isbn };
    
    fetch("/api/metadata/preview", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPreview(data.metadata);
        } else {
            alert("Error: " + data.error);
        }
    })
    .catch(error => {
        console.error("Error:", error);
        alert("Failed to fetch metadata preview");
    });
}

function fetchAndUpdate() {
    const bookId = document.getElementById("book_id").value;
    const title = document.getElementById("search_title").value;
    const author = document.getElementById("search_author").value;
    const isbn = document.getElementById("search_isbn").value;
    
    if (!title && !author && !isbn) {
        alert("Please provide at least a title, author, or ISBN");
        return;
    }
    
    const data = { book_id: parseInt(bookId), title, author, isbn };
    
    // Show loading
    const button = event.target;
    button.innerHTML = "Fetching...";
    button.disabled = true;
    
    fetch("/api/metadata/fetch", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        button.innerHTML = "Fetch & Update";
        button.disabled = false;
        
        if (data.success) {
            displayResults(data);
        } else {
            alert("Error: " + data.error);
        }
    })
    .catch(error => {
        console.error("Error:", error);
        button.innerHTML = "Fetch & Update";
        button.disabled = false;
        alert("Failed to fetch and update metadata");
    });
}

function displayPreview(metadata) {
    let html = "<h5>Found Metadata:</h5>";
    
    if (metadata.sources_used) {
        html += `<p><strong>Sources:</strong> ${metadata.sources_used.join(", ")}</p>`;
    }
    
    if (metadata.title) {
        html += `<p><strong>Title:</strong> ${metadata.title}</p>`;
    }
    
    if (metadata.authors) {
        html += `<p><strong>Authors: <AUTHORS>
    }
    
    if (metadata.publishers) {
        html += `<p><strong>Publishers:</strong> ${metadata.publishers.join(", ")}</p>`;
    }
    
    if (metadata.tags) {
        html += `<p><strong>Tags:</strong> `;
        metadata.tags.forEach(tag => {
            html += `<span class="label label-info">${tag}</span> `;
        });
        html += `</p>`;
    }
    
    if (metadata.description) {
        html += `<p><strong>Description:</strong> ${metadata.description.substring(0, 300)}...</p>`;
    }
    
    if (metadata.publish_date) {
        html += `<p><strong>Publish Date:</strong> ${metadata.publish_date}</p>`;
    }
    
    if (metadata.rating) {
        html += `<p><strong>Rating:</strong> ${metadata.rating}/5</p>`;
    }
    
    document.getElementById("preview-content").innerHTML = html;
    document.getElementById("preview-section").style.display = "block";
}

function displayResults(data) {
    let html = "<h5>Update Complete!</h5>";
    
    if (data.sources_used) {
        html += `<p><strong>Sources Used:</strong> ${data.sources_used.join(", ")}</p>`;
    }
    
    if (data.updates_made && data.updates_made.length > 0) {
        html += "<p><strong>Updates Made:</strong></p><ul>";
        data.updates_made.forEach(update => {
            html += `<li>${update}</li>`;
        });
        html += "</ul>";
    } else {
        html += "<p>No updates were made.</p>";
    }
    
    html += `<p><a href="/book/${document.getElementById("book_id").value}" class="btn btn-success">View Updated Book</a></p>`;
    
    document.getElementById("result-content").innerHTML = html;
    document.getElementById("result-section").style.display = "block";
}
</script>
{% endblock %}
EOF'

print_status "Metadata fetch template created"

# Create bulk metadata template
print_info "Creating bulk metadata template..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/templates/metadata_bulk.html << '"'"'EOF'"'"'
{% extends "layout.html" %}
{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-3 col-lg-2 sidebar">
            {% include 'sidebar.html' %}
        </div>
        <div class="col-sm-9 col-lg-10 main">
            <h1>{{_("Bulk Metadata Fetch")}}</h1>

            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-info">
                        <strong>{{_("Note:")}} </strong>
                        {{_("Bulk metadata fetching will process multiple books sequentially. This may take some time depending on the number of books selected.")}}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{_("Select Books for Metadata Update")}}</h4>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label for="book-search">{{_("Search Books")}}</label>
                                <input type="text" class="form-control" id="book-search"
                                       placeholder="Search by title or author..." onkeyup="filterBooks()">
                            </div>

                            <div class="form-group">
                                <button type="button" class="btn btn-sm btn-default" onclick="selectAll()">{{_("Select All")}}</button>
                                <button type="button" class="btn btn-sm btn-default" onclick="selectNone()">{{_("Select None")}}</button>
                                <button type="button" class="btn btn-sm btn-info" onclick="selectMissingMetadata()">{{_("Select Books Missing Metadata")}}</button>
                            </div>

                            <div id="book-list" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                <p>{{_("Loading books...")}}</p>
                            </div>

                            <div class="form-group" style="margin-top: 20px;">
                                <button type="button" class="btn btn-primary" onclick="startBulkFetch()" id="bulk-fetch-btn">
                                    {{_("Start Bulk Metadata Fetch")}}
                                </button>
                                <span id="selected-count" class="text-muted">{{_("No books selected")}}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4>{{_("Bulk Fetch Settings")}}</h4>
                        </div>
                        <div class="panel-body">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" id="update-tags" checked> {{_("Update Tags")}}
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" id="update-description" checked> {{_("Update Description")}}
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" id="update-authors" checked> {{_("Update Authors")}}
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" id="update-publishers"> {{_("Update Publishers")}}
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-warning">
                        <div class="panel-heading">
                            <h4>{{_("Important Notes")}}</h4>
                        </div>
                        <div class="panel-body">
                            <ul>
                                <li>{{_("This process may take several minutes")}}</li>
                                <li>{{_("Rate limiting is applied to respect external APIs")}}</li>
                                <li>{{_("Existing metadata may be overwritten")}}</li>
                                <li>{{_("You can monitor progress below")}}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div id="progress-section" class="row" style="display: none; margin-top: 20px;">
                <div class="col-md-12">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            <h4>{{_("Bulk Fetch Progress")}}</h4>
                        </div>
                        <div class="panel-body">
                            <div class="progress">
                                <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%">
                                    <span id="progress-text">0%</span>
                                </div>
                            </div>
                            <div id="progress-details">
                                <p id="current-book">{{_("Preparing...")}}</p>
                                <div id="results-summary"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let allBooks = [];
let selectedBooks = [];

// Load books on page load
document.addEventListener("DOMContentLoaded", function() {
    loadBooks();
});

function loadBooks() {
    // For now, we'"'"'ll create a simple book list
    // In a real implementation, this would fetch from an API
    fetch("/search?query=*")
        .then(response => response.text())
        .then(html => {
            // Parse the HTML to extract book information
            // This is a simplified approach
            displayBookList();
        })
        .catch(error => {
            console.error("Error loading books:", error);
            document.getElementById("book-list").innerHTML =
                "<p class=\"text-danger\">Error loading books. Please refresh the page.</p>";
        });
}

function displayBookList() {
    // Simplified book list for demonstration
    const sampleBooks = [
        {id: 1, title: "Sample Book 1", author: "Author 1", tags: ["fiction"]},
        {id: 2, title: "Sample Book 2", author: "Author 2", tags: []},
        {id: 3, title: "Sample Book 3", author: "Author 3", tags: ["science", "non-fiction"]}
    ];

    let html = "";
    sampleBooks.forEach(book => {
        const hasMetadata = book.tags.length > 0;
        const statusClass = hasMetadata ? "text-success" : "text-warning";
        const statusText = hasMetadata ? "Has metadata" : "Missing metadata";

        html += `
            <div class="book-item" data-title="${book.title.toLowerCase()}" data-author="${book.author.toLowerCase()}">
                <label class="checkbox-inline">
                    <input type="checkbox" value="${book.id}" onchange="updateSelection()">
                    <strong>${book.title}</strong> by ${book.author}
                    <small class="${statusClass}">(${statusText})</small>
                </label>
            </div>
        `;
    });

    document.getElementById("book-list").innerHTML = html;
}

function filterBooks() {
    const searchTerm = document.getElementById("book-search").value.toLowerCase();
    const bookItems = document.querySelectorAll(".book-item");

    bookItems.forEach(item => {
        const title = item.dataset.title;
        const author = item.dataset.author;

        if (title.includes(searchTerm) || author.includes(searchTerm)) {
            item.style.display = "block";
        } else {
            item.style.display = "none";
        }
    });
}

function selectAll() {
    const checkboxes = document.querySelectorAll("#book-list input[type=checkbox]");
    checkboxes.forEach(cb => {
        if (cb.closest(".book-item").style.display !== "none") {
            cb.checked = true;
        }
    });
    updateSelection();
}

function selectNone() {
    const checkboxes = document.querySelectorAll("#book-list input[type=checkbox]");
    checkboxes.forEach(cb => cb.checked = false);
    updateSelection();
}

function selectMissingMetadata() {
    const checkboxes = document.querySelectorAll("#book-list input[type=checkbox]");
    checkboxes.forEach(cb => {
        const item = cb.closest(".book-item");
        const hasMissingMetadata = item.querySelector(".text-warning");
        cb.checked = hasMissingMetadata && item.style.display !== "none";
    });
    updateSelection();
}

function updateSelection() {
    const checkboxes = document.querySelectorAll("#book-list input[type=checkbox]:checked");
    selectedBooks = Array.from(checkboxes).map(cb => parseInt(cb.value));

    const count = selectedBooks.length;
    document.getElementById("selected-count").textContent =
        count === 0 ? "No books selected" : `${count} book(s) selected`;
}

function startBulkFetch() {
    if (selectedBooks.length === 0) {
        alert("Please select at least one book");
        return;
    }

    if (!confirm(`Start bulk metadata fetch for ${selectedBooks.length} books? This may take several minutes.`)) {
        return;
    }

    // Show progress section
    document.getElementById("progress-section").style.display = "block";
    document.getElementById("bulk-fetch-btn").disabled = true;

    // Start the bulk fetch process
    fetch("/api/metadata/bulk", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ book_ids: selectedBooks })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayBulkResults(data.results);
        } else {
            alert("Error: " + data.error);
        }
        document.getElementById("bulk-fetch-btn").disabled = false;
    })
    .catch(error => {
        console.error("Error:", error);
        alert("Bulk fetch failed");
        document.getElementById("bulk-fetch-btn").disabled = false;
    });
}

function displayBulkResults(results) {
    const total = results.length;
    const successful = results.filter(r => r.success).length;
    const failed = total - successful;

    document.getElementById("progress-bar").style.width = "100%";
    document.getElementById("progress-text").textContent = "Complete";
    document.getElementById("current-book").textContent = "Bulk fetch completed!";

    let summaryHtml = `
        <div class="alert alert-info">
            <strong>Summary:</strong> ${successful} successful, ${failed} failed out of ${total} books
        </div>
    `;

    if (failed > 0) {
        summaryHtml += "<h5>Failed Updates:</h5><ul>";
        results.filter(r => !r.success).forEach(result => {
            summaryHtml += `<li>${result.book_title || result.book_id}: ${result.error}</li>`;
        });
        summaryHtml += "</ul>";
    }

    document.getElementById("results-summary").innerHTML = summaryHtml;
}
</script>
{% endblock %}
EOF'

print_status "Bulk metadata template created"
