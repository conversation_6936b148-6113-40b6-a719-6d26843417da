#!/bin/bash

# Create Full-Text Search Service
echo "🔧 Creating Full-Text Search Service"
echo "==================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Install additional packages for text extraction
print_info "Installing text extraction dependencies..."
sudo docker exec $CONTAINER_NAME pip install PyPDF2 python-docx ebooklib

# Create full-text search service
print_info "Creating full-text search service..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/fulltext_search.py << '"'"'EOF'"'"'
"""
Full-Text Search Service for Calibre-Web
Searches inside book content (PDF, EPUB, TXT, etc.)
"""

import os
import re
import logging
from pathlib import Path
from . import logger

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    import ebooklib
    from ebooklib import epub
    EPUB_AVAILABLE = True
except ImportError:
    EPUB_AVAILABLE = False

class FullTextSearcher:
    def __init__(self):
        self.supported_formats = []
        if PDF_AVAILABLE:
            self.supported_formats.extend(['.pdf'])
        if DOCX_AVAILABLE:
            self.supported_formats.extend(['.docx'])
        if EPUB_AVAILABLE:
            self.supported_formats.extend(['.epub'])
        self.supported_formats.extend(['.txt', '.md', '.html', '.htm'])
    
    def extract_text_from_file(self, file_path):
        """Extract text content from various file formats"""
        try:
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.pdf' and PDF_AVAILABLE:
                return self._extract_pdf_text(file_path)
            elif file_ext == '.epub' and EPUB_AVAILABLE:
                return self._extract_epub_text(file_path)
            elif file_ext == '.docx' and DOCX_AVAILABLE:
                return self._extract_docx_text(file_path)
            elif file_ext in ['.txt', '.md', '.html', '.htm']:
                return self._extract_plain_text(file_path)
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return None
    
    def _extract_pdf_text(self, file_path):
        """Extract text from PDF files"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num in range(min(len(pdf_reader.pages), 50)):  # Limit to first 50 pages
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
            return text
        except Exception as e:
            logger.error(f"PDF extraction error: {e}")
            return None
    
    def _extract_epub_text(self, file_path):
        """Extract text from EPUB files"""
        try:
            book = epub.read_epub(file_path)
            text = ""
            
            for item in book.get_items():
                if item.get_type() == ebooklib.ITEM_DOCUMENT:
                    content = item.get_content().decode('utf-8', errors='ignore')
                    # Remove HTML tags
                    clean_text = re.sub(r'<[^>]+>', '', content)
                    text += clean_text + "\n"
                    
                    # Limit text length to prevent memory issues
                    if len(text) > 100000:  # 100KB limit
                        break
            
            return text
        except Exception as e:
            logger.error(f"EPUB extraction error: {e}")
            return None
    
    def _extract_docx_text(self, file_path):
        """Extract text from DOCX files"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            logger.error(f"DOCX extraction error: {e}")
            return None
    
    def _extract_plain_text(self, file_path):
        """Extract text from plain text files"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                return file.read()
        except Exception as e:
            logger.error(f"Plain text extraction error: {e}")
            return None
    
    def search_in_text(self, text, query, case_sensitive=False):
        """Search for query in text content"""
        if not text or not query:
            return []
        
        flags = 0 if case_sensitive else re.IGNORECASE
        
        # Split query into words
        query_words = query.split()
        matches = []
        
        # Search for each word
        for word in query_words:
            pattern = re.escape(word)
            for match in re.finditer(pattern, text, flags):
                start = max(0, match.start() - 100)
                end = min(len(text), match.end() + 100)
                context = text[start:end].strip()
                
                matches.append({
                    'word': word,
                    'position': match.start(),
                    'context': context,
                    'match_start': match.start() - start,
                    'match_end': match.end() - start
                })
        
        return matches
    
    def search_book_content(self, book_path, query, case_sensitive=False):
        """Search for query in book content"""
        try:
            if not os.path.exists(book_path):
                return None
            
            # Extract text from the book file
            text_content = self.extract_text_from_file(book_path)
            
            if not text_content:
                return None
            
            # Search in the extracted text
            matches = self.search_in_text(text_content, query, case_sensitive)
            
            return {
                'file_path': book_path,
                'matches': matches,
                'total_matches': len(matches),
                'text_length': len(text_content),
                'supported_format': True
            }
            
        except Exception as e:
            logger.error(f"Error searching book content: {e}")
            return None
    
    def get_supported_formats(self):
        """Get list of supported file formats"""
        return self.supported_formats

# Global instance
fulltext_searcher = FullTextSearcher()
EOF'

print_status "Full-text search service created"

# Add full-text search routes
print_info "Adding full-text search routes..."
sudo docker exec $CONTAINER_NAME bash -c 'cat >> /app/calibre-web/cps/web.py << '"'"'EOF'"'"'

# Full-Text Search Routes
@app.route("/search/fulltext")
@login_required_if_no_ano
def fulltext_search_page():
    """Full-text search page"""
    return render_template("fulltext_search.html", title="Full-Text Search")

@app.route("/api/search/fulltext", methods=["POST"])
@login_required_if_no_ano
def api_fulltext_search():
    """API endpoint for full-text search"""
    try:
        from .fulltext_search import fulltext_searcher
        from .db import CalibreDB
        
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400
        
        query = data.get("query", "").strip()
        case_sensitive = data.get("case_sensitive", False)
        max_results = data.get("max_results", 20)
        
        if not query:
            return jsonify({"success": False, "error": "Search query required"}), 400
        
        calibre_db = CalibreDB()
        results = []
        
        # Get all books from database
        books = calibre_db.session.query(db.Books).limit(100).all()  # Limit for performance
        
        for book in books:
            try:
                # Get book file path
                book_path = None
                if book.data:
                    for data_item in book.data:
                        file_path = os.path.join("/books", str(book.path), f"{data_item.name}.{data_item.format.lower()}")
                        if os.path.exists(file_path):
                            book_path = file_path
                            break
                
                if not book_path:
                    continue
                
                # Search in book content
                search_result = fulltext_searcher.search_book_content(book_path, query, case_sensitive)
                
                if search_result and search_result["total_matches"] > 0:
                    # Get book metadata
                    authors = ", ".join([author.name for author in book.authors]) if book.authors else "Unknown"
                    
                    results.append({
                        "book_id": book.id,
                        "title": book.title,
                        "authors": authors,
                        "matches": search_result["matches"][:5],  # Limit matches per book
                        "total_matches": search_result["total_matches"],
                        "file_format": os.path.splitext(book_path)[1]
                    })
                
                # Limit total results
                if len(results) >= max_results:
                    break
                    
            except Exception as e:
                logger.error(f"Error searching book {book.id}: {e}")
                continue
        
        return jsonify({
            "success": True,
            "results": results,
            "total_books_searched": len(books),
            "query": query,
            "supported_formats": fulltext_searcher.get_supported_formats()
        })
        
    except Exception as e:
        log.error(f"Full-text search API error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
EOF'

print_status "Full-text search routes added"
