#!/bin/bash

# Add Enhanced Search Route Directly
echo "🔧 Adding Enhanced Search Route Directly"
echo "========================================"

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Add enhanced search routes directly to web.py
print_info "Adding enhanced search routes to web.py..."
sudo docker exec $CONTAINER_NAME bash -c 'cat >> /app/calibre-web/cps/web.py << '"'"'EOF'"'"'

# Enhanced Search Routes
@app.route("/search/enhanced")
@login_required_if_no_ano
def enhanced_search_page():
    """Enhanced search page"""
    return render_template("search_enhanced.html", title="Enhanced Search")

@app.route("/api/search/enhanced", methods=["POST"])
@login_required_if_no_ano
def api_enhanced_search():
    """Enhanced search API"""
    try:
        search_data = request.get_json()
        if not search_data:
            return jsonify({"error": "No search data provided"}), 400
        
        query = search_data.get("query", "")
        field = search_data.get("field", "all")
        
        if not query.strip():
            return jsonify({"books": [], "total": 0, "query": query})
        
        # Use Calibre-Web internal search
        calibre_db = CalibreDB()
        
        # Simple search implementation using existing database models
        try:
            results = []
            
            # Search in books table
            if field == "all" or field == "title":
                books_query = calibre_db.session.query(db.Books).filter(
                    db.Books.title.contains(query)
                )
            elif field == "author":
                books_query = calibre_db.session.query(db.Books).join(db.books_authors_link).join(db.Authors).filter(
                    db.Authors.name.contains(query)
                )
            elif field == "tags":
                books_query = calibre_db.session.query(db.Books).join(db.books_tags_link).join(db.Tags).filter(
                    db.Tags.name.contains(query)
                )
            elif field == "series":
                books_query = calibre_db.session.query(db.Books).join(db.books_series_link).join(db.Series).filter(
                    db.Series.name.contains(query)
                )
            elif field == "publisher":
                books_query = calibre_db.session.query(db.Books).join(db.books_publishers_link).join(db.Publishers).filter(
                    db.Publishers.name.contains(query)
                )
            else:
                # Default to title search
                books_query = calibre_db.session.query(db.Books).filter(
                    db.Books.title.contains(query)
                )
            
            books = books_query.limit(50).all()
            
            for book in books:
                try:
                    authors = ", ".join([author.name for author in book.authors]) if book.authors else "Unknown"
                    tags = ", ".join([tag.name for tag in book.tags]) if book.tags else ""
                    series = book.series[0].name if book.series else ""
                    publisher = book.publishers[0].name if book.publishers else ""
                    description = book.comments[0].text if book.comments else ""
                    
                    results.append({
                        "id": book.id,
                        "title": book.title or "Unknown Title",
                        "author": authors,
                        "series": series,
                        "tags": tags,
                        "publisher": publisher,
                        "description": description[:200] + "..." if len(description) > 200 else description
                    })
                except Exception as e:
                    log.error(f"Error processing book {book.id}: {e}")
                    continue
            
            return jsonify({
                "books": results,
                "total": len(results),
                "query": query,
                "field": field,
                "elasticsearch_used": False
            })
            
        except Exception as e:
            log.error(f"Database search error: {e}")
            return jsonify({"error": f"Search failed: {str(e)}"}), 500
            
    except Exception as e:
        log.error(f"Enhanced search error: {e}")
        return jsonify({"error": f"Search failed: {str(e)}"}), 500
EOF'

print_status "Enhanced search routes added to web.py"

# Restart the container to apply changes
print_info "Restarting Calibre-Web to apply changes..."
sudo docker restart $CONTAINER_NAME

print_info "Waiting for Calibre-Web to restart..."
sleep 25

# Test the enhanced search
print_info "Testing enhanced search..."
sleep 5

# Check if the route is working
if curl -s "http://localhost:8083/search/enhanced" | grep -q "Enhanced Search\|Advanced Search"; then
    print_status "Enhanced search page is working!"
else
    print_warning "Enhanced search page may need more time to load"
fi

echo ""
echo "🎉 Enhanced Search Route Added!"
echo "=============================="
echo ""
print_info "Access the enhanced search at:"
echo "  🔍 http://*************:8083/search/enhanced"
echo ""
print_status "Enhanced search should now be fully functional! 🚀"
