version: '3.8'

services:
  calibre-web:
    image: lscr.io/linuxserver/calibre-web:latest
    container_name: calibre-web1
    restart: unless-stopped

    environment:
      # User/Group Configuration
      - PUID=1000
      - PGID=1000
      - TZ=Etc/UTC

      # LinuxServer Mods (optional)
      - DOCKER_MODS=linuxserver/mods:universal-calibre

      # OAuth Configuration (optional)
      - OAUTHLIB_RELAX_TOKEN_SCOPE=1

      # Enhanced Search Configuration
      - ELASTICSEARCH_HOST=localhost
      - ELASTICSEARCH_PORT=9200
      - ELASTICSEARCH_ENABLED=true
    volumes:
      # Your specific paths - CHANGE THESE TO YOUR ACTUAL PATHS
      - ./config:/config
      - ./books:/books
      - ./cache:/cache
    depends_on:
      - elasticsearch
    ports:
      - "5595:8083"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8083/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: calibre-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      # Host volume for Elasticsearch data - CHANGE THIS PATH TO YOUR PREFERENCE
      - ./elasticsearch_data:/usr/share/elasticsearch/data
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
