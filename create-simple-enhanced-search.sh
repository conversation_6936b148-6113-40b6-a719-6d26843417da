#!/bin/bash

# Create Simple Enhanced Search
echo "🔧 Creating Simple Enhanced Search"
echo "================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Create a simple enhanced search HTML page that works with existing search
print_info "Creating enhanced search HTML page..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/templates/enhanced_search.html << '"'"'EOF'"'"'
{% extends "layout.html" %}
{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-3 col-lg-2 sidebar">
            {% include 'sidebar.html' %}
        </div>
        <div class="col-sm-9 col-lg-10 main">
            <h1>{{_("Enhanced Search")}}</h1>
            
            <div class="row">
                <div class="col-md-8">
                    <form id="enhanced-search-form" action="/search" method="GET">
                        <div class="form-group">
                            <label for="query">{{_("Search Query")}}</label>
                            <input type="text" class="form-control" id="query" name="query" 
                                   placeholder="Enter your search terms..." required>
                        </div>
                        
                        <div class="form-group">
                            <label for="include_tag">{{_("Include Tags")}}</label>
                            <input type="text" class="form-control" id="include_tag" name="include_tag" 
                                   placeholder="Tags to include (comma separated)">
                        </div>
                        
                        <div class="form-group">
                            <label for="exclude_tag">{{_("Exclude Tags")}}</label>
                            <input type="text" class="form-control" id="exclude_tag" name="exclude_tag" 
                                   placeholder="Tags to exclude (comma separated)">
                        </div>
                        
                        <div class="form-group">
                            <label for="include_serie">{{_("Series")}}</label>
                            <input type="text" class="form-control" id="include_serie" name="include_serie" 
                                   placeholder="Series name">
                        </div>
                        
                        <div class="form-group">
                            <label for="include_language">{{_("Language")}}</label>
                            <select class="form-control" id="include_language" name="include_language">
                                <option value="">{{_("Any Language")}}</option>
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="it">Italian</option>
                                <option value="pt">Portuguese</option>
                                <option value="ru">Russian</option>
                                <option value="ja">Japanese</option>
                                <option value="zh">Chinese</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="book_title">{{_("Book Title")}}</label>
                            <input type="text" class="form-control" id="book_title" name="book_title" 
                                   placeholder="Specific book title">
                        </div>
                        
                        <div class="form-group">
                            <label for="author">{{_("Author")}}</label>
                            <input type="text" class="form-control" id="author" name="author" 
                                   placeholder="Author name">
                        </div>
                        
                        <div class="form-group">
                            <label for="publisher">{{_("Publisher")}}</label>
                            <input type="text" class="form-control" id="publisher" name="publisher" 
                                   placeholder="Publisher name">
                        </div>
                        
                        <button type="submit" class="btn btn-primary">{{_("Enhanced Search")}}</button>
                        <button type="button" class="btn btn-secondary" id="clear-form">{{_("Clear")}}</button>
                        <a href="/search" class="btn btn-info">{{_("Basic Search")}}</a>
                    </form>
                </div>
                
                <div class="col-md-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{_("Search Tips")}}</h4>
                        </div>
                        <div class="panel-body">
                            <ul>
                                <li>{{_("Use multiple fields for more precise results")}}</li>
                                <li>{{_("Tags: separate multiple tags with commas")}}</li>
                                <li>{{_("Leave fields empty to ignore them")}}</li>
                                <li>{{_("Search is case-insensitive")}}</li>
                                <li>{{_("Partial matches are supported")}}</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4>{{_("Quick Searches")}}</h4>
                        </div>
                        <div class="panel-body">
                            <p><a href="/search?query=*" class="btn btn-sm btn-default">{{_("All Books")}}</a></p>
                            <p><a href="/search?include_tag=fiction" class="btn btn-sm btn-default">{{_("Fiction")}}</a></p>
                            <p><a href="/search?include_tag=non-fiction" class="btn btn-sm btn-default">{{_("Non-Fiction")}}</a></p>
                            <p><a href="/search?include_tag=science" class="btn btn-sm btn-default">{{_("Science")}}</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById("clear-form").addEventListener("click", function() {
    document.getElementById("enhanced-search-form").reset();
});

// Auto-submit when Enter is pressed in any field
document.querySelectorAll("input").forEach(function(input) {
    input.addEventListener("keypress", function(e) {
        if (e.key === "Enter") {
            document.getElementById("enhanced-search-form").submit();
        }
    });
});
</script>
{% endblock %}
EOF'

print_status "Enhanced search HTML page created"

# Add a route for the enhanced search page
print_info "Adding enhanced search route..."
sudo docker exec $CONTAINER_NAME bash -c 'cat >> /app/calibre-web/cps/web.py << '"'"'EOF'"'"'

# Enhanced Search Page Route
@app.route("/enhanced-search")
@login_required_if_no_ano
def enhanced_search_page():
    """Enhanced search page with advanced filters"""
    return render_template("enhanced_search.html", title="Enhanced Search")
EOF'

# Add navigation link to the enhanced search
print_info "Adding navigation link..."
sudo docker exec $CONTAINER_NAME bash -c 'cp /app/calibre-web/cps/templates/layout.html /app/calibre-web/cps/templates/layout.html.backup'

# Add enhanced search link to navigation
sudo docker exec $CONTAINER_NAME bash -c 'sed -i "s|<li><a href=\"{{url_for('\''web.search'\'')}}\">{{_(\"Search\")}}</a></li>|<li><a href=\"{{url_for('\''web.search'\'')}}\">{{_(\"Search\")}}</a></li>\n                    <li><a href=\"/enhanced-search\">{{_(\"Enhanced Search\")}}</a></li>|g" /app/calibre-web/cps/templates/layout.html 2>/dev/null || true'

# Restart the container
print_info "Restarting Calibre-Web..."
sudo docker restart $CONTAINER_NAME

print_info "Waiting for restart..."
sleep 25

# Test the enhanced search page
print_info "Testing enhanced search page..."
if curl -s "http://localhost:8083/enhanced-search" | grep -q "Enhanced Search"; then
    print_status "Enhanced search page is working!"
else
    print_warning "Enhanced search page may need more time"
fi

echo ""
echo "🎉 Simple Enhanced Search Created!"
echo "================================="
echo ""
print_info "Access the enhanced search at:"
echo "  🔍 http://*************:8083/enhanced-search"
echo ""
print_info "Features:"
echo "  ✅ Advanced search form with multiple fields"
echo "  ✅ Tag inclusion/exclusion"
echo "  ✅ Series, language, author, publisher filters"
echo "  ✅ Quick search buttons"
echo "  ✅ Search tips and help"
echo ""
print_status "Enhanced search is now available in the navigation menu! 🚀"
