# Enhanced Calibre-Web Deployment

This package contains an enhanced version of Calibre-Web with advanced search capabilities powered by Elasticsearch.

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 2GB RAM available
- 10GB+ disk space for books and data

### One-Command Deployment
```bash
chmod +x deploy-enhanced-calibre.sh
./deploy-enhanced-calibre.sh
```

## 📁 Package Contents

### Essential Files
- `docker-compose.production.yml` - Production Docker Compose configuration
- `deploy-enhanced-calibre.sh` - Automated deployment script
- `main_patch.py` - Script to integrate enhanced search features
- `cps/` - Enhanced Calibre-Web source code with search features
- `README_DEPLOYMENT.md` - This deployment guide

### Enhanced Features
- **Advanced Search Interface** - Multi-field search with operators
- **Elasticsearch Integration** - Lightning-fast full-text search
- **Smart Filtering** - By tags, series, publishers, languages, formats
- **Range Filters** - Rating and date ranges
- **Modern UI** - Responsive design with AJAX
- **Fallback Support** - Works with or without Elasticsearch

## 🔧 Manual Deployment

### Step 1: Configure Data Directory
Edit `docker-compose.production.yml` and change the volume paths:
```yaml
volumes:
  # Change these paths to your desired locations
  - /your/path/to/calibre-data/config:/config
  - /your/path/to/calibre-data/books:/books
  - /your/path/to/calibre-data/cache:/cache
```

### Step 2: Create Data Directories
```bash
sudo mkdir -p /your/path/to/calibre-data/{config,books,cache,elasticsearch}
sudo chown -R $USER:$USER /your/path/to/calibre-data
```

### Step 3: Deploy Services
```bash
# Copy production config
cp docker-compose.production.yml docker-compose.yml

# Start Elasticsearch first
docker-compose up -d elasticsearch

# Wait for Elasticsearch to be ready
sleep 30

# Start Calibre-Web
docker-compose up -d calibre-web
```

### Step 4: Install Enhanced Features
```bash
# Copy enhanced search files
docker cp cps/search_enhanced.py calibre-web-enhanced:/app/calibre-web/cps/
docker cp cps/templates/search_enhanced.html calibre-web-enhanced:/app/calibre-web/cps/templates/
docker cp cps/elasticsearch_service.py calibre-web-enhanced:/app/calibre-web/cps/
docker cp cps/templates/layout.html calibre-web-enhanced:/app/calibre-web/cps/templates/

# Install Elasticsearch client
docker exec calibre-web-enhanced pip install "elasticsearch>=8.0.0,<9.0.0"

# Apply integration patch
docker cp main_patch.py calibre-web-enhanced:/app/
docker exec calibre-web-enhanced python /app/main_patch.py

# Restart to apply changes
docker-compose restart calibre-web
```

## 🌐 Access Your Enhanced Calibre-Web

- **Main Interface**: http://localhost:5595
- **Enhanced Search**: http://localhost:5595/search/advanced
- **Elasticsearch**: http://localhost:9200

### Network Access
To access from other devices on your network, the service is bound to all interfaces (0.0.0.0:5595).
Access via: `http://YOUR_SERVER_IP:5595`

## 📚 Initial Setup

1. **First Time Setup**: Navigate to http://localhost:5595
2. **Database Configuration**: Set library path to `/books`
3. **Create Admin User**: Follow the setup wizard
4. **Add Books**: Upload or import your book collection
5. **Test Enhanced Search**: Try the advanced search features

## 🔍 Enhanced Search Features

### Advanced Search Interface
- Multi-field search with AND/OR operators
- Fuzzy matching and phrase search
- Field-specific searches (title, author, tags, etc.)

### Smart Filters
- **Tags**: Multi-select tag filtering
- **Series**: Filter by book series
- **Publishers**: Filter by publisher
- **Languages**: Filter by language
- **Formats**: Filter by file format (PDF, EPUB, etc.)
- **Ratings**: Range slider for ratings
- **Publication Date**: Date range picker

### Modern UI Features
- Responsive design for mobile and desktop
- AJAX-powered search with real-time results
- Autocomplete suggestions
- Advanced pagination
- Export search results

## 🛠️ Troubleshooting

### Services Not Starting
```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs calibre-web
docker-compose logs elasticsearch
```

### Permission Issues
```bash
# Fix data directory permissions
sudo chown -R $USER:$USER /your/path/to/calibre-data
chmod -R 755 /your/path/to/calibre-data
```

### Enhanced Search Not Available
```bash
# Verify enhanced files are installed
docker exec calibre-web-enhanced ls -la /app/calibre-web/cps/search_enhanced.py

# Check if Elasticsearch client is installed
docker exec calibre-web-enhanced pip list | grep elasticsearch

# Re-apply the patch
docker exec calibre-web-enhanced python /app/main_patch.py
docker-compose restart calibre-web
```

## 📊 System Requirements

### Minimum
- 2GB RAM
- 2 CPU cores
- 10GB disk space

### Recommended
- 4GB+ RAM
- 4+ CPU cores
- 50GB+ disk space
- SSD storage for better performance

## 🔒 Security Notes

- Change default passwords after setup
- Consider using reverse proxy (nginx) for HTTPS
- Regularly backup your data directory
- Keep Docker images updated

## 📦 Backup and Migration

### Backup
```bash
# Backup entire data directory
tar -czf calibre-backup-$(date +%Y%m%d).tar.gz /your/path/to/calibre-data
```

### Migration to Another PC
1. Copy this entire deployment package
2. Copy your data directory backup
3. Run the deployment script
4. Restore your data directory

## 🆘 Support

For issues with:
- **Enhanced Features**: Check the enhanced search documentation
- **Calibre-Web**: Visit the official Calibre-Web documentation
- **Elasticsearch**: Check Elasticsearch logs and documentation

## 📄 License

This enhanced version maintains the same license as the original Calibre-Web project.
