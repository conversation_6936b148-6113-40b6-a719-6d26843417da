#!/bin/bash

# Enhanced Calibre-Web Cleanup Script
# This script helps clean up the deployment and manage containers

set -e

echo "🧹 Enhanced Calibre-Web Cleanup Script"
echo "======================================"

# Configuration
CONTAINER_NAME="calibre-web1"
ELASTICSEARCH_CONTAINER="calibre-elasticsearch"
KIBANA_CONTAINER="calibre-kibana"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

print_question() {
    echo -e "\033[1;35m❓ $1\033[0m"
}

# Function to show menu
show_menu() {
    echo ""
    echo "🧹 Cleanup Options:"
    echo "=================="
    echo "1. 🛑 Stop all containers"
    echo "2. 🗑️  Remove all containers"
    echo "3. 🧽 Clean Docker images"
    echo "4. 📁 Clean temporary files"
    echo "5. 🔄 Reset deployment (stop + remove containers)"
    echo "6. 💾 Backup data before cleanup"
    echo "7. 📊 Show system status"
    echo "8. 🗂️  Archive old files"
    echo "9. 🚀 Full cleanup and restart"
    echo "0. ❌ Exit"
    echo ""
}

# Function to stop containers
stop_containers() {
    print_info "Stopping containers..."
    
    if docker ps | grep -q $CONTAINER_NAME; then
        docker stop $CONTAINER_NAME
        print_status "Stopped Calibre-Web container"
    else
        print_info "Calibre-Web container not running"
    fi
    
    if docker ps | grep -q $ELASTICSEARCH_CONTAINER; then
        docker stop $ELASTICSEARCH_CONTAINER
        print_status "Stopped Elasticsearch container"
    else
        print_info "Elasticsearch container not running"
    fi
    
    if docker ps | grep -q $KIBANA_CONTAINER; then
        docker stop $KIBANA_CONTAINER
        print_status "Stopped Kibana container"
    else
        print_info "Kibana container not running"
    fi
}

# Function to remove containers
remove_containers() {
    print_info "Removing containers..."
    
    # Stop first
    stop_containers
    
    # Remove containers
    docker rm -f $CONTAINER_NAME 2>/dev/null && print_status "Removed Calibre-Web container" || print_info "Calibre-Web container not found"
    docker rm -f $ELASTICSEARCH_CONTAINER 2>/dev/null && print_status "Removed Elasticsearch container" || print_info "Elasticsearch container not found"
    docker rm -f $KIBANA_CONTAINER 2>/dev/null && print_status "Removed Kibana container" || print_info "Kibana container not found"
}

# Function to clean Docker images
clean_docker_images() {
    print_info "Cleaning Docker images..."
    
    # Remove unused images
    docker image prune -f
    print_status "Removed unused Docker images"
    
    # Optionally remove specific images
    read -p "Remove Calibre-Web images? (y/N): " remove_calibre
    if [[ $remove_calibre =~ ^[Yy]$ ]]; then
        docker rmi lscr.io/linuxserver/calibre-web:latest 2>/dev/null && print_status "Removed Calibre-Web image" || print_info "Calibre-Web image not found"
        docker rmi calibre-web-enhanced:latest 2>/dev/null && print_status "Removed enhanced Calibre-Web image" || print_info "Enhanced image not found"
        docker rmi calibre-web-simple:latest 2>/dev/null && print_status "Removed simple Calibre-Web image" || print_info "Simple image not found"
    fi
    
    read -p "Remove Elasticsearch images? (y/N): " remove_elastic
    if [[ $remove_elastic =~ ^[Yy]$ ]]; then
        docker rmi docker.elastic.co/elasticsearch/elasticsearch:8.11.0 2>/dev/null && print_status "Removed Elasticsearch image" || print_info "Elasticsearch image not found"
        docker rmi docker.elastic.co/kibana/kibana:8.11.0 2>/dev/null && print_status "Removed Kibana image" || print_info "Kibana image not found"
    fi
}

# Function to clean temporary files
clean_temp_files() {
    print_info "Cleaning temporary files..."
    
    # Clean deployment packages
    rm -f /tmp/enhanced-calibre-web-*.tar.gz && print_status "Removed deployment packages" || print_info "No deployment packages found"
    
    # Clean local cache and temporary directories
    rm -rf cache/* 2>/dev/null && print_status "Cleaned cache directory" || print_info "Cache directory already clean"
    
    # Clean Docker build cache
    docker builder prune -f && print_status "Cleaned Docker build cache"
    
    # Clean system temporary files related to our deployment
    rm -f /tmp/calibre-* 2>/dev/null && print_status "Cleaned system temp files" || print_info "No temp files to clean"
}

# Function to backup data
backup_data() {
    print_info "Creating backup..."
    
    BACKUP_DIR="/tmp/calibre-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup configuration
    if [ -d "config" ]; then
        cp -r config "$BACKUP_DIR/"
        print_status "Backed up config directory"
    fi
    
    # Backup books (if not too large)
    if [ -d "books" ]; then
        BOOKS_SIZE=$(du -sm books 2>/dev/null | cut -f1)
        if [ "$BOOKS_SIZE" -lt 1000 ]; then  # Less than 1GB
            cp -r books "$BACKUP_DIR/"
            print_status "Backed up books directory"
        else
            print_warning "Books directory too large ($BOOKS_SIZE MB), skipping"
            echo "Manual backup recommended: cp -r books $BACKUP_DIR/"
        fi
    fi
    
    # Create archive
    tar -czf "$BACKUP_DIR.tar.gz" -C /tmp "$(basename $BACKUP_DIR)"
    rm -rf "$BACKUP_DIR"
    
    print_status "Backup created: $BACKUP_DIR.tar.gz"
}

# Function to show system status
show_status() {
    print_info "System Status:"
    echo ""
    
    # Container status
    echo "📦 Container Status:"
    if docker ps | grep -q $CONTAINER_NAME; then
        echo "  ✅ Calibre-Web: Running"
    else
        echo "  ❌ Calibre-Web: Stopped"
    fi
    
    if docker ps | grep -q $ELASTICSEARCH_CONTAINER; then
        echo "  ✅ Elasticsearch: Running"
    else
        echo "  ❌ Elasticsearch: Stopped"
    fi
    
    # Service status
    echo ""
    echo "🌐 Service Status:"
    if curl -s http://localhost:5595 >/dev/null 2>&1; then
        echo "  ✅ Calibre-Web: Accessible (http://localhost:5595)"
    else
        echo "  ❌ Calibre-Web: Not accessible"
    fi
    
    if curl -s http://localhost:9200 >/dev/null 2>&1; then
        echo "  ✅ Elasticsearch: Accessible (http://localhost:9200)"
    else
        echo "  ❌ Elasticsearch: Not accessible"
    fi
    
    # Disk usage
    echo ""
    echo "💾 Disk Usage:"
    if [ -d "config" ]; then
        echo "  📁 Config: $(du -sh config 2>/dev/null | cut -f1)"
    fi
    if [ -d "books" ]; then
        echo "  📚 Books: $(du -sh books 2>/dev/null | cut -f1)"
    fi
    if [ -d "cache" ]; then
        echo "  💾 Cache: $(du -sh cache 2>/dev/null | cut -f1)"
    fi
    
    # Docker usage
    echo ""
    echo "🐳 Docker Usage:"
    echo "  📦 Images: $(docker images --format 'table {{.Repository}}\t{{.Tag}}\t{{.Size}}' | grep -E 'calibre|elastic' | wc -l) related images"
    echo "  💾 Total Docker space: $(docker system df --format 'table {{.Type}}\t{{.TotalCount}}\t{{.Size}}' | tail -n +2 | awk '{sum+=$3} END {print sum "B"}')"
}

# Function to archive old files
archive_old_files() {
    print_info "Archiving old files..."
    
    # Create archive directory if it doesn't exist
    mkdir -p archive/cleanup
    
    # Move old log files
    find . -name "*.log*" -mtime +7 -exec mv {} archive/cleanup/ \; 2>/dev/null && print_status "Archived old log files" || print_info "No old log files found"
    
    # Move old backup files
    find . -name "*backup*" -mtime +30 -exec mv {} archive/cleanup/ \; 2>/dev/null && print_status "Archived old backup files" || print_info "No old backup files found"
    
    # Move old deployment packages
    find /tmp -name "enhanced-calibre-web-*.tar.gz" -mtime +7 -exec mv {} archive/cleanup/ \; 2>/dev/null && print_status "Archived old deployment packages" || print_info "No old packages found"
}

# Function for full cleanup and restart
full_cleanup_restart() {
    print_warning "This will stop all containers, clean up, and restart the deployment"
    read -p "Are you sure? (y/N): " confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        print_info "Starting full cleanup and restart..."
        
        # Backup first
        backup_data
        
        # Stop and remove containers
        remove_containers
        
        # Clean temporary files
        clean_temp_files
        
        # Archive old files
        archive_old_files
        
        # Restart deployment
        print_info "Restarting deployment..."
        if [ -f "deploy-enhanced-calibre.sh" ]; then
            ./deploy-enhanced-calibre.sh
        else
            print_error "deploy-enhanced-calibre.sh not found"
        fi
    else
        print_info "Full cleanup cancelled"
    fi
}

# Main menu loop
while true; do
    show_menu
    read -p "Select an option (0-9): " choice
    
    case $choice in
        1)
            stop_containers
            ;;
        2)
            remove_containers
            ;;
        3)
            clean_docker_images
            ;;
        4)
            clean_temp_files
            ;;
        5)
            remove_containers
            ;;
        6)
            backup_data
            ;;
        7)
            show_status
            ;;
        8)
            archive_old_files
            ;;
        9)
            full_cleanup_restart
            ;;
        0)
            print_info "Cleanup script exited"
            exit 0
            ;;
        *)
            print_error "Invalid option. Please select 0-9."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done
