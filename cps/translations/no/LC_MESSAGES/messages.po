# Norwegian translations for Calibre-Web.
# Copyright (C) 2022 Vegard Fladby
# This file is distributed under the same license as the Calibre-Web
# project.
# FIRST AUTHOR Vegard Fladby, 2022.
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/Calibre-Web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2023-01-06 11:00+0000\n"
"Last-Translator: Vegard Fladby <<EMAIL>>\n"
"Language: no\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statistikk"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "Server startet på nytt. Last inn siden på nytt"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "Utfører avslutning av server, vennligst lukk vinduet"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr ""

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Ukjent kommando"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Test e-post i kø for sending til %(email)s, sjekk Oppgaver for resultat"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Ukjent"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Admin side"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Grunnleggende konfigurasjon"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "UI-konfigurasjon"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "Egendefinert kolonnenr.%(column)d finnes ikke i caliber-databasen"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "Rediger brukere"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Alle"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Bruker ikke funnet"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} brukere ble slettet"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Vis alt"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Feil utformet forespørsel"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Gjestenavn kan ikke endres"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "Gjesten kan ikke ha denne rollen"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Ingen administratorbruker igjen, kan ikke fjerne administratorrollen"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Verdien må være sann eller usann"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Ugyldig rolle"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "Gjestene kan ikke ha denne utsikten"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "Ugyldig visning"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Gjestenes lokalitet bestemmes automatisk og kan ikke angis"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Ingen gyldig lokalitet gitt"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Ikke oppgitt gyldig bokspråk"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Parameter ikke funnet"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "Ugyldig lesekolonne"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "Ugyldig begrenset kolonne"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Calibre-Web-konfigurasjonen er oppdatert"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Vil du virkelig slette Kobo-tokenet?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Vil du virkelig slette dette domenet?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Vil du virkelig slette denne brukeren?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Er du sikker på at du vil slette denne hyllen?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Er du sikker på at du vil endre lokaliteter for valgte bruker(e)?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Er du sikker på at du vil endre synlige bokspråk for valgte bruker(e)?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Er du sikker på at du vil endre den valgte rollen for den(e) valgte brukeren?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Er du sikker på at du vil endre de valgte begrensningene for den(e) valgte brukeren?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Er du sikker på at du vil endre de valgte synlighetsbegrensningene for valgte bruker(e)?"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Er du sikker på at du vil endre atferden for hyllesynkronisering for de(n) valgte brukeren(e)?"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Er du sikker på at du vil endre plassering av Caliber-biblioteket?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-Web vil søke etter oppdaterte omslag og oppdatere omslagsminiatyrbilder, kan dette ta litt tid?"

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Er du sikker på at du vil slette Calibre-Webs synkroniseringsdatabase for å tvinge frem en full synkronisering med Kobo Reader?"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Benekte"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Tillate"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{} synkroniseringsoppføringer slettet"

#: cps/admin.py:987
msgid "Tag not found"
msgstr "Merket ble ikke funnet"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Ugyldig handling"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json er ikke konfigurert for webapplikasjon"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Loggfilplasseringen er ikke gyldig, skriv inn riktig bane"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Plasseringen av tilgangsloggfilen er ikke gyldig, skriv inn riktig bane"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Angi en LDAP-leverandør, port, DN og brukerobjektidentifikator"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Vennligst skriv inn en LDAP-tjenestekonto og passord"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "Angi en LDAP-tjenestekonto"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAP-gruppeobjektfilter må ha én \"%s\"-formatidentifikator"

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAP-gruppeobjektfilter har uovertruffen parentes"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP-brukerobjektfilter må ha én \"%s\"-formatidentifikator"

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAP-brukerobjektfilter har uovertruffen parentes"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP-medlemsbrukerfilter må ha én \"%s\"-formatidentifikator"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "LDAP-medlemsbrukerfilter har uovertruffen parentes"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAP CA-sertifikat, sertifikat eller nøkkelplassering er ikke gyldig. Angi riktig bane"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Legg til ny bruker"

#: cps/admin.py:1295 cps/templates/admin.html:100
#, fuzzy
msgid "Edit Email Server Settings"
msgstr "Rediger e-postserverinnstillinger"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr ""

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, fuzzy, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Databasefeil: %(error)s."

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "Test e-post i kø for sending til %(email)s, sjekk Oppgaver for resultat"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Det oppsto en feil ved sending av test-e-posten: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Vennligst konfigurer e-postadressen din først..."

#: cps/admin.py:1351
#, fuzzy
msgid "Email Server Settings updated"
msgstr "E-postserverinnstillinger oppdatert"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "Rediger innstillinger for planlagte oppgaver"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "Ugyldig starttidspunkt for spesifisert oppgave"

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "Ugyldig varighet for spesifisert oppgave"

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "Innstillinger for planlagte oppgaver er oppdatert"

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
#, fuzzy
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "En ukjent feil oppstod. Prøv igjen senere."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "Innstillinger DB er ikke skrivbar"

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Rediger bruker %(nick)s"

#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Passord for bruker %(user)s tilbakestilling"

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Vennligst konfigurer SMTP-postinnstillingene først..."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Loggfilviser"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Ber om oppdateringspakke"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Laster ned oppdateringspakken"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Pakker ut oppdateringspakken"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Erstatter filer"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Databaseforbindelser er stengt"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Stopper server"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Oppdatering fullført, vennligst trykk OK og last inn siden på nytt"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Oppdatering mislyktes:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTP-feil"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Tilkoblingsfeil"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Tidsavbrudd under etablering av tilkobling"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Generell feil"

#: cps/admin.py:1551
msgid "Update file could not be saved in temp dir"
msgstr "Oppdateringsfilen kunne ikke lagres i temp dir"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "Filer kunne ikke erstattes under oppdatering"

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "Kunne ikke pakke ut minst én LDAP-bruker"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Kunne ikke opprette minst én LDAP-bruker"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Feil: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Feil: Ingen bruker ble returnert som svar fra LDAP-serveren"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Minst én LDAP-bruker ikke funnet i databasen"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} Bruker ble importert"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "DB-plassering er ikke gyldig, skriv inn riktig bane"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "DB er ikke skrivbar"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "Nøkkelfilplasseringen er ikke gyldig. Angi riktig bane"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "Sertifikatfilplasseringen er ikke gyldig, vennligst skriv inn riktig bane"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr ""

#: cps/admin.py:1917
msgid "Database Settings updated"
msgstr "Databaseinnstillinger oppdatert"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "Databasekonfigurasjon"

#: cps/admin.py:1940 cps/web.py:1299
#, fuzzy
msgid "Oops! Please complete all fields."
msgstr "Vennligst fyll ut alle feltene!"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "E-post er ikke fra gyldig domene"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Legg til ny bruker"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "Bruker '%(user)s' opprettet"

#: cps/admin.py:1972
#, fuzzy
msgid "Oops! An account already exists for this Email. or name."
msgstr "Fant en eksisterende konto for denne e-postadressen eller navnet."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "Brukeren '%(nick)s' slettet"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "Kan ikke slette gjestebruker"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Ingen administratorbruker igjen, kan ikke slette bruker"

#: cps/admin.py:2063 cps/web.py:1484
#, fuzzy
msgid "Email can't be empty and has to be a valid Email"
msgstr "E-postadresse kan ikke være tom og må være en gyldig e-post"

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "Bruker '%(nick)s' oppdatert"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Søk"

#: cps/converter.py:31
msgid "not installed"
msgstr "ikke installert"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Utførelsestillatelser mangler"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Ingen"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Fil %(file)s lastet opp"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Kilde- eller målformat for konvertering mangler"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Boken ble satt i kø for konvertering til %(book_format)s"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Det oppsto en feil ved konvertering av denne boken: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
#, fuzzy
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Oops! Den valgte boktittelen er utilgjengelig. Filen eksisterer ikke eller er ikke tilgjengelig"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "Brukeren har ingen rettigheter til å laste opp cover"

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "Identifikatorer skiller ikke mellom store og små bokstaver, overskriver gammel identifikator"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "'%(langname)s' er ikke et gyldig språk"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "Metadata ble oppdatert"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "Feil ved redigering av bok: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Opplastet bok finnes sannsynligvis i biblioteket, vurder å endre før du laster opp ny: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "Filtypen «%(ext)s» er ikke tillatt å lastes opp til denne serveren"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "Filtypen «%(ext)s» er ikke tillatt å lastes opp til denne serveren"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Filen som skal lastes opp må ha en utvidelse"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Filen %(filename)s kunne ikke lagres i midlertidig dir"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Kunne ikke flytte omslagsfil %(file)s: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Bokformatet er slettet"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Boken ble slettet"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "Du mangler tillatelser til å slette bøker"

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "redigere metadata"

#: cps/editbooks.py:1016
#, fuzzy, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "%(seriesindex)s er ikke et gyldig tall, hopper over"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "Brukeren har ingen rettigheter til å laste opp flere filformater"

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Kunne ikke opprette banen %(path)s (Tillatelse nektet)."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Kunne ikke lagre filen %(file)s."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Filformat %(ext)s lagt til %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Google Disk-konfigurasjonen er ikke fullført. Prøv å deaktivere og aktivere Google Disk på nytt"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Tilbakeringingsdomene er ikke bekreftet. Følg fremgangsmåten for å bekrefte domenet i Googles utviklerkonsoll"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "%(format)s format ikke funnet for bok-ID: %(book)d"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "%(format)s ikke funnet på Google Disk: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s ikke funnet: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
#, fuzzy
msgid "Send to eReader"
msgstr "Send til E-Reader"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "Denne e-posten er sendt via Calibre-Web."

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web test e-post"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "Test e-post"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Kom i gang med Calibre-Web"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "Registrerings-e-post for bruker: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "Konverter %(orig)s til %(format)s og send til E-Reader"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "Send %(format)s til E-Reader"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s sendes til E-Reader"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Den forespurte filen kunne ikke leses. Kanskje feil tillatelser?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "Lesestatus kunne ikke angis: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Sletting av bokmappe for bok %(id)s mislyktes, banen har undermapper: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Sletting av bok %(id)s mislyktes: %(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Sletter bok %(id)s kun fra databasen, bokbanen i databasen er ikke gyldig: %(path)s"

#: cps/helper.py:439
#, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Endre navn på forfatter fra: '%(src)s' til '%(dest)s' mislyktes med feil: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Fil %(file)s ikke funnet på Google Disk"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Endre navn på tittel fra: '%(src)s' til '%(dest)s' mislyktes med feil: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Finner ikke bokbane %(path)s på Google Disk"

#: cps/helper.py:657
#, fuzzy
msgid "Found an existing account for this Email address"
msgstr "Fant en eksisterende konto for denne e-postadressen"

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Dette brukernavnet er allerede tatt"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "Ugyldig format for e-postadresse"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr ""

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "Python-modulen 'advocate' er ikke installert, men er nødvendig for omslagsopplastinger"

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Feil ved nedlasting av cover"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Omslagsformatfeil"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "Du har ikke tilgang til localhost eller det lokale nettverket for coveropplastinger"

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Kunne ikke opprette bane for dekning"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Cover-filen er ikke en gyldig bildefil, eller kunne ikke lagres"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Bare jpg/jpeg/png/webp/bmp-filer støttes som coverfile"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "Ugyldig omslagsfilinnhold"

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Bare jpg/jpeg-filer støttes som coverfile"

#: cps/helper.py:989 cps/helper.py:1149
msgid "Cover"
msgstr "Dekke"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "UnRar binær fil ikke funnet"

#: cps/helper.py:1017
msgid "Error executing UnRar"
msgstr "Feil ved kjøring av UnRar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "DB er ikke skrivbar"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Utførelsestillatelser mangler"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "Feil ved kjøring av UnRar"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr "Sett alle bøker i kø for sikkerhetskopiering av metadata"

#: cps/kobo_auth.py:92
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Gå til Calibre-Web fra ikke-lokale vert for å få gyldig api_endpoint for kobo-enhet"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo oppsett"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Registrer deg hos %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, fuzzy, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "du er nå logget på som: '%(nickname)s'"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Link til %(oauth)s lyktes"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Pålogging mislyktes, ingen bruker koblet til OAuth-konto"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Fjern koblingen til %(oauth)s"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Fjern koblingen til %(oauth)s mislyktes"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Ikke koblet til %(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Kunne ikke logge på med GitHub."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Kunne ikke hente brukerinformasjon fra GitHub."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Kunne ikke logge på med Google."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Kunne ikke hente brukerinformasjon fra Google."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub Oauth-feil, prøv igjen senere."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "GitHub Oauth-feil: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google Oauth-feil, prøv igjen senere."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google Oauth-feil: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} Stjerner"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Logg Inn"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Token ikke funnet"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Token har utløpt"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Suksess! Gå tilbake til enheten din"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Bøker"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Vis nyere bøker"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Hot bøker"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Vis populære bøker"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Nedlastede bøker"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Vis nedlastede bøker"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Topprangerte bøker"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Vis best rangerte bøker"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Lese bøker"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "Vis lest og ulest"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Uleste bøker"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Vis ulest"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Oppdage"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Vis tilfeldige bøker"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Kategorier"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "Vis kategorivalg"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Serie"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "Vis serieutvalg"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Forfattere"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "Vis forfattervalg"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Forlag"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "Vis utgivervalg"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Språk"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "Vis språkvalg"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Vurderinger"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "Vis vurderingsvalg"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Filformater"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "Vis valg av filformater"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Arkiverte bøker"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "Vis arkiverte bøker"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Bøker Liste"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Vis bokliste"

#: cps/search.py:201
msgid "Published after "
msgstr "Publisert etter "

#: cps/search.py:208
msgid "Published before "
msgstr "Publisert før "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Vurdering <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Vurdering >= %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "Lesestatus = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "Feil ved søk etter egendefinerte kolonner. Start Calibre-Web på nytt"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Avansert søk"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Ugyldig hylle er angitt"

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Beklager at du ikke har lov til å legge til en bok i den hyllen"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Boken er allerede en del av hyllen: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Boken er lagt til i hyllen: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "Du har ikke lov til å legge en bok i hyllen"

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "Bøker er allerede en del av hyllen: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "Bøker er lagt til i hyllen: %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Kunne ikke legge til bøker i hyllen: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Boken er fjernet fra hyllen: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "Beklager at du ikke har lov til å fjerne en bok fra denne hyllen"

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Lag en hylle"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Beklager at du ikke har lov til å redigere denne hyllen"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Rediger en hylle"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "Feil ved sletting av hylle"

#: cps/shelf.py:239
msgid "Shelf successfully deleted"
msgstr "Hylle slettet"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Endre rekkefølgen på hylle: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "Beklager at du ikke har lov til å opprette en offentlig hylle"

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Hylle %(title)s opprettet"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Hylle %(title)s endret"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "det var en feil"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "En offentlig hylle med navnet '%(title)s' eksisterer allerede."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "En privat hylle med navnet '%(title)s' eksisterer allerede."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Hylle: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Feil ved åpning av hylle. Hylle finnes ikke eller er ikke tilgjengelig"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Oppgaver"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Venter"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Mislyktes"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Startet"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Ferdig"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "Endte"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "avbrutt"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Ukjent status"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Uventede data under lesing av oppdateringsinformasjon"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Ingen oppdatering tilgjengelig. Du har allerede den nyeste versjonen installert"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "En ny oppdatering er tilgjengelig. Klikk på knappen nedenfor for å oppdatere til siste versjon."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "Kunne ikke hente oppdateringsinformasjon"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Klikk på knappen nedenfor for å oppdatere til siste stabile versjon."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "En ny oppdatering er tilgjengelig. Klikk på knappen nedenfor for å oppdatere til versjon: %(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Ingen utgivelsesinformasjon tilgjengelig"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Discover (tilfeldige bøker)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Hot Books (mest nedlastede)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Lastet ned bøker av %(user)s"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Forfatter: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Utgiver: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Serie: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "Vurdering: Ingen"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Rangering: %(rating)s stjerner"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Filformat: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Kategori: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Språk: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "Nedlastinger"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Rangeringsliste"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Liste over filformater"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "Vennligst konfigurer SMTP-postinnstillingene først..."

#: cps/web.py:1265
#, fuzzy, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Boken ble satt i kø for sending til %(eReadermail)s"

#: cps/web.py:1268
#, fuzzy, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Oops! Det oppsto en feil ved sending av denne boken: %(res)s"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Vennligst oppdater profilen din med en gyldig Send til Kindle-e-postadresse."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr ""

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Registrere"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "E-postserveren er ikke konfigurert, kontakt administratoren din!"

#: cps/web.py:1295 cps/web.py:1342
#, fuzzy
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "E-postserveren er ikke konfigurert, kontakt administratoren din!"

#: cps/web.py:1328
#, fuzzy
msgid "Oops! Your Email is not allowed."
msgstr "Din e-post kan ikke registreres"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr ""

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "Kan ikke aktivere LDAP-autentisering"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr ""

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "du er nå logget på som: '%(nickname)s'"

#: cps/web.py:1415
#, fuzzy, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "Reservepålogging som: '%(nickname)s', LDAP-serveren er ikke tilgjengelig, eller brukeren er ukjent"

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "Kunne ikke logge på: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "Vennligst skriv inn gyldig brukernavn for å tilbakestille passordet"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "Nytt passord ble sendt til e-postadressen din"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "En ukjent feil oppstod. Prøv igjen senere."

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "Vennligst skriv inn gyldig brukernavn for å tilbakestille passordet"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "du er nå logget på som: '%(nickname)s'"

#: cps/web.py:1510 cps/web.py:1560
#, fuzzy, python-format
msgid "%(name)s's Profile"
msgstr "%(name)s sin profil"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "Profil oppdatert"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr ""

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Fant ingen gyldig gmail.json-fil med OAuth-informasjon"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s sendes til E-Reader"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Caliber ebook-convert %(tool)s ble ikke funnet"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s format ikke funnet på disken"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "Ebook-konvertering mislyktes med ukjent feil"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify-konvertering mislyktes: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "Konvertert fil ikke funnet eller mer enn én fil i mappen %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Caliber mislyktes med feil: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "Ebook-konvertering mislyktes: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "Konvertere"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Kobler til Caliber-databasen på nytt"

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "E-post"

#: cps/tasks/metadata_backup.py:34
msgid "Backing up Metadata"
msgstr "Sikkerhetskopierer metadata"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "Genererte %(count)s forsideminiatyrbilder"

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "Forsideminiatyrbilder"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "Genererte {0} serieminiatyrbilder"

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "Tømmer cache for miniatyrbilde for omslag"

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Laste opp"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Brukere"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Brukernavn"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
#, fuzzy
msgid "Email"
msgstr "E-post"

#: cps/templates/admin.html:15
#, fuzzy
msgid "Send to eReader Email"
msgstr "Send til E-Reader E-postadresse"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Admin"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Passord"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "nedlasting"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Se bøker"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Redigere"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Slett"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Offentlig hylle"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "Importer LDAP-brukere"

#: cps/templates/admin.html:62
#, fuzzy
msgid "Email Server Settings"
msgstr "Innstillinger for e-postserver"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTP vertsnavn"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTP-port"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "Kryptering"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTP-pålogging"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
#, fuzzy
msgid "From Email"
msgstr "Fra e-post"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "E-posttjeneste"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via Oauth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Konfigurasjon"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Caliber Database Directory"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Loggnivå"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Havn"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Ekstern port"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Bøker per side"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Opplastinger"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Anonym surfing"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Offentlig registrering"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Magic Link ekstern pålogging"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Omvendt proxy-pålogging"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Omvendt proxy-overskriftsnavn"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Rediger Caliber-databasekonfigurasjon"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Rediger grunnleggende konfigurasjon"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Rediger UI-konfigurasjon"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "Planlagte oppgaver"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr ""

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
#, fuzzy
msgid "Maximum Duration"
msgstr "Maksimal oppgavevarighet"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
#, fuzzy
msgid "Generate Thumbnails"
msgstr "Generer miniatyrbilder av bokomslag"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "Generer serieomslagsminiatyrbilder"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Koble til Caliber-databasen på nytt"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr ""

#: cps/templates/admin.html:197
#, fuzzy
msgid "Refresh Thumbnail Cache"
msgstr "Oppdater thumbnail cover Cache"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Administrasjon"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Last ned feilsøkingspakken"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Vis logger"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Omstart"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Skru av"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "Versjonsinformasjon"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Versjon"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Detaljer"

#: cps/templates/admin.html:232
#, fuzzy
msgid "Current Version"
msgstr "Gjeldende versjon"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Se etter oppdatering"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Utfør oppdatering"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Er du sikker på at du vil starte på nytt?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "OK"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Avbryt"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Er du sikker på at du vil slå av?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Oppdaterer, vennligst ikke last inn denne siden på nytt"

#: cps/templates/author.html:15
msgid "via"
msgstr "via"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "I biblioteket"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Sorter etter bokdato, nyeste først"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Sorter etter bokdato, eldste først"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Sorter tittelen i alfabetisk rekkefølge"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Sorter tittelen i omvendt alfabetisk rekkefølge"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Sorter etter publiseringsdato, nyeste først"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Sorter etter publiseringsdato, eldste først"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "redusere"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Mer av"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, fuzzy, python-format
msgid "Book %(index)s of %(range)s"
msgstr "Sletting av bok %(index)s mislyktes: %(range)s"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Språk"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Forlegger"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
#, fuzzy
msgid "Published"
msgstr "Forlegger"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
#, fuzzy
msgid "Description:"
msgstr "Beskrivelse"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr ""

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr ""

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
#, fuzzy
msgid "No Results Found"
msgstr "Merket ble ikke funnet"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr ""

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr ""

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr ""

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Slett bok"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Slett formater:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "Konverter bokformat:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Konverter fra:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "Velg et alternativ"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Konvertere til:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Konverter bok"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
#, fuzzy
msgid "Uploading..."
msgstr "Laster inn..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Lukk"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#, fuzzy
msgid "Error"
msgstr "Serverport"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr ""

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Last opp format"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Boktittel"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Forfatter"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Tagger"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "Serie-ID"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Publiseringsdato"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Vurdering"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Beskrivelse"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "Identifikatorer"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "Identifikatortype"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "Identifikatorverdi"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Ta bort"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "Legg til identifikator"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Hent omslag fra URL (JPEG - Bilde vil bli lastet ned og lagret i databasen)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Last opp cover fra lokal disk"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Ja"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "Nei"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Se bok på Lagre"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Hent metadata"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Lagre"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Nøkkelord"

#: cps/templates/book_edit.html:240
msgid "Search keyword"
msgstr "Søk nøkkelord"

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Klikk på omslaget for å laste metadata til skjemaet"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Laster inn..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Kilde"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Søkefeil!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Ingen resultater! Prøv et annet søkeord."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "Dette feltet er obligatorisk"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Slå sammen valgte bøker"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Fjern valg"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "Bytt forfatter og tittel"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "Oppdater tittelsortering automatisk"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "Oppdater forfattersortering automatisk"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Skriv inn tittel"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Tittel"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Skriv inn tittelsortering"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Tittelsortering"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Skriv inn forfattersortering"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Forfatter Sort"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Skriv inn forfattere"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Skriv inn kategorier"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Gå inn i serien"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Serieindeks"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Skriv inn Språk"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Publiseringsdato"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Skriv inn Publishers"

#: cps/templates/book_table.html:73
msgid "Enter comments"
msgstr "Skriv inn kommentarer"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "Kommentarer"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "Arkivstatus"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Les status"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
msgid "Enter "
msgstr "Tast inn "

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Er du virkelig sikker?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "Bøker med tittel vil bli slått sammen fra:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "Til bok med tittel:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Slå sammen"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Plassering av Caliber-databasen"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Bruker du Google Disk?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Autentiser Google Disk"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Google Drive Caliber-mappe"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "Metadata Se kanal-ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Oppheve"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "Ny db-plassering er ugyldig, vennligst skriv inn gyldig bane"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Serverkonfigurasjon"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Serverport"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "SSL-sertfilplassering (la den stå tom for ikke-SSL-servere)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "SSL-nøkkelfilplassering (la den stå tom for ikke-SSL-servere)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Oppdater kanalen"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Stabil"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Nattlig"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "Klarerte verter (kommaseparert)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Loggfilkonfigurasjon"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Plassering og navn på loggfil (calibre-web.log for ingen oppføring)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Aktiver tilgangslogg"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Plassering og navn på tilgangsloggfil (access.log for ingen oppføring)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Funksjonskonfigurasjon"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "Konverter ikke-engelske tegn i tittel og forfatter mens du lagrer på disk"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Aktiver opplastinger"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(Vennligst sørg for at brukerne også har opplastingsrettigheter)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Tillatte opplastingsfilformater"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Aktiver anonym surfing"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Aktiver offentlig registrering"

#: cps/templates/config_edit.html:131
#, fuzzy
msgid "Use Email as Username"
msgstr "Bruk e-post som brukernavn"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Aktiver Magic Link Remote Login"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Aktiver Kobo-synkronisering"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Ukjente proxy-forespørsler til Kobo Store"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Server ekstern port (for portviderekoblede API-anrop)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Bruk Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Goodreads API-nøkkel"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Tillat omvendt proxy-autentisering"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Påloggingstype"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Bruk standardautentisering"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Bruk LDAP-autentisering"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Bruk OAuth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAP-serverens vertsnavn eller IP-adresse"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAP-serverport"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAP-kryptering"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP CACertificate Path (Kun nødvendig for klientsertifikatautentisering)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP-sertifikatbane (kun nødvendig for klientsertifikatautentisering)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP-nøkkelfilbane (kun nødvendig for klientsertifikatautentisering)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP-autentisering"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anonym"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Uautentisert"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Enkel"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP-administratorbrukernavn"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP-administratorpassord"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP Distinguished Name (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP-brukerobjektfilter"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "LDAP Server er OpenLDAP?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "Følgende innstillinger er nødvendig for brukerimport"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAP gruppeobjektfilter"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAP-gruppenavn"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "Felt for LDAP-gruppemedlemmer"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAP-medlemsbrukerfilterdeteksjon"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr ""

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr ""

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr ""

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr ""

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr ""

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr ""

#: cps/templates/config_edit.html:319
#, fuzzy
msgid "External binaries"
msgstr "Ekstern port"

#: cps/templates/config_edit.html:325
msgid "Path to Calibre Binaries"
msgstr ""

#: cps/templates/config_edit.html:333
#, fuzzy
msgid "Calibre E-Book Converter Settings"
msgstr "Caliber ebook-convert %(tool)s ble ikke funnet"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr ""

#: cps/templates/config_edit.html:344
msgid "Location of Unrar binary"
msgstr ""

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "Vis forfattervalg"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr ""

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr ""

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr ""

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr ""

#: cps/templates/config_edit.html:393
msgid "User Password policy"
msgstr ""

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr ""

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr ""

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr ""

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr ""

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr ""

#: cps/templates/config_view_edit.html:17
#, fuzzy
msgid "View Configuration"
msgstr "Konfigurasjon"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr ""

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr ""

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr ""

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr ""

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr ""

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr ""

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr ""

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr ""

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr ""

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr ""

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
#, fuzzy
msgid "Admin User"
msgstr "Legg til ny bruker"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
#, fuzzy
msgid "Allow Downloads"
msgstr "Nedlastinger"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr ""

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
#, fuzzy
msgid "Allow Uploads"
msgstr "Aktiver opplastinger"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr ""

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
#, fuzzy
msgid "Allow Delete Books"
msgstr "Vis nyere bøker"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr ""

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr ""

#: cps/templates/config_view_edit.html:123
msgid "Default Language"
msgstr ""

#: cps/templates/config_view_edit.html:131
msgid "Default Visible Language of Books"
msgstr ""

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr ""

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr ""

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr ""

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr ""

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr ""

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr ""

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr ""

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr ""

#: cps/templates/detail.html:262
msgid "Mark Book as Read or Unread"
msgstr ""

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
#, fuzzy
msgid "Read"
msgstr "redusere"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr ""

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr ""

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "Arkiverte bøker"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
#, fuzzy
msgid "Add to shelf"
msgstr "Rediger en hylle"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr ""

#: cps/templates/detail.html:348
#, fuzzy
msgid "Edit Metadata"
msgstr "redigere metadata"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr ""

#: cps/templates/email_edit.html:15
msgid "Standard Email Account"
msgstr ""

#: cps/templates/email_edit.html:16
msgid "Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr ""

#: cps/templates/email_edit.html:42
#, fuzzy
msgid "STARTTLS"
msgstr "Omstart"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr ""

#: cps/templates/email_edit.html:51
#, fuzzy
msgid "SMTP Password"
msgstr "Passord"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr ""

#: cps/templates/email_edit.html:66
msgid "Save and Send Test Email"
msgstr ""

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr ""

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr ""

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr ""

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr ""

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
#, fuzzy
msgid "Enter domainname"
msgstr "Skriv inn kommentarer"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr ""

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr ""

#: cps/templates/generate_kobo_auth_url.html:11
msgid "Kobo Token:"
msgstr ""

#: cps/templates/grid.html:21
msgid "List"
msgstr ""

#: cps/templates/http_error.html:34
#, fuzzy
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "E-postserveren er ikke konfigurert, kontakt administratoren din!"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr ""

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "Databasekonfigurasjon"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr ""

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr ""

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr ""

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr ""

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
#, fuzzy
msgid "Sort authors in alphabetical order"
msgstr "Sorter tittelen i alfabetisk rekkefølge"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
#, fuzzy
msgid "Sort authors in reverse alphabetical order"
msgstr "Sorter tittelen i omvendt alfabetisk rekkefølge"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr ""

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr ""

#: cps/templates/index.xml:7
#, fuzzy
msgid "Start"
msgstr "Omstart"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr ""

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr ""

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr ""

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr ""

#: cps/templates/index.xml:45
#, fuzzy
msgid "Recently added Books"
msgstr "Vis nyere bøker"

#: cps/templates/index.xml:49
#, fuzzy
msgid "The latest Books"
msgstr "Slå sammen valgte bøker"

#: cps/templates/index.xml:54
#, fuzzy
msgid "Random Books"
msgstr "Vis tilfeldige bøker"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr ""

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr ""

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr ""

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr ""

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr ""

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr ""

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr ""

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr ""

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr ""

#: cps/templates/layout.html:32
#, fuzzy
msgid "Toggle Navigation"
msgstr "Loggfilkonfigurasjon"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "Enkel"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr ""

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
#, fuzzy
msgid "Settings"
msgstr "Vurderinger"

#: cps/templates/layout.html:138
#, fuzzy
msgid "Please do not refresh the page"
msgstr "Oppdaterer, vennligst ikke last inn denne siden på nytt"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr ""

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr ""

#: cps/templates/layout.html:202
#, fuzzy
msgid "Book Details"
msgstr "Detaljer"

#: cps/templates/list.html:22
msgid "Grid"
msgstr ""

#: cps/templates/listenmp3.html:167
#, fuzzy
msgid "Archived"
msgstr "Arkiverte bøker"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr ""

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr ""

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr ""

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr ""

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr ""

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr ""

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr ""

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr ""

#: cps/templates/logviewer.html:21
#, fuzzy
msgid "Download Access Log"
msgstr "Aktiver tilgangslogg"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr ""

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr ""

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr ""

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr ""

#: cps/templates/modal_dialogs.html:15
#, fuzzy
msgid "Enter Tag"
msgstr "Tast inn "

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr ""

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr ""

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr ""

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr ""

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr ""

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr ""

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr ""

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr ""

#: cps/templates/modal_dialogs.html:83
#, fuzzy
msgid "name"
msgstr "Brukernavn"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr ""

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr ""

#: cps/templates/modal_dialogs.html:98
#, fuzzy
msgid "Select"
msgstr "Slett"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr ""

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr ""

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr ""

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "Bruk e-post som brukernavn"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
#, fuzzy
msgid "Light"
msgstr "Nattlig"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr ""

#: cps/templates/read.html:88
msgid "Sepia"
msgstr ""

#: cps/templates/read.html:90
msgid "Black"
msgstr ""

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr ""

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr ""

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "Slett"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "Venter"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "E-post"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "redusere"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "Ugyldig lesekolonne"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr ""

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr ""

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr ""

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
#, fuzzy
msgid "Next Page"
msgstr "Admin side"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr ""

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr ""

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr ""

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr ""

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr ""

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr ""

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr ""

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr ""

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr ""

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "Admin side"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr ""

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr ""

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr ""

#: cps/templates/readcbr.html:124
#, fuzzy
msgid "Height"
msgstr "Nattlig"

#: cps/templates/readcbr.html:125
#, fuzzy
msgid "Native"
msgstr "Lagre"

#: cps/templates/readcbr.html:130
#, fuzzy
msgid "Rotate"
msgstr "Startet"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr ""

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr ""

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr ""

#: cps/templates/readcbr.html:150
#, fuzzy
msgid "Direction"
msgstr "Administrasjon"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr ""

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr ""

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr ""

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr ""

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr ""

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr ""

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr ""

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr ""

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr ""

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr ""

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr ""

#: cps/templates/register.html:10
#, fuzzy
msgid "Choose a username"
msgstr "Bruk e-post som brukernavn"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr ""

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr ""

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr ""

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr ""

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr ""

#: cps/templates/schedule_edit.html:33
#, fuzzy
msgid "Generate Series Cover Thumbnails"
msgstr "Generer serieomslagsminiatyrbilder"

#: cps/templates/search.html:7
#, fuzzy
msgid "Search Term:"
msgstr "Søkefeil!"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr ""

#: cps/templates/search_form.html:21
#, fuzzy
msgid "Published Date From"
msgstr "Publisert etter "

#: cps/templates/search_form.html:31
#, fuzzy
msgid "Published Date To"
msgstr "Publisert etter "

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr ""

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr ""

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr ""

#: cps/templates/search_form.html:116
#, fuzzy
msgid "Exclude Languages"
msgstr "Skriv inn Språk"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr ""

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr ""

#: cps/templates/search_form.html:145
#, fuzzy
msgid "Rating Above"
msgstr "Vurdering: Ingen"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr ""

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr ""

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr ""

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr ""

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr ""

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr ""

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr ""

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr ""

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "Sorter etter bokdato, nyeste først"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "Sorter etter bokdato, eldste først"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr ""

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr ""

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr ""

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr ""

#: cps/templates/stats.html:7
#, fuzzy
msgid "Library Statistics"
msgstr "Statistikk"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr ""

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr ""

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr ""

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr ""

#: cps/templates/stats.html:29
#, fuzzy
msgid "System Statistics"
msgstr "Statistikk"

#: cps/templates/stats.html:33
msgid "Program"
msgstr ""

#: cps/templates/stats.html:34
#, fuzzy
msgid "Installed Version"
msgstr "ikke installert"

#: cps/templates/tasks.html:12
#, fuzzy
msgid "User"
msgstr "Brukere"

#: cps/templates/tasks.html:14
#, fuzzy
msgid "Task"
msgstr "Oppgaver"

#: cps/templates/tasks.html:15
#, fuzzy
msgid "Status"
msgstr "Omstart"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr ""

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr ""

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "Slå sammen"

#: cps/templates/tasks.html:21
#, fuzzy
msgid "Actions"
msgstr "Vurderinger"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr ""

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr ""

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr ""

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
#, fuzzy
msgid "Language of Books"
msgstr "Språk"

#: cps/templates/user_edit.html:54
#, fuzzy
msgid "OAuth Settings"
msgstr "Vis forfattervalg"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr ""

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr ""

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr ""

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr ""

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr ""

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr ""

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr ""

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
#, fuzzy
msgid "Delete User"
msgstr "Kan ikke slette gjestebruker"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr ""

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr ""

#: cps/templates/user_table.html:131
#, fuzzy
msgid "Edit User"
msgstr "Rediger brukere"

#: cps/templates/user_table.html:134
#, fuzzy
msgid "Enter Username"
msgstr "Brukernavn"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "Test e-post"

#: cps/templates/user_table.html:136
msgid "Enter eReader Email"
msgstr ""

#: cps/templates/user_table.html:136
msgid "eReader Email"
msgstr ""

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr ""

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr ""

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr ""

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr ""

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr ""

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr ""

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr ""

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr ""

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr ""

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr ""

#: cps/templates/user_table.html:144
#, fuzzy
msgid "Change Password"
msgstr "Passord"

#: cps/templates/user_table.html:147
msgid "View"
msgstr ""

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr ""

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr ""

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "Vis serieutvalg"

