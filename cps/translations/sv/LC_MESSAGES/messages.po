# German translations for Calibre-Web.
# Copyright (C) 2016 <PERSON><PERSON> Isaacs
# This file is distributed under the same license as the Calibre-Web
# project.
# FIRST AUTHOR OzzieIsaacs, 2016.
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/Calibre-Web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2021-05-13 11:00+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: sv\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statistik"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "Server startas om, vänligen uppdatera sidan"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "Stänger servern, vänligen stäng fönstret"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr ""

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Okänt kommando"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Testa e-post i kö för att skicka till %(email)s, vänligen kontrollera Uppgifter för resultat"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Okänd"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Administrationssida"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Grundläggande konfiguration"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "Användargränssnitt konfiguration"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, fuzzy, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "Anpassad kolumn n.%(column)d finns inte i calibre-databasen"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "Redigera användare"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Alla"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Användaren hittades inte"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} användare har tagits bort"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Visa alla"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Felaktig begäran"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Gästnamn kan inte ändras"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "Gäst kan inte ha den här rollen"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Ingen administratörsanvändare kvar, kan inte ta bort administratörsrollen"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Värdet måste vara sant eller falskt"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Ogiltig roll"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "Gästen kan inte ha den här vyn"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "Ogiltig vy"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Gästens språk bestäms automatiskt och kan inte ställas in"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Inget giltigt språk anges"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Inget giltigt bokspråk anges"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Parameter hittades inte"

#: cps/admin.py:578
#, fuzzy
msgid "Invalid Read Column"
msgstr "Ogiltig roll"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr ""

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Calibre-Web konfiguration uppdaterad"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Vill du verkligen ta bort Kobo-token?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Vill du verkligen ta bort den här domänen?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Vill du verkligen ta bort den här användaren?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Är du säker på att du vill ta bort hyllan?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Är du säker på att du vill ändra språk för valda användare?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Är du säker på att du vill ändra synliga bokspråk för valda användare?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Är du säker på att du vill ändra den valda rollen för de valda användarna?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Är du säker på att du vill ändra de valda begränsningarna för de valda användarna?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Är du säker på att du vill ändra de valda synlighetsbegränsningarna för de valda användarna?"

#: cps/admin.py:635
#, fuzzy
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Är du säker på att du vill ändra den valda rollen för de valda användarna?"

#: cps/admin.py:637
#, fuzzy
msgid "Are you sure you want to change Calibre library location?"
msgstr "Är du säker på att du vill stoppa Calibre-Web?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr ""

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr ""

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Förneka"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Tillåt"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr ""

#: cps/admin.py:987
msgid "Tag not found"
msgstr "Taggen hittades inte"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Ogiltig åtgärd"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json är inte konfigurerad för webbapplikation"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Loggfilens plats är inte giltig, vänligen ange rätt sökväg"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Åtkomstloggplatsens plats är inte giltig, vänligen ange rätt sökväg"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Vänligen ange en LDAP-leverantör, port, DN och användarobjektidentifierare"

#: cps/admin.py:1223
#, fuzzy
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Ange giltigt användarnamn för att återställa lösenordet"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr ""

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAP-gruppobjektfilter måste ha en \"%s\"-formatidentifierare"

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAP-gruppobjektfilter har omatchande parentes"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP-användarobjektfilter måste ha en \"%s\"-formatidentifierare"

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAP-användarobjektfilter har omatchad parentes"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "Användarfilter för LDAP-medlemmar måste ha en \"%s\"-formatidentifierare"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "Användarfilter för LDAP-medlemmar har omatchad parentes"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAP-certifikat, certifikat eller nyckelplats är inte giltigt, vänligen ange rätt sökväg"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Lägg till ny användare"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "Ändra SMTP-inställningar"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr ""

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Databasfel: %(error)s."

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "Testa e-post i kö för att skicka till %(email)s, vänligen kontrollera Uppgifter för resultat"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Det gick inte att skicka Testmeddelandet: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Vänligen konfigurera din e-postadress först..."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "E-postserverinställningar uppdaterade"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr ""

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr ""

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr ""

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr ""

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "Ett okänt fel uppstod. Försök igen senare."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr ""

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Redigera användaren %(nick)s"

#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Lösenord för användaren %(user)s återställd"

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Konfigurera SMTP-postinställningarna först..."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Visaren för loggfil"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Begär uppdateringspaketet"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Hämtar uppdateringspaketet"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Packar upp uppdateringspaketet"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Ersätta filer"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Databasanslutningarna är stängda"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Stoppar server"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Uppdatering klar, tryck på okej och uppdatera sidan"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Uppdateringen misslyckades:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTP-fel"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Anslutningsfel"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Tiden ute när du etablerade anslutning"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Allmänt fel"

#: cps/admin.py:1551
#, fuzzy
msgid "Update file could not be saved in temp dir"
msgstr "Uppdateringsfilen kunde inte sparas i Temp Dir"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr ""

#: cps/admin.py:1576
#, fuzzy
msgid "Failed to extract at least One LDAP User"
msgstr "Det gick inte att skapa minst en LDAP-användare"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Det gick inte att skapa minst en LDAP-användare"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Fel: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Fel: Ingen användare återges som svar på LDAP-servern"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Minst en LDAP-användare hittades inte i databasen"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} användare har importerats"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "DB-plats är inte giltig, vänligen ange rätt sökväg"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "DB är inte skrivbar"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "Keyfile-platsen är inte giltig, vänligen ange rätt sökväg"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "Certfile-platsen är inte giltig, vänligen ange rätt sökväg"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr ""

#: cps/admin.py:1917
#, fuzzy
msgid "Database Settings updated"
msgstr "E-postserverinställningar uppdaterade"

#: cps/admin.py:1925
#, fuzzy
msgid "Database Configuration"
msgstr "Funktion konfiguration"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "Fyll i alla fält!"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "E-posten är inte från giltig domän"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Lägg till ny användare"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "Användaren '%(user)s' skapad"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "Hittade ett befintligt konto för den här e-postadressen eller namnet."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "Användaren '%(nick)s' borttagen"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "Det går inte att ta bort gästanvändaren"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Ingen adminstratörsanvändare kvar, kan inte ta bort användaren"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr ""

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "Användaren '%(nick)s' uppdaterad"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Sök"

#: cps/converter.py:31
msgid "not installed"
msgstr "inte installerad"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Körningstillstånd saknas"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Ingen"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Filen %(file)s uppladdad"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Källa eller målformat för konvertering saknas"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Boken är i kö för konvertering till %(book_format)s"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Det gick inte att konvertera den här boken: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Hoppsan! Vald boktitel är inte tillgänglig. Filen finns inte eller är inte tillgänglig"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr ""

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "Identifierare är inte skiftlägeskänsliga, skriver över gammal identifierare"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, fuzzy, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "%(langname)s är inte ett giltigt språk"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "Metadata uppdaterades"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr ""

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Uppladdad bok finns förmodligen i biblioteket, överväg att ändra innan du laddar upp nya: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "Filändelsen '%(ext)s' får inte laddas upp till den här servern"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "Filändelsen '%(ext)s' får inte laddas upp till den här servern"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Filen som ska laddas upp måste ha en ändelse"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Filen %(filename)s kunde inte sparas i temp dir"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Det gick inte att flytta omslagsfil %(file)s: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Bokformat har tagits bort"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Boken har tagits bort"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr ""

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "redigera metadata"

#: cps/editbooks.py:1016
#, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr ""

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr ""

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Det gick inte att skapa sökväg %(path)s (behörighet nekad)."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Det gick inte att lagra filen %(file)s."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Filformatet %(ext)s lades till %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Installationen av Google Drive är inte klar, försök att inaktivera och aktivera Google Drive igen"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Återuppringningsdomänen är inte verifierad, följ stegen för att verifiera domänen i Google utvecklarkonsol"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "%(format)s formatet hittades inte för bok-id: %(book)d"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "%(format)s hittades inte på Google Drive: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s hittades inte: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
#, fuzzy
msgid "Send to eReader"
msgstr "Skicka till Kindle"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "Detta e-postmeddelande har skickats via Calibre-Web."

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web test e-post"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "Test e-post"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Kom igång med Calibre-Web"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "Registrera e-post för användare: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "Konvertera %(orig)s till %(format)s och skicka till Kindle"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "Skicka %(format)s till Kindle"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "Skicka till Kindle"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Den begärda filen kunde inte läsas. Kanske fel behörigheter?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr ""

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Borttagning av bokmapp för boken %(id)s misslyckades, sökvägen har undermappar: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Borttagning av boken %(id)s misslyckades: %(message)s"

#: cps/helper.py:392
#, fuzzy, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Borttagning av boken %(id)s, boksökväg inte giltig: %(path)s"

#: cps/helper.py:439
#, fuzzy, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Byt namn på titel från: \"%(src)s\" till \"%(dest)s\" misslyckades med fel: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Filen %(file)s hittades inte på Google Drive"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Byt namn på titel från: \"%(src)s\" till \"%(dest)s\" misslyckades med fel: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Boksökvägen %(path)s hittades inte på Google Drive"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr ""

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Detta användarnamn är redan taget"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "Ogiltigt e-postadressformat"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr ""

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr ""

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Fel vid hämtning av omslaget"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Fel på omslagsformat"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr ""

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Det gick inte att skapa sökväg för omslag"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Omslagsfilen är inte en giltig bildfil eller kunde inte lagras"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Endast jpg/jpeg/png/webp/bmp-filer stöds som omslagsfil"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr ""

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Endast jpg/jpeg-filer stöds som omslagsfil"

#: cps/helper.py:989 cps/helper.py:1149
#, fuzzy
msgid "Cover"
msgstr "Upptäck"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "UnRar binär fil hittades inte"

#: cps/helper.py:1017
#, fuzzy
msgid "Error executing UnRar"
msgstr "Fel vid körning av UnRar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "DB är inte skrivbar"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Körningstillstånd saknas"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "Fel vid körning av UnRar"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr ""

#: cps/kobo_auth.py:92
#, fuzzy
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Vänligen få tillgång till calibre-web från icke localhost för att få giltig api_endpoint för Kobo-enhet"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo-installation"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Registrera dig med %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "du är nu inloggad som: \"%(nickname)s\""

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Länk till %(oauth)s lyckades"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Inloggningen misslyckades, ingen användare kopplad till OAuth-konto"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Sluta länka till %(oauth)s lyckades"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Sluta länka till %(oauth)s misslyckades"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Inte länkad till %(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Det gick inte att logga in med GitHub."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Det gick inte att hämta användarinformation från GitHub."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Det gick inte att logga in med Google."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Det gick inte att hämta användarinformation från Google."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub Oauth-fel, försök igen senare."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "GitHub Oauth-fel: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google Oauth-fel, försök igen senare."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google Oauth-fel: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} stjärnor"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Logga in"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Token hittades inte"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Token har löpt ut"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Lyckades! Vänligen återvänd till din enhet"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Böcker"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Visa senaste böcker"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Heta böcker"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Visa heta böcker"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Hämtade böcker"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Visa hämtade böcker"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Bäst rankade böcker"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Visa böcker med bästa betyg"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Lästa böcker"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "Visa lästa och olästa"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Olästa böcker"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Visa olästa"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Upptäck"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Visa slumpmässiga böcker"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Kategorier"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "Visa kategorival"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Serier"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "Visa serieval"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Författare"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "Visa författarval"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Förlag"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "Visa urval av förlag"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Språk"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "Visa språkval"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Betyg"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "Visa val av betyg"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Filformat"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "Visa val av filformat"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Arkiverade böcker"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "Visa arkiverade böcker"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Boklista"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Visa boklista"

#: cps/search.py:201
msgid "Published after "
msgstr "Publicerad efter "

#: cps/search.py:208
msgid "Published before "
msgstr "Publicerad före "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Betyg <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Betyg >= %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "Lässtatus = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr ""

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Avancerad sökning"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Ogiltig hylla specificerad"

#: cps/shelf.py:55
#, fuzzy
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Tyvärr får du inte lägga till en bok på hyllan: %(shelfname)s"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Boken är redan en del av hyllan: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Boken har lagts till i hyllan: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr ""

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "Böcker är redan en del av hyllan: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "Böcker har lagts till hyllan: %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Kunde inte lägga till böcker till hyllan: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Boken har tagits bort från hyllan: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr ""

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Skapa en hylla"

#: cps/shelf.py:226
#, fuzzy
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Tyvärr har du inte rätt att ta bort en bok från den här hyllan: %(sname)s"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Redigera en hylla"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr ""

#: cps/shelf.py:239
#, fuzzy
msgid "Shelf successfully deleted"
msgstr "Boken har tagits bort"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Ändra ordning på hyllan: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr ""

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Hyllan %(title)s skapad"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Hyllan %(title)s ändrad"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "Det fanns ett fel"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "En offentlig hylla med namnet \"%(title)s\" finns redan."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "En privat hylla med namnet \"%(title)s\" finns redan."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Hylla: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Fel vid öppning av hyllan. Hylla finns inte eller är inte tillgänglig"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Uppgifter"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Väntar"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Misslyckades"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Startad"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Klar"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr ""

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr ""

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Okänd status"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Oväntade data vid läsning av uppdateringsinformation"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Ingen uppdatering tillgänglig. Du har redan den senaste versionen installerad"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "En ny uppdatering är tillgänglig. Klicka på knappen nedan för att uppdatera till den senaste versionen."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "Kunde inte hämta uppdateringsinformation"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Klicka på knappen nedan för att uppdatera till den senaste stabila versionen."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "En ny uppdatering är tillgänglig. Klicka på knappen nedan för att uppdatera till version: %(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Ingen versionsinformation tillgänglig"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Upptäck (slumpmässiga böcker)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Heta böcker (mest hämtade)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Hämtade böcker av %(user)s"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Författare: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Förlag: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Serier: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr ""

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Betyg: %(rating)s stars"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Filformat: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Kategori: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Språk: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "Hämtningar"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Betygslista"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Lista över filformat"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "Konfigurera SMTP-postinställningarna först..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Boken är i kö för att skicka till %(eReadermail)s"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Det gick inte att skicka den här boken: %(res)s"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Konfigurera din kindle-e-postadress först..."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr ""

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Registrera"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "E-postservern är inte konfigurerad, kontakta din administratör!"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "E-postservern är inte konfigurerad, kontakta din administratör!"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "Din e-post är inte tillåten att registrera"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "Bekräftelsemail skickades till ditt e-postkonto."

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "Det går inte att aktivera LDAP-autentisering"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr ""

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "du är nu inloggad som: \"%(nickname)s\""

#: cps/web.py:1415
#, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr ""

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "Det gick inte att logga in: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "Fel användarnamn eller lösenord"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "Nytt lösenord skickades till din e-postadress"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "Ett okänt fel uppstod. Försök igen senare."

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "Ange giltigt användarnamn för att återställa lösenordet"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "du är nu inloggad som: \"%(nickname)s\""

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "%(name)ss profil"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "Profilen uppdaterad"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "Hittade ett befintligt konto för den här e-postadressen"

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Hittade ingen giltig gmail.json-fil med OAuth-information"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, fuzzy, python-format
msgid "%(book)s send to E-Reader"
msgstr "Skicka till Kindle"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "calibre e-bokkonverterings %(tool)s hittades inte"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s-format hittades inte på disken"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "E-bokkonverteraren misslyckades med okänt fel"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify-konverteraren misslyckades: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "Konverterad fil hittades inte eller mer än en fil i mappen %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "calibre misslyckades med fel: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "E-bokkonverteraren misslyckades: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr ""

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr ""

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr ""

#: cps/tasks/metadata_backup.py:34
#, fuzzy
msgid "Backing up Metadata"
msgstr "redigera metadata"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr ""

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Ladda upp"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Användarlista"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Smeknamn"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "E-post"

#: cps/templates/admin.html:15
#, fuzzy
msgid "Send to eReader Email"
msgstr "Kindle"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Administratör"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Lösenord"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "Hämta"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Visa e-böcker"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Redigera"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Ta bort"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Publik hylla"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "Importera LDAP-användare"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "Inställningar för SMTP-e-postserver"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTP-värdnamn"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTP-port"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "SSL"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTP-inloggning"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Från meddelande"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "E-posttjänst"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via Oauth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Konfiguration"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Calibre DB dir"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Loggnivå"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Port"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Extern port"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Böcker per sida"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Laddar upp"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Anonym surfning"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Publik registrering"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Fjärrinloggning"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Omvänd proxy inloggning"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Omvänt proxy rubriknamn"

#: cps/templates/admin.html:159
#, fuzzy
msgid "Edit Calibre Database Configuration"
msgstr "Redigera grundläggande konfiguration"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Redigera grundläggande konfiguration"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Redigera UI-konfiguration"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr ""

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr ""

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr ""

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr ""

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr ""

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr ""

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr ""

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr ""

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Administration"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Hämta felsökningspaketet"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Visa loggfiler"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Starta om Calibre-Web"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Stoppa Calibre-Web"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr ""

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Version"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Detaljer"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "Aktuell version"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Sök efter uppdatering"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Utför uppdatering"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Är du säker på att du vill starta om Calibre-Web?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "Ok"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Avbryt"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Är du säker på att du vill stoppa Calibre-Web?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Uppdaterar, vänligen uppdatera inte sidan"

#: cps/templates/author.html:15
msgid "via"
msgstr "via"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "I biblioteket"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Sortera efter bokdatum, nyast först"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Sortera efter bokdatum, äldsta först"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Sortera titel i alfabetisk ordning"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Sortera titel i omvänd alfabetisk ordning"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Sortera efter publiceringsdatum, nyast först"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Sortera efter publiceringsdatum, äldsta först"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "minska"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Mer av"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, fuzzy, python-format
msgid "Book %(index)s of %(range)s"
msgstr ""

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Språk"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Förlag"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "Publicerad"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "Beskrivning:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "Föregående"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "Nästa"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "Inga resultat hittades"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "Hem"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "Sök i bibliotek"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "Logga ut"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Ta bort boken"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Ta bort format:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "Konvertera bokformat:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Konvertera från:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "välj ett alternativ"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Konvertera till:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Konvertera boken"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "Laddar upp..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Stäng"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "Fel"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "Uppladdning klar, bearbetning, vänligen vänta ..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Ladda upp format"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Boktitel"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Författare"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Taggar"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "Serie-ID"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Publiceringsdatum"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Betyg"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Beskrivning"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "Identifierare"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "Identifierartyp"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "Identifierarvärde"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Ta bort"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "Lägg till identifierare"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Omslagswebbadress (jpg, omslag hämtas och lagras i databasen, fältet är efteråt tomt igen)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Ladda upp omslag från lokal enhet"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Ja"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "Nej"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Visa bok vid Spara"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Hämta metadata"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Spara"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Sökord"

#: cps/templates/book_edit.html:240
#, fuzzy
msgid "Search keyword"
msgstr " Sök sökord "

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Klicka på omslaget för att läsa in metadata till formuläret"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Läser in..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Källa"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Sökningsfel!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Inga resultat hittades! Försök med ett annat sökord."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "Detta fält är obligatoriskt"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Slå ihop utvalda böcker"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Ta bort markeringar"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr ""

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "Uppdatera titelsortering automatiskt"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "Uppdatera författarsortering automatiskt"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Ange titel"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Titel"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Ange titelsortering"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Titelsortering"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Ange författarsortering"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Författarsortering"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Ange författare"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Ange kategorier"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Ange serier"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Serieindex"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Ange språk"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Publiceringsdatum"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Ange utgivare"

#: cps/templates/book_table.html:73
#, fuzzy
msgid "Enter comments"
msgstr "Ange domännamn"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr ""

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr ""

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Lässtatus"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
#, fuzzy
msgid "Enter "
msgstr "Identifierare"

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Är du verkligen säker?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "Böcker med titel slås samman från:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "I bok med titel:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Slå samman"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Plats för Calibre-databasen"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Använda Google Drive?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Autentisera Google Drive"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Google Drive Calibre-mapp"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "Metadata Titta på kanal ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Återkalla"

#: cps/templates/config_db.html:80
#, fuzzy
msgid "New db location is invalid, please enter valid path"
msgstr "DB-plats är inte giltig, vänligen ange rätt sökväg"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Serverkonfiguration"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Serverport"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "SSL certfile plats (lämna den tom för icke-SSL-servrar)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "SSL Keyfile plats (lämna den tom för icke-SSL-servrar)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Uppdatera kanal"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Stabil"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Ostabil"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr ""

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Loggfil konfiguration"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Plats och namn på loggfilen (calibre-web.log för ingen post)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Aktivera åtkomstlogg"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Plats och namn på åtkomstloggfil (access.log för ingen post)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Funktion konfiguration"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr ""

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Aktivera uppladdning"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr ""

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Tillåtna filformat för uppladdning"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Aktivera anonym surfning"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Aktivera offentlig registrering"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "Använd e-post som användarnamn"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Aktivera fjärrinloggning (\"magisk länk\")"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Aktivera Kobo sync"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Proxy okänd begäran till Kobo Store"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Extern port för server (för port vidarebefordrade API-anrop)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Använd Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Goodreads API-nyckel"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Tillåt omvänd proxyautentisering"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Inloggningstyp"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Använd standardautentisering"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Använd LDAP-autentisering"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Använd OAuth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAP-serverns värdnamn eller IP-adress"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAP-serverport"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAP-kryptering"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP CACertificate-sökväg (behövs endast för autentisering av klientcertifikat)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP-certifikatsökväg (behövs endast för autentisering av klientcertifikat)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP-nyckelfilsökväg (behövs endast för autentisering av klientcertifikat)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP-autentisering"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anonym"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Oautentiserad"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Enkel"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP-adminstratörsanvändarnamn"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP-adminstratörslösenord"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP Distinguished Name (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP-användarobjektfilter"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "LDAP-server är OpenLDAP?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "Följande inställningar behövs för användarimport"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAP-gruppobjektfilter"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAP-gruppnamn"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "Fält för LDAP-gruppmedlemmar"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAP-användarfilterdetektering för medlemmar"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "Upptäck automatiskt"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "Anpassat filter"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "LDAP-användarfilter för medlemmar"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "Skaffa %(provider)s OAuth-certifikat"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth-klient-id"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth-klient-hemlighet"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "Externa binärer"

#: cps/templates/config_edit.html:325
#, fuzzy
msgid "Path to Calibre Binaries"
msgstr "Sökväg till calibre e-bokkonverterare"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Inställningar för calibre e-bokkonverterare"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Sökväg till Kepubify calibre e-bokkonverterare"

#: cps/templates/config_edit.html:344
#, fuzzy
msgid "Location of Unrar binary"
msgstr "Plats för UnRar-binär"

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "OAuth-inställningar"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr ""

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr ""

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr ""

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr ""

#: cps/templates/config_edit.html:393
#, fuzzy
msgid "User Password policy"
msgstr "Återställ användarlösenordet"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr ""

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr ""

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr ""

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr ""

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr ""

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "Visa konfiguration"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "Antal slumpmässiga böcker att visa"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "Antal författare att visa innan de döljs (0 = inaktivera dölja)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "Tema"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "Standard tema"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "caliBlur! Mörkt tema"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "Reguljärt uttryck för att ignorera kolumner"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "Länka läst/oläst-status till Calibre-kolumn"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Visa begränsningar baserade på calibre-kolumnen"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "Reguljärt uttryck för titelsortering"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "Standardinställningar för nya användare"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "Adminstratör användare"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "Tillåt Hämtningar"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "Tillåt bokvisare"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "Tillåt Uppladdningar"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "Tillåt Redigera"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "Tillåt borttagning av böcker"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "Tillåt Ändra lösenord"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "Tillåt Redigering av offentliga hyllor"

#: cps/templates/config_view_edit.html:123
#, fuzzy
msgid "Default Language"
msgstr "Uteslut språk"

#: cps/templates/config_view_edit.html:131
#, fuzzy
msgid "Default Visible Language of Books"
msgstr "Visa böcker med språk"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "Standardvisibiliteter för nya användare"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "Visa slumpmässiga böcker i detaljvyn"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "Lägg till tillåtna/avvisade taggar"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "Lägg till tillåtna/avvisade anpassade kolumnvärden"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "Läs i webbläsaren"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "Lyssna i webbläsaren"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "Markera som oläst"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "Markera som läst"

#: cps/templates/detail.html:262
#, fuzzy
msgid "Mark Book as Read or Unread"
msgstr "Markera som oläst"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "Läst"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "Återställ från arkivet"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "Lägg till i arkivet"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "Arkiverad"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "Lägg till hyllan"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(Publik)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "Redigera metadata"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "Välj servertyp"

#: cps/templates/email_edit.html:15
#, fuzzy
msgid "Standard Email Account"
msgstr "Använd standard e-postkonto"

#: cps/templates/email_edit.html:16
#, fuzzy
msgid "Gmail Account"
msgstr "Välj servertyp"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Återkalla Gmail-åtkomst"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "SMTP-lösenord"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "Gräns för bilagestorlek"

#: cps/templates/email_edit.html:66
#, fuzzy
msgid "Save and Send Test Email"
msgstr "Spara inställningarna och skicka test-e-post"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "Tillbaka"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "Tillåtna domäner för registrering"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "Lägg till domän"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "Lägg till"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "Ange domännamn"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "Avvisade domäner för registrering"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "Öppna filen .kobo/Kobo/Kobo eReader.conf i en textredigerare och lägg till (eller redigera):"

#: cps/templates/generate_kobo_auth_url.html:11
#, fuzzy
msgid "Kobo Token:"
msgstr "Kobo Sync Token"

#: cps/templates/grid.html:21
msgid "List"
msgstr ""

#: cps/templates/http_error.html:34
#, fuzzy
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "E-postservern är inte konfigurerad, kontakta din administratör!"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Skapa ärende"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "Funktion konfiguration"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "Tillbaka till hemmet"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr ""

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr ""

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr ""

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "Sortera författare i alfabetisk ordning"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "Sortera författare i omvänd alfabetisk ordning"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "Sortera stigande enligt serieindex"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "Sortera fallande enligt serieindex"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "Starta"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "Alfabetiska böcker"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "Böcker sorterade alfabetiskt"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "Populära publikationer från den här katalogen baserad på hämtningar."

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "Populära publikationer från den här katalogen baserad på betyg."

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "Senaste tillagda böcker"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "De senaste böckerna"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "Slumpmässiga böcker"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "Böcker ordnade efter författare"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "Böcker ordnade efter förlag"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "Böcker ordnade efter kategori"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "Böcker ordnade efter serier"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "Böcker ordnade efter språk"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "Böcker sorterade efter Betyg"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "Böcker ordnade av filformat"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "Hyllor"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "Böcker organiserade i hyllor"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "Växla navigering"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "Enkel"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "Konto"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "Inställningar"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "Vänligen uppdatera inte sidan"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "Bläddra"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "Om"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "Bokdetaljer"

#: cps/templates/list.html:22
msgid "Grid"
msgstr ""

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "Arkiverad"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "Kom ihåg mig"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "Glömt lösenord?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "Logga in med magisk länk"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Visa Calibre-Web-logg: "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Visa åtkomstlogg: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "Strömutmatning kan inte visas"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "Visa åtkomstlogg: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Hämta logg för calibre-web"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "Hämta åtkomstlogg"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "Välj tillåtna/avvisade taggar"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "Välj tillåtna/avvisade anpassade kolumnvärden"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "Välj tillåtna/avvisade användarens taggar"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "Välj tillåtna/avvisade anpassade kolumnvärden för användaren"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "Ange tagg"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "Lägg till visningsbegränsning"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "Detta bokformat tas bort permanent från databasen"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "Boken kommer att tas bort från Calibre-databasen"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "och från hårddisken"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Viktigt Kobo-notering: borttagna böcker kommer att finnas kvar på alla kopplade Kobo-enheter."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "Böcker måste först arkiveras och enheten synkroniseras innan en bok säkert kan tas bort."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "Välj filplats"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "typ"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "namn"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "storlek"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "Föräldramapp"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "Välj"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "Ok"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Calibre-Web e-bokkatalog"

#: cps/templates/read.html:7
#, fuzzy
msgid "epub Reader"
msgstr "PDF-läsare"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "Välj ett användarnamn"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "Ljust"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "Mörkt"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr ""

#: cps/templates/read.html:90
#, fuzzy
msgid "Black"
msgstr "Tillbaka"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "Fyll i texten igen när sidofält är öppna."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr ""

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "Ta bort"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "Väntar"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "Vertikal"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "Läst"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "Ogiltig roll"

#: cps/templates/readcbr.html:8
#, fuzzy
msgid "Comic Reader"
msgstr "PDF-läsare"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "Kortkommandon"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "Föregående sida"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "Nästa sida"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr ""

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "Skala till bäst"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "Skala till bredd"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "Skala till höjd"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "Skala till ursprunglig"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "Rotera åt höger"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "Rotera åt vänster"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "Vänd bilden"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr ""

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "Administrationssida"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "Skala"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "Bäst"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "Bredd"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "Höjd"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "Ursprunglig"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "Rotera"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "Vänd"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "Horisontell"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "Vertikal"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "Riktning"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "Vänster till höger"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "Höger till vänster"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr ""

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr ""

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr ""

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr ""

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr ""

#: cps/templates/readdjvu.html:5
#, fuzzy
msgid "DJVU Reader"
msgstr "PDF-läsare"

#: cps/templates/readpdf.html:31
#, fuzzy
msgid "PDF Reader"
msgstr "PDF-läsare"

#: cps/templates/readtxt.html:6
#, fuzzy
msgid "txt Reader"
msgstr "PDF-läsare"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "Registrera ett nytt konto"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "Välj ett användarnamn"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "Din e-postadress"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "Magic Link - Auktorisera ny enhet"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "På en annan enhet, logga in och besök:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "När du gör det kommer du automatiskt att logga in på den här enheten."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "Länken går ut efter 10 minuter."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr ""

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "Sökterm:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "Resultat för:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "Publiceringsdatum från"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "Publiceringsdatum till"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "Uteslut taggar"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "Uteslut serier"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "Uteslut hyllor"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "Uteslut språk"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "Tillägg"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "Uteslut tillägg"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "Betyg större än"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "Betyg mindre än"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr ""

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr ""

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "Ta bort den här hyllan"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "Redigera hyllegenskaper"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "Ordna böcker manuellt"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "Inaktivera ändring av ordning"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "Aktivera ändring av ordning"

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "Sortera efter bokdatum, nyast först"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "Sortera efter bokdatum, äldsta först"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "Dela med alla"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr ""

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "Drag och släpp för att ändra ordning"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "Dold bok"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "Calibre-biblioteksstatistik"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "Böcker i det här biblioteket"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "Författare i det här biblioteket"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "Kategorier i det här biblioteket"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "Serier i detta bibliotek"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "Systemstatistik"

#: cps/templates/stats.html:33
msgid "Program"
msgstr ""

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "Installerad version"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "Användare"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "Uppgift"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "Status"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "Förlopp"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "Drifttid"

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "Slå samman"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr ""

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr ""

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr ""

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "Återställ användarlösenordet"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "Visa böcker med språk"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "OAuth-inställningar"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "Koppla"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "Koppla bort"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Kobo Sync Token"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "Skapa/Visa"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr ""

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "Lägg till tillåtna/avvisade anpassade kolumnvärden"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr ""

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "Ta bort den här användaren"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Skapa Kobo Auth URL"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "Välj..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "Redigera användare"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "Ange användarnamn"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "Test e-post"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "Enter eReader Email"
msgstr "Kindle"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "eReader Email"
msgstr "Test e-post"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "Språk"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "Synliga bokspråk"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "Redigera tillåtna taggar"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "Tillåtna taggar"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "Redigera avvisade taggar"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "Avvisade taggar"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "Redigera tillåtna kolumnvärden"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "Tillåtna kolumnvärden"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "Redigera avvisade kolumnvärden"

#: cps/templates/user_table.html:142
#, fuzzy
msgid "Denied Column Values"
msgstr "Avvisade kolumnvärden"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "Ändra lösenord"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "Visa"

#: cps/templates/user_table.html:150
#, fuzzy
msgid "Edit Public Shelves"
msgstr "Redigera publika hyllor"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr ""

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "Visa läst/oläst val"

