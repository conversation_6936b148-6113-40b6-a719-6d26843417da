# Polish translations for Calibre Web.
# Copyright (C) 2017 <PERSON><PERSON><PERSON>
# This file is distributed under the same license as the Calibre Web
# project.
# <PERSON><PERSON><PERSON> <radek.k<PERSON><PERSON><PERSON><PERSON>@outlook.com>, 2017.
#
msgid ""
msgstr ""
"Project-Id-Version: Calibre Web - polski (POT: 2021-06-12 08:52)\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2021-06-12 15:35+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <radek.kierz<PERSON><EMAIL>>\n"
"Language: pl\n"
"Language-Team: \n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statystyki"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "Serwer uruchomiony ponownie, proszę odświeżyć stronę"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "Wykonano wyłączenie serwera, proszę zamknąć okno"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr ""

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Nieznane polecenie"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Testowy e-mail czeka w kolejce do wysłania do %(email)s, sprawdź zadania, aby uzyskać wynik"

# ???
#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Nieznany"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Panel administratora"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Konfiguracja podstawowa"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "Konfiguracja Interfejsu"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, fuzzy, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "Niestandardowa kolumna No.%(column)d nie istnieje w bazie calibre"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "Edytuj użytkowników"

# ???
#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Wszystko"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Nie znaleziono użytkownika"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} użytkowników usuniętych pomyślnie"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Pokaż wszystkie"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Nieprawidłowo sformułowane żądanie"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Nazwa gościa nie może być zmieniona"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "Gość nie może pełnić tej roli"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Nie można odebrać praw administratora. Brak na serwerze innego konta z prawami administratora"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Wartość musi być prawdziwa lub fałszywa"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Nieprawidłowa rola"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "Gość nie może tego zobaczyć"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "Nieprawidłowy widok"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Lokalizacja gościa jest określana automatycznie i nie można jej ustawić"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Nie podano prawidłowej lokalizacji"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Nie podano obowiązującego języka książki"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Nie znaleziono parametru"

#: cps/admin.py:578
#, fuzzy
msgid "Invalid Read Column"
msgstr "Nieprawidłowa kolumna odczytu"

#: cps/admin.py:584
#, fuzzy
msgid "Invalid Restricted Column"
msgstr "Nieprawidłowa kolumna z ograniczeniami"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Konfiguracja Calibre-Web została zaktualizowana"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Czy na pewno chcesz usunąć Token Kobo?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Czy naprawdę chcesz usunąć tę domenę?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Czy naprawdę chcesz usunąć tego użytkownika?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Czy na pewno chcesz usunąć półkę?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Czy na pewno chcesz zmienić ustawienia lokalne wybranego użytkownika(ów)?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Czy na pewno chcesz zmienić widoczne języki książek dla wybranego użytkownika (użytkowników)?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Czy na pewno chcesz zmienić wybraną rolę dla wybranego użytkownika (użytkowników)?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Czy na pewno chcesz zmienić wybrane ograniczenia dla wybranego użytkownika(ów)?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Czy na pewno chcesz zmienić wybrane ograniczenia widoczności dla wybranego użytkownika(ów)?"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Czy na pewno chcesz zmienić zachowanie synchronizacji półek dla wybranego użytkownika(ów)?"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Czy na pewno chcesz zmienić lokalizację biblioteki Calibre?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr ""

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr ""

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Zabroń"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Zezwalaj"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr ""

#: cps/admin.py:987
#, fuzzy
msgid "Tag not found"
msgstr "Nie znaleziono znacznika"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Nieprawidłowe działanie"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json nie został skonfigurowany dla aplikacji webowej"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokalizacja pliku dziennika jest nieprawidłowa, wprowadź poprawną ścieżkę"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokalizacja pliku dziennika dostępu jest nieprawidłowa, wprowadź poprawną ścieżkę"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Wprowadź dostawcę LDAP, port, nazwę wyróżniającą i identyfikator obiektu użytkownika"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Proszę wprowadzić konto i hasło usługi LDAP"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "Proszę wprowadzić konto usługi LDAP"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "Filtr obiektów grupy LDAP musi mieć jeden identyfikator formatu \"% s\""

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "Filtr obiektów grupy LDAP ma niedopasowany nawias"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "Filtr obiektów użytkownika LDAP musi mieć jeden identyfikator formatu \"% s\""

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "Filtr obiektów użytkownika LDAP ma niedopasowany nawias"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "Filtr użytkownika członka LDAP musi mieć jedno \"%s\" identyfikator formatu"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "Filtr użytkownika członka LDAP ma niedopasowane nawiasy"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "Główny urząd certyfikatu LDAP, Certyfikat lub Lokalizacja Klucza nie jest prawidłowa, Proszę wprowadzić poprawną ścieżkę"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Dodaj nowego użytkownika"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "Zmień ustawienia SMTP"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr ""

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Błąd bazy danych: %(error)s."

#: cps/admin.py:1344
#, fuzzy, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "Testowy e-mail czeka w kolejce do wysłania do %(email)s, sprawdź zadania, aby uzyskać wynik"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Wystąpił błąd podczas wysyłania e-maila testowego: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Najpierw skonfiguruj swój adres e-mail..."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "Zaktualizowano ustawienia serwera poczty e-mail"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr ""

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr ""

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr ""

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr ""

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "Wystąpił nieznany błąd. Spróbuj ponownie później."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr ""

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Edytuj użytkownika %(nick)s"

# ???
#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Zrestartowano hasło użytkownika %(user)s"

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Proszę najpierw skonfigurować ustawienia SMTP poczty e-mail..."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Przeglądanie dziennika"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Żądanie o pakiet aktualizacji"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Pobieranie pakietu aktualizacji"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Rozpakowywanie pakietu aktualizacji"

# ???
#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Zastępowanie plików"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Połączenia z bazą danych zostały zakończone"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Zatrzymywanie serwera"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Aktualizacja zakończona, proszę nacisnąć OK i odświeżyć stronę"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Aktualizacja nieudana:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "Błąd HTTP"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Błąd połączenia"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Przekroczono limit czasu podczas nawiązywania połączenia"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Błąd ogólny"

#: cps/admin.py:1551
#, fuzzy
msgid "Update file could not be saved in temp dir"
msgstr "Plik aktualizacji nie mógł zostać zapisany w katalogu tymczasowym"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr ""

#: cps/admin.py:1576
#, fuzzy
msgid "Failed to extract at least One LDAP User"
msgstr "Błąd przy tworzeniu przynajmniej jednego użytkownika LDAP"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Błąd przy tworzeniu przynajmniej jednego użytkownika LDAP"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Błąd: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Błąd. LDAP nie zwrócił żadnego użytkownika"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Przynajmniej jeden użytkownik LDAP nie został znaleziony w bazie danych"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} Użytkownik pomyślnie zaimportowany"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "Lokalizacja bazy danych jest nieprawidłowa, wprowadź poprawną ścieżkę"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "Baza danych nie jest zapisywalna"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokalizacja pliku klucza jest nieprawidłowa, wprowadź poprawną ścieżkę"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokalizacja pliku certyfikatu jest nieprawidłowa, wprowadź poprawną ścieżkę"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr ""

#: cps/admin.py:1917
#, fuzzy
msgid "Database Settings updated"
msgstr "Zaktualizowano ustawienia serwera poczty e-mail"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "Konfiguracja bazy danych"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "Proszę wypełnić wszystkie pola!"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "E-mail nie pochodzi z prawidłowej domeny"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Dodaj nowego użytkownika"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "Użytkownik '%(user)s' został utworzony"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "Znaleziono istniejące konto dla tego adresu e-mail lub nazwy."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "Użytkownik '%(nick)s' został usunięty"

#: cps/admin.py:2005
#, fuzzy
msgid "Can't delete Guest User"
msgstr "Nie można usunąć użytkownika gościa"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Nie można usunąć użytkownika. Brak na serwerze innego konta z prawami administratora"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr ""

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "Użytkownik '%(nick)s' został zaktualizowany"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Szukaj"

#: cps/converter.py:31
msgid "not installed"
msgstr "nie zainstalowane"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Brak uprawnienia do wykonywania pliku"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Brak"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Wysłano plik %(file)s"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Brak formatu źródłowego lub docelowego do konwersji"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Książka została pomyślnie umieszczona w zadaniach do konwersji %(book_format)s"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Podczas konwersji książki wystąpił błąd: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Błąd otwierania e-booka. Plik nie istnieje lub jest niedostępny"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr ""

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "W identyfikatorach nie jest rozróżniana wielkość liter, nadpisywanie starego identyfikatora"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, fuzzy, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "%(langname)s nie jest prawidłowym językiem"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "Metadane zostały pomyślnie zaktualizowane"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr ""

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Wysłana książka prawdopodobnie istnieje w bibliotece, rozważ zmianę przed przesłaniem nowej: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "Rozszerzenie pliku '%(ext)s' nie jest dozwolone do wysłania na ten serwer"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "Rozszerzenie pliku '%(ext)s' nie jest dozwolone do wysłania na ten serwer"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Plik do wysłania musi mieć rozszerzenie"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Nie można zapisać pliku %(filename)s w katalogu tymczasowym"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Nie udało się przenieść pliku okładki %(file)s:%(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Plik książki w wybranym formacie został usunięty"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Książka została usunięta"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr ""

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "edytuj metadane"

#: cps/editbooks.py:1016
#, fuzzy, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "%(seriesindex)s nie jest poprawną liczbą, pomijanie"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr ""

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Nie udało się utworzyć łącza %(path)s (Odmowa dostępu)."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Nie można zapisać pliku %(file)s."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Format pliku %(ext)s dodany do %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Konfiguracja Google Drive nie została zakończona, spróbuj dezaktywować i ponownie aktywować Google Drive"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Zwrotna domena nie jest zweryfikowana, proszę zweryfikowania domenę w konsoli deweloperskiej google"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "Nie znaleziono formatu %(format)s dla id książki: %(book)d"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "Nie znaleziono %(format)s na Google Drive: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s nie znaleziono: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
#, fuzzy
msgid "Send to eReader"
msgstr "Wyślij do Kindle"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "Ten e-mail został wysłany za pośrednictwem Calibre-Web."

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Testowy e-mail Calibre-Web"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "Testowy e-mail"

# ???
#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Zacznij korzystać z Calibre-Web"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "Rejestracja e-mail dla użytkownika: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "Konwertuj %(orig)s do %(format)s i wyślij do Kindle"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "Wyślij %(format)s do Kindle"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "Wyślij do Kindle"

# ???
#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Żądany plik nie mógł zostać odczytany. Może brakuje uprawnień?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr ""

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Usuwanie folderu książki dla książki %(id)s nie powiodło się, ścieżka ma podfoldery: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Usuwanie książki %(id)s zakończyło się błędem: %(message)s"

#: cps/helper.py:392
#, fuzzy, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Usuwanie książki %(id)s, ścieżka książki jest niepoprawna: %(path)s"

#: cps/helper.py:439
#, fuzzy, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Zmiana nazwy tytułu z: „%(src)s” na „%(dest)s” zakończyła się błędem: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Nie znaleziono pliku %(file)s na Google Drive"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Zmiana nazwy tytułu z: „%(src)s” na „%(dest)s” zakończyła się błędem: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Nie znaleziono ścieżki do książki %(path)s na Google Drive"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr ""

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Nazwa użytkownika jest już zajęta"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "Nieprawidłowy format adresu e-mail"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr ""

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr ""

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Błąd przy pobieraniu okładki"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Błędny format okładki"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr ""

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Nie udało się utworzyć ścieżki dla okładki"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Plik okładki nie jest poprawnym plikiem obrazu lub nie mógł zostać zapisany"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Jako plik okładki obsługiwane są tylko pliki jpg/jpeg/png/webp/bmp"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr ""

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Jako plik okładki dopuszczalne są jedynie pliki jpg/jpeg"

#: cps/helper.py:989 cps/helper.py:1149
#, fuzzy
msgid "Cover"
msgstr "Odkrywaj"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "Plik wykonywalny programu unrar nie znaleziony"

#: cps/helper.py:1017
#, fuzzy
msgid "Error executing UnRar"
msgstr "Błąd przy wykonywaniu unrar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "Baza danych nie jest zapisywalna"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Brak uprawnienia do wykonywania pliku"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "Błąd przy wykonywaniu unrar"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr ""

#: cps/kobo_auth.py:92
#, fuzzy
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Aby uzyskać prawidłowy api_endpoint dla urządzenia Kobo, należy skorzystać z dostępu do calibre-web spoza localhost"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Konfiguracja Kobo"

# ???
#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Zarejestruj się %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "zalogowałeś się jako: '%(nickname)s'"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Łączenie z %(oauth)s zakończono sukcesem"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Błąd logowania, użytkownik niepołączony z kontem OAuth"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Rozłączanie z %(oauth)s zakończono sukcesem"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Rozłączanie z %(oauth)s zakończono porażką"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Nie połączono z %(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Nie udało się zalogować za pomocą GitHub."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Nie udało się pobrać informacji o użytkowniku z GitHub."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Nie udało się zalogować do Google."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Nie udało się pobrać informacji o użytkowniku z Google."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "Błąd GitHub Oauth, proszę spróbować później."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "Błąd GitHub Oauth: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Błąd Google Oauth, proszę spróbować później."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Błąd Google Oauth: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} Gwiazdek"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Zaloguj się"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Nie znaleziono tokenu"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Token wygasł"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Powodzenie! Wróć do swojego urządzenia"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Książki"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Pokaż menu ostatnio dodanych książek"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Najpopularniejsze"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Pokaż menu najpopularniejszych książek"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Pobrane książki"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Pokaż pobrane książki"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Najwyżej ocenione"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Pokaż menu najwyżej ocenionych książek"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Przeczytane"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "Pokaż menu przeczytane i nieprzeczytane"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Nieprzeczytane"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Pokaż nieprzeczytane"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Odkrywaj"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Pokazuj losowe książki"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Kategorie"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "Pokaż menu wyboru kategorii"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Cykle"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "Pokaż menu wyboru cyklu"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Autorzy"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "Pokaż menu wyboru autora"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Wydawcy"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "Pokaż menu wyboru wydawcy"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Języki"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "Pokaż menu wyboru języka"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Oceny"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "Pokaż menu listy ocen"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Formaty plików"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "Pokaż menu formatu plików"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Zarchiwizowane książki"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "Pokaż zarchiwizowane książki"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Lista książek"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Pokaż listę książek"

#: cps/search.py:201
msgid "Published after "
msgstr "Opublikowane po "

#: cps/search.py:208
msgid "Published before "
msgstr "Opublikowane przed "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Ocena <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Ocena >= %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "Status przeczytania = %(status)s"

#: cps/search.py:351
#, fuzzy
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "Błąd podczas wyszukiwania kolumn niestandardowych, proszę zrestartować Calibre-Web"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Wyszukiwanie"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Podano niewłaściwą półkę"

#: cps/shelf.py:55
#, fuzzy
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Niestety, nie posiadasz uprawnień do dodania książki do półki: %(shelfname)s"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Książka jest już dodana do półki: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Książka została dodana do półki: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr ""

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "Książki są już dodane do półki: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "Książki zostały dodane do półki %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Nie można dodać książek do półki: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Książka została usunięta z półki: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr ""

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Utwórz półkę"

#: cps/shelf.py:226
#, fuzzy
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Niestety nie możesz usunąć książki z tej półki %(sname)s"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Edytuj półkę"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr ""

#: cps/shelf.py:239
#, fuzzy
msgid "Shelf successfully deleted"
msgstr "Książka została usunięta"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Zmieniono kolejność półki: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr ""

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Półka %(title)s została utworzona"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Półka %(title)s została zmieniona"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "Wystąpił błąd"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "Publiczna półka o nazwie '%(title)s' już istnieje."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "Prywatna półka o nazwie '%(title)s' już istnieje."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Półka: „%(name)s”"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Błąd otwierania półki. Półka nie istnieje lub jest niedostępna"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Zadania"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Oczekiwanie"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Nieudane"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Rozpoczynanie"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Zakończone"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr ""

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr ""

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Ststus nieznany"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Nieoczekiwane dane podczas odczytywania informacji o aktualizacji"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Brak dostępnej aktualizacji. Masz już zainstalowaną najnowszą wersję"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "Dostępna jest nowa aktualizacja. Kliknij przycisk poniżej, aby zaktualizować do najnowszej wersji."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "Nie można pobrać informacji o aktualizacji"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Kliknij przycisk poniżej, aby zaktualizować do najnowszej stabilnej wersji."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "Dostępna jest nowa aktualizacja. Kliknij przycisk poniżej, aby zaktualizować do wersji: %(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Brak dostępnych informacji o wersji"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Odkrywaj (losowe książki)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Najpopularniejsze książki (najczęściej pobierane)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Książki pobrane przez %(user)s"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Autor: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Wydawca: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Cykl: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr ""

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Ocena: %(rating)s gwiazdek"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Format pliku: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Kategoria: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Język: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "DLS"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Lista z ocenami"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Lista formatów"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "Proszę najpierw skonfigurować ustawienia SMTP poczty e-mail..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Książka została umieszczona w kolejce do wysłania do %(eReadermail)s"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Wystąpił błąd podczas wysyłania tej książki: %(res)s"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Najpierw skonfiguruj adres e-mail Kindle..."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr ""

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Zarejestruj się"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "Serwer e-mail nie jest skonfigurowany, skontaktuj się z administratorem!"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "Serwer e-mail nie jest skonfigurowany, skontaktuj się z administratorem!"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "Twój e-mail nie może się zarejestrować"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "Wiadomość e-mail z potwierdzeniem została wysłana na Twoje konto e-mail."

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "Nie można aktywować uwierzytelniania LDAP"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr ""

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "zalogowałeś się jako: '%(nickname)s'"

#: cps/web.py:1415
#, fuzzy, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "Fallback Login as: %(nickname)s, LDAP Server not reachable, or user not known"

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "Nie można zalogować: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "Błędna nazwa użytkownika lub hasło"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "Nowe hasło zostało wysłane na Twój adres e-mail"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "Wystąpił nieznany błąd. Spróbuj ponownie później."

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "Wprowadź prawidłową nazwę użytkownika, aby zresetować hasło"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "zalogowałeś się jako: '%(nickname)s'"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "Profil użytkownika %(name)s"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "Zaktualizowano profil"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "Znaleziono istniejące konto dla tego adresu e-mail"

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Nie znaleziono poprawnego pliku gmail.json z informacjami OAuth"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, fuzzy, python-format
msgid "%(book)s send to E-Reader"
msgstr "Wyślij do Kindle"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Nie znaleziono narzędzia calibre %(tool)s do konwertowania"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "Nie znaleziono na dysku formatu %(format)s"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "Konwertowanie ebooka zakończyło się niepowodzeniem z nieznanego powodu"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify-converter spowodowało błąd: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "Konwertowany plik nie został znaleziony, lub więcej niż jeden plik w folderze %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, fuzzy, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre nie powiodło się z błędem: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "Konwertowanie nie powiodło się: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr ""

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr ""

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr ""

#: cps/tasks/metadata_backup.py:34
#, fuzzy
msgid "Backing up Metadata"
msgstr "edytuj metadane"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr ""

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Wysyłanie"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Lista użytkowników"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Nazwa użytkownika"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "E-mail"

#: cps/templates/admin.html:15
#, fuzzy
msgid "Send to eReader Email"
msgstr "Adres e-mail dla wysyłania do Kindle"

# ???
#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Panel administratora"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Hasło"

# ???
#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "Pobieranie"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Przeglądanie"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Edycja"

# ???
#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Usuń"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Półka publiczna"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "Importuj użytkowników LDAP"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "Ustawienia serwera e-mail SMTP"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "Adres serwera SMTP"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "Port serwera SMTP"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "SSL"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "Nazwa użytkownika SMTP"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Wyślij z adresu e-mail"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "Usługa e-mail"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail przez Oauth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Konfiguracja"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Folder bazy danych Calibre"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Poziom dziennika"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Port"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Port zewnętrzny"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Liczba książek na stronie"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Wysyłanie"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Anonimowe przeglądanie"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Publiczna rejestracja"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Zdalne logowanie (Magic Link)"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Logowanie reverse proxy"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Nazwa nagłowka reverse proxy"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Edytuj konfigurację bazy danych Calibre"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Edytuj podstawową konfigurację"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Edytuj konfigurację interfejsu"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr ""

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr ""

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr ""

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr ""

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr ""

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr ""

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr ""

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr ""

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Zarządzanie"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Pobierz pakiet Debug"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Podgląd dziennika"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Uruchom ponownie Calibre Web"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Zatrzymaj Calibre Web"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr ""

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Wersja"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Szczegóły"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "Bieżąca wersja"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Sprawdź aktualizacje"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Wykonaj aktualizację"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Na pewno chcesz uruchomić ponownie Calibre Web?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "OK"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Anuluj"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Na pewno chcesz zatrzymać Calibre Web?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Aktualizowanie, proszę nie odświeżać strony"

#: cps/templates/author.html:15
msgid "via"
msgstr "przez"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "W Bibliotece"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Sortuj książki według daty, najnowsze jako pierwsze"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Sortuj książki według daty, najstarsze jako pierwsze"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Sortuj tytuły w porządku alfabetycznym"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Sortuj tytuły w odwrotnym porządku alfabetycznym"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Sortuj według daty publikacji, najnowsze jako pierwsze"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Sortuj według daty publikacji, najstarsze jako pierwsze"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "zwiń"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Więcej według"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, fuzzy, python-format
msgid "Book %(index)s of %(range)s"
msgstr ""

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Język"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Wydawca"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "Data publikacji"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "Opis:"

# ???
#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "Poprzedni"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "Następne"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "Brak wyników"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "Główne menu"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "Przeszukaj bibliotekę"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "Wyloguj się"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Usuń książkę"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Usuń formaty:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "Konwertuj format książki:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Konwertuj z:"

# ???
#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "wybierz opcję"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Konwertuj na:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Konwertuj książkę"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "Wysyłanie…"

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Zamknij"

# ???
#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "Błąd"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "Wysyłanie zakończone, przetwarzanie, proszę czekać…"

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Wyślij format"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Tytuł książki"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Autor"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Etykiety"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "ID cyklu"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Data publikacji"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Ocena"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Opis"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "Identyfikatory"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "Rodzaj identyfikatora"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "Wartość identyfikatora"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Usuń"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "Dodaj identyfikator"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Pobierz okładkę z linku (JPEG - obraz zostanie pobrany i zapisany w bazie)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Wyślij okładkę z dysku lokalnego"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Tak"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "Nie"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Po zapisaniu wyświetl szczegóły książki"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Uzyskaj metadane"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Zapisz"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Słowo kluczowe"

#: cps/templates/book_edit.html:240
#, fuzzy
msgid "Search keyword"
msgstr " Szukaj słowa kluczowego "

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Kliknij okładkę, aby załadować metadane do formularza"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Ładowanie..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Źródło"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Błąd wyszukiwania!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Nie znaleziono! Spróbuj użyć innego słowa kluczowego."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "To pole jest wymagane"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Łączenie wybranych książek"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Usuń zaznaczone"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "Zamień autora i tytuł"

#: cps/templates/book_table.html:47
#, fuzzy
msgid "Update Title Sort automatically"
msgstr "Aktualizuj tytuł Sortuj automatycznie"

#: cps/templates/book_table.html:51
#, fuzzy
msgid "Update Author Sort automatically"
msgstr "Automatyczna aktualizacja sortowania autorów"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Wpisz tytuł"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Tytuł"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Wprowadź tytuł sortowania"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Tytuł sortowania"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Wpisz autora sortowania"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Autor sortowania"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Wpisz autorów"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Wprowadź kategorie"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Wpisz serię"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Indeks serii"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Wprowadź języki"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Data publikacji"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Wpisz Wydawnictwa"

#: cps/templates/book_table.html:73
#, fuzzy
msgid "Enter comments"
msgstr "Podaj nazwę domeny"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr ""

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr ""

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Status odczytu"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
#, fuzzy
msgid "Enter "
msgstr "Identyfikatory"

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Czy jesteś pewny?"

#: cps/templates/book_table.html:117
#, fuzzy
msgid "Books with Title will be merged from:"
msgstr "Książki z tytułem będą łączone z:"

#: cps/templates/book_table.html:121
#, fuzzy
msgid "Into Book with Title:"
msgstr "Into Book with Title:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Połącz"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Lokalizacja bazy danych Calibre"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Użyć dysku Google?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Uwierzytelnij Dysk Google"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Folder Calibre na Google Drive"

# ???
#: cps/templates/config_db.html:52
#, fuzzy
msgid "Metadata Watch Channel ID"
msgstr "Metadane Watch Channel ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Unieważnij"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "Nowa lokalizacja bazy danych jest nieprawidłowa, proszę podać prawidłową ścieżkę"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Konfiguracja serwera"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Port serwera"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "Lokalizacja certyfikatu SSL (pozostaw puste jeśli bez SSL)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "Lokalizacja klucza SSL (pozostaw puste jeśli bez SSL)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Kanał aktualizacji"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Wersje stabilne"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Wydania nocne"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr ""

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Konfiguracja dziennika"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Lokalizacja i nazwa pliku dziennika (domyślnie calibre-web.log)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Włącz dziennik dostępu (access log)"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Lokalizacja i nazwa pliku dziennika dostepu (domyślnie access.log)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Konfiguracja funkcjonalności"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr ""

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Włącz wysyłanie"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr ""

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Dozwolone formaty przesyłania"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Włącz anonimowe przeglądanie"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Włącz publiczną rejestrację"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "Użyj e-maila jako nazwy użytkownika"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Włącz zdalne logowanie („Magic Link”)"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Włącz synchronizację Kobo"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Nieznane żądania proxy do Kobo Store"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Zewnętrzny port serwera (dla połączeń API przekierowanych przez port)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Użyj Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Klucz API Goodreads"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Zezwalaj na uwierzytelnianie reverse proxy"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Rodzaj logowania"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Użyj standardowego uwierzytelnienia"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Użyj uwierzytelniania LDAP"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Uzyj OAuth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "Nazwa hosta lub adres IP serwera LDAP"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "Port serwera LDAP"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "Szyfrowanie LDAP"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "Ścieżka certyfikatu urzędu certyfikacji LDAP (wymagana tylko dla uwierzytelniania certyfikatu klienta)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "Ścieżka certyfikatu LDAP (potrzebna tylko do uwierzytelniania certyfikatem klienta)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "Ścieżka pliku klucza LDAP (wymagana tylko dla uwierzytelniania certyfikatem klienta)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "Uwierzytelnianie LDAP"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anonim"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Nieuwierzytelniony"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Proste"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "Nazwa administratora LDAP"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "Hasło administratora LDAP"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP Distinguished Name (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP User Object Filter"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "Serwer LDAP to OpenLDAP?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "Następujące ustawienia są niezbędne dla zaimportowania użytkowników"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "Filtr obiektów grupy LDAP"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "Nazwa grupy LDAP"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "Pola członków grupy LDAP"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "Wykrywanie filtra użytkownika członka LDAP"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "Autodetekcja"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "Filtr niestandardowy"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "Filtr użytkownika członka LDAP"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "Uzyskaj %(provider)s OAuth Credential"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth Client Id"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth Client Secret"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "Zewnętrzne pliki"

#: cps/templates/config_edit.html:325
#, fuzzy
msgid "Path to Calibre Binaries"
msgstr "Ścieżka do konwertera Calibre"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Ustawienia konwertera calibre"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Ścieżka do konwertera Kepubify"

#: cps/templates/config_edit.html:344
#, fuzzy
msgid "Location of Unrar binary"
msgstr "Lokalizacja pliku binarnego UnRar"

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "Ustawienia OAuth"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr ""

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr ""

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr ""

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr ""

#: cps/templates/config_edit.html:393
#, fuzzy
msgid "User Password policy"
msgstr "Zresetuj hasło użytkownika"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr ""

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr ""

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr ""

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr ""

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr ""

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "Konfiguracja widoku"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "Liczba losowych książek do pokazania"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "Liczba autorów do pokazania przed ukryciem (0=wyłącza ukrywanie)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "Motyw"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "Motyw standardowy"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "Motyw caliBlur! Dark"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "Wyrażenie regularne dla ignorowanych kolumn"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "Link do statusu Przeczytanych/Nieprzeczytanych książek z kolumny Calibre (własna kolumna)"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Ograniczenie przeglądania w oparciu o kolumnę Calibre"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "Wyrażenie regularne dla tytułu sortującego"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "Domyślne ustawienia dla nowych użytkowników"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "Użytkownik z uprawnieniami administratora"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "Zezwalaj na pobieranie"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "Zezwalaj na przeglądanie e-booków"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "Zezwalaj na wysyłanie"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "Zezwalaj na edycję"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "Zezwalaj na usuwanie książek"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "Zezwalaj na zmianę hasła"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "Zezwalaj na edycję półek publicznych"

#: cps/templates/config_view_edit.html:123
#, fuzzy
msgid "Default Language"
msgstr "Wyklucz języki"

#: cps/templates/config_view_edit.html:131
#, fuzzy
msgid "Default Visible Language of Books"
msgstr "Pokaż książki w języku"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "Domyślne ustawienia widoku dla nowych użytkowników"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "Pokaz losowe książki w widoku szczegółowym"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "Dodaj dozwolone/zabronione etykiety"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "Dodaj dozwolone/zabronione wartości własnych kolumn"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "Czytaj w przeglądarce"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "Słuchaj w przeglądarce"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "Oznacz jako nieprzeczytane"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "Oznacz jako przeczytane"

#: cps/templates/detail.html:262
#, fuzzy
msgid "Mark Book as Read or Unread"
msgstr "Oznacz jako nieprzeczytane"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "Przeczytana"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "Przywróć z archiwum"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "Dodaj do archiwum"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "Zarchiwizowane"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "Dodaj do półki"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(publiczna)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "Edytuj metadane"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "Wybierz typ serwera"

#: cps/templates/email_edit.html:15
#, fuzzy
msgid "Standard Email Account"
msgstr "Użyj standardowego konta e-mail"

#: cps/templates/email_edit.html:16
#, fuzzy
msgid "Gmail Account"
msgstr "Wybierz typ serwera"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Cofnij dostęp do Gmaila"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "Hasło SMTP"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "Limit rozmiaru załącznika"

#: cps/templates/email_edit.html:66
#, fuzzy
msgid "Save and Send Test Email"
msgstr "Zapisz ustawienia i wyślij testową wiadomość e-mail"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "Wróć"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "Domeny dozwolone do rejestracji (biała lista)"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "Dodaj domenę"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "Dodaj"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "Podaj nazwę domeny"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "Domeny zabronione (czarna lista)"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "Otwórz plik .kobo/Kobo/Kobo eReader.conf w edytorze tekstu i dodaj (lub edytuj):"

#: cps/templates/generate_kobo_auth_url.html:11
#, fuzzy
msgid "Kobo Token:"
msgstr "Token Kobo Sync"

#: cps/templates/grid.html:21
msgid "List"
msgstr ""

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "Instancja Calibre-Web jest nieskonfigurowana, proszę skontaktować się z administratorem"

# | msgid "Create a Shelf"
#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Zgłoś błąd"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "Konfiguracja bazy danych"

# ???
#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "Powrót do głównego menu"

#: cps/templates/http_error.html:57
#, fuzzy
msgid "Logout User"
msgstr "Wyloguj użytkownika"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr ""

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr ""

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "Sortuj autorów w porządku alfabetycznym"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "Posortuj autorów w odwrotnym porządku alfabetycznym"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "Sortuj rosnąco według indeksu serii"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "Sortuj malejąco według indeksu serii"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "Rozpocznij"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "Książki alfabetyczne"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "Książki uporządkowane alfabetycznie"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "Popularne publikacje z tego katalogu bazujące na pobranych."

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "Popularne publikacje z tego katalogu bazujące na ocenach."

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "Ostatnio dodane książki"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "Ostatnie książki"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "Losowe książki"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "Książki sortowane według autorów"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "Książki sortowane według wydawców"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "Książki sortowane według kategorii"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "Książki sortowane według cyklu"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "Ksiązki sortowane według języka"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "Książki sortowane według oceny"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "Ksiązki sortowane według formatu"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "Półki"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "Książki ułożone na półkach"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "Przełącz nawigację"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "Proste"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "Konto"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "Ustawienia"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "Proszę nie odświeżać strony"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "Przeglądaj"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "Informacje"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "Szczegóły książki"

#: cps/templates/list.html:22
msgid "Grid"
msgstr ""

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "Zarchiwizowane"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "Zapamiętaj mnie"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "Zapomniałeś hasło?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "Zaloguj się za pomocą „magic link”"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Pokaż dziennik Calibre-Web: "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Dziennik Calibre-Web: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "Nie można wyświetlić wyjścia"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "Pokaż dziennik dostępu: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Pobierz log Calibre-Web"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "Pobierz log dostępu"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "Wybierz dozwolone/zabronione etykiety"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "Wybierz dozwolone/zabronione wartości własnych kolumn"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "Wybierz dozwolone/zabronione etykiety użytkownika"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "Wybierz dozwolone/zabronione wartości własnych kolumn użytkownika"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "Wpisz etykietę"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "Dodaj ograniczenie przeglądania"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "Ten format książki zostanie trwale usunięty z bazy danych"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "Książka zostanie usunięta z bazy danych Calibre"

# ???
#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "i z dysku twardego"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Ważne dla Kobo: usunięte książki pozostaną na każdym połączonym urządzeniu Kobo."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "Książki muszą najpierw zostać zarchiwizowane a urządzenie zsynchronizowane, zanim książka będzie mogła zostać bezpiecznie usunięta."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "Wybierz lokalizację pliku"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "typ"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "nazwa"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "rozmiar"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "Główny katalog"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "Wybierz"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "OK"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Katalog e-booków Calibre-Web"

#: cps/templates/read.html:7
#, fuzzy
msgid "epub Reader"
msgstr "Czytnik PDF"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "Wybierz nazwę użytkownika"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "Jasny"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "Ciemny"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr ""

#: cps/templates/read.html:90
#, fuzzy
msgid "Black"
msgstr "Wróć"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "Przepływ tekstu, gdy paski boczne są otwarte."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr ""

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

# ???
#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "Usuń"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "Oczekiwanie"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "Pionowo"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "Przeczytana"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "Nieprawidłowa kolumna odczytu"

#: cps/templates/readcbr.html:8
#, fuzzy
msgid "Comic Reader"
msgstr "Czytnik PDF"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "Skróty klawiaturowe"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "Poprzednia strona"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "Następna strona"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr ""

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "Skaluj do najlepszego"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "Skaluj do szerokości"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "Skaluj do wysokości"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "Skaluj do wielkości oryginalnej"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "Obróć w prawo"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "Obróć w lewo"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "Odwórć obraz"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr ""

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "Panel administratora"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "Skaluj"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "Najlepszy"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "Szerokość"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "Wysokość"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "Natywnie"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "Obrót"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "Odwróć"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "Poziomo"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "Pionowo"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "Kierunek"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "Od lewej do prawej"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "Od prawej do lewej"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr ""

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr ""

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "Pasek przewijania"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "Pokaż"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "Ukryj"

#: cps/templates/readdjvu.html:5
#, fuzzy
msgid "DJVU Reader"
msgstr "Czytnik PDF"

#: cps/templates/readpdf.html:31
#, fuzzy
msgid "PDF Reader"
msgstr "Czytnik PDF"

#: cps/templates/readtxt.html:6
#, fuzzy
msgid "txt Reader"
msgstr "Czytnik PDF"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "Zarejestruj nowe konto"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "Wybierz nazwę użytkownika"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "Twój adres e-mail"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "Magic Link – Autoryzuj nowe urządzenie"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "Użyj innego urządzenia, zaloguj się i odwiedź:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "Gdy to zrobisz, automatycznie zalogujesz się na tym urządzeniu."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "Link wygaśnie po 10 minutach."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr ""

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "Wyszukiwano:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "Wyniki dla:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "Data publikacji od"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "Data publikacji do"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "Wyklucz etykiety"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "Wyklucz cykle"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "Z wyłączeniem półek"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "Wyklucz języki"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "Rozszerzenia"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "Wyklucz rozszerzenia"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "Ocena większa niż"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "Ocena mniejsza niż"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "Od:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "Do:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "Usuń tą półkę"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "Edytuj właściwości półki"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "Ręczne porządkowanie książek"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "Wyłączenie Zlecenie zmiany"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "Włącz polecenie zmiany"

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "Sortuj książki według daty, najnowsze jako pierwsze"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "Sortuj książki według daty, najstarsze jako pierwsze"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "Współdziel z wszystkimi"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "Zsynchronizuj tę półkę z urządzeniem Kobo"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "Przeciągnij i upuść, aby zmienić kolejność"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "Ukryta książka"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "Statystyki biblioteki Calibre"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "Książki"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "Autorzy"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "Kategorie"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "Cykle w tej bibliotece"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "Statystyki systemu"

#: cps/templates/stats.html:33
msgid "Program"
msgstr ""

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "Zainstalowana wersja"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "Użytkownik"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "Zadanie"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "Status"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "Postęp"

# ???
#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "Czas wykonania"

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "Połącz"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr ""

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr ""

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr ""

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "Zresetuj hasło użytkownika"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "Pokaż książki w języku"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "Ustawienia OAuth"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "Połącz"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "Rozłącz"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Token Kobo Sync"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "Utwórz/Przeglądaj"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr ""

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "Dodaj dozwolone/zabronione wartości własnych kolumn"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "Synchronizuj z Kobo tylko książki z wybranych półek"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "Usuń tego użytkownika"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Generuj Kobo Auth URL"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "Wybierz..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "Edytuj użytkownika"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "Wprowadź nazwę użytkownika"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "Testowy e-mail"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "Enter eReader Email"
msgstr "Adres e-mail dla wysyłania do Kindle"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "eReader Email"
msgstr "Testowy e-mail"

#: cps/templates/user_table.html:137
#, fuzzy
msgid "Locale"
msgstr "Ustawienia regionalne"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "Widoczne języki książek"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "Edytuj dozwolone tagi"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "Dozwolone Tagi"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "Edytuj zabronione etykiety"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "Zabronione etykiety"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "Edycja dozwolonych wartości kolumn"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "Dozwolone wartości kolumn"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "Edycja zabronionych wartości kolumn"

#: cps/templates/user_table.html:142
#, fuzzy
msgid "Denied Column Values"
msgstr "Wartości kolumn odmowy"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "Zmień hasło"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "Zobacz"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "Edytuj półki publiczne"

#: cps/templates/user_table.html:152
#, fuzzy
msgid "Sync selected Shelves with Kobo"
msgstr "Zsynchronizuj wybrane półki z Kobo"

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "Pokaż wybór przeczytane/nieprzeczytane"

