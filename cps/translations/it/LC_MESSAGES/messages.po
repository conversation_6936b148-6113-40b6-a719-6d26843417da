# Traduzione in italiano per Calibre-Web.
# Copyright (C) 2016 Smart Cities Community
# This file is distributed under the same license as the Calibre-Web
# <AUTHOR> <EMAIL>, 2016.
# SPDX-FileCopyrightText: 2023, 2024, 2025 <PERSON><PERSON> <<EMAIL>>
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2025-04-15 00:50+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language: it\n"
"Language-Team: Italian <>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statistiche"

#: cps/admin.py:151
msgid "Server restarted, please reload page."
msgstr "Server riavviato, ricarica la pagina."

#: cps/admin.py:153
msgid "Performing Server shutdown, please close window."
msgstr "Esecuzione dell'arresto del server, chiudi la finestra."

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr "Tutto OK! Database riconnesso"

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Comando sconosciuto"

#: cps/admin.py:175
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Tutto OK! Libri in coda per il backup dei metadati, controlla le attività per il risultato"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Sconosciuto"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Pagina di amministrazione"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Configurazione di base"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "Configurazione dell'interfaccia utente"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "La colonna personalizzata no.%(column)d non esiste nel database di Calibre"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "Modifica utenti"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Tutti"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Utente non trovato"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "utenti eliminati correttamente"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Mostra tutto"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Richiesta non valida"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Il nome dell'utente Guest (ospite) non può essere modificato"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "L'utente Guest (ospite) non può avere questo ruolo"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Non rimarrebbe nessun utente amministratore, non è possibile rimuovere il ruolo di amministratore"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Il valore deve essere vero o falso"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Ruolo non valido"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "L'utente Guest (ospite) non può visualizzare questa schermata"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "Visualizzazione non valida"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Le impostazioni locali dell'utente Guest (ospite) sono determinate automaticamente e non possono essere configurate"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Nessuna lingua valida specificata"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Nessun libro valido per la lingua specificata"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Parametro non trovato"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "Colonna di lettura non valida"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "Colonna con restrizioni non valida"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "La configurazione di Calibre-Web è stata aggiornata"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Vuoi veramente eliminare il token di Kobo?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Vuoi veramente eliminare questo dominio?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Vuoi veramente eliminare questo utente?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Sei sicuro di voler eliminare questo scaffale?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Sei sicuro di voler cambiare le impostazioni internazionali degli utenti selezionati?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Sei sicuro di voler cambiare le lingue visibili del libro per gli utenti selezionati?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Sei sicuro di voler cambiare il ruolo selezionato per gli utenti selezionati?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Sei sicuro di voler cambiare le restrizioni selezionate per gli utenti selezionati?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Sei sicuro di voler cambiare le restrizioni di visibilità selezionate per gli utenti selezionati?"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Sei sicuro di voler cambiare il comportamento di sincronizzazione dello scaffale per gli utenti selezionati?"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Sei sicuro di voler cambiare la posizione della biblioteca di Calibre?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-Web cercherà le copertine aggiornate e aggiornerà le miniature delle copertine, ma ci vorrà un po' di tempo."

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Sei sicuro di voler eliminare il database sincronizzato di Calibre-Web e forzare una sincronizzazione completa con il tuo lettore Kobo?"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Nega"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Consenti"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{} voci di sincronizzazione eliminate"

#: cps/admin.py:987
msgid "Tag not found"
msgstr "Etichetta non trovata"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Azione non valida"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json non è configurato per Web Application"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "La posizione del file di log non è valida, per favore indica il percorso corretto"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "La posizione del file del log di accesso non è valida, indica il percorso corretto"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Inserisci un provider LDAP, una porta, un DN e un identificatore oggetto utente"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Inserisci un account e una password del servizio LDAP"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "Inserisci un account di servizio LDAP"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "Il filtro oggetto gruppo LDAP deve avere un identificatore di formato \"%s\""

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "Il filtro oggetto gruppo LDAP ha parentesi senza corrispondenza"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "Il filtro oggetto utente LDAP deve avere un identificatore di formato \"%s\""

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "Il filtro oggetto utente LDAP ha parentesi senza corrispondenza"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "Il filtro utente membro LDAP deve avere un identificatore di formato \"%s\""

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "Il filtro utente membro LDAP ha parentesi senza corrispondenza"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "Il certificato CA LDAP, il certificato o la posizione della chiave non sono validi. Inserisci il percorso corretto"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Aggiungi nuovo utente"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "Modifica le impostazioni del server e-mail"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr "Tutto OK! Account Gmail verificato."

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Errore nel database: %(error)s."

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "L'e-mail di prova è stato accodata correttamente per essere spedita a %(email)s, controlla il risultato in Attività"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Si è verificato un errore nell'invio dell'e-mail di prova: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Configura prima il tuo indirizzo e-mail..."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "Le impostazioni del server e-mail sono state aggiornate"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "Modifica le impostazioni delle attività pianificate"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "Ora di inizio non valida per l'attività specificata"

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "Durata non valida per l'attività specificata"

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "Impostazioni delle attività pianificate aggiornate"

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "Si è verificato un errore sconosciuto. Per favore riprova più tardi."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "Il DB delle impostazioni non è scrivibile"

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Modifica utente %(nick)s"

#: cps/admin.py:1457
#, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Tutto OK! Password reimpostata per l'utente %(user)s"

#: cps/admin.py:1463
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Per favore configura le impostazioni della posta SMTP."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Visualizzatore del file di log"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Richiesta del pacchetto di aggiornamento"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Download del pacchetto di aggiornamento"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Decompressione del pacchetto di aggiornamento"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Sostituzione dei file"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Le connessioni al database sono chiuse"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Arresto del server"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Aggiornamento terminato, premi OK e ricarica la pagina"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Aggiornamento non riuscito:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "Errore HTTP"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Errore di connessione"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Tempo scaduto nello stabilire la connessione"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Errore generale"

#: cps/admin.py:1551
msgid "Update file could not be saved in temp dir"
msgstr "Il file di aggiornamento non può essere salvato nella cartella temporanea"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "Impossibile sostituire i file durante l'aggiornamento"

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "Impossibile estrarre almeno un utente LDAP"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Impossibile creare almeno un utente LDAP"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Errore: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Errore: nessun utente restituito in risposta dal server LDAP"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Almeno un utente LDAP non è stato trovato nel database"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} utente importato correttamente"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr "Percorso dei libri non valido"

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "La posizione del DB non è valida, per favore indica il percorso corretto"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "Il DB non è scrivibile"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "La posizione del Keyfile non è valida. Inserisci il percorso corretto"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "La posizione del Certfile non è valida, indica il percorso corretto"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr "La lunghezza della password deve essere compresa tra 1 e 40"

#: cps/admin.py:1917
msgid "Database Settings updated"
msgstr "Impostazioni del database aggiornate"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "Configurazione del database"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "Per favore completa tutti i campi."

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "L'e-mail non proviene da un dominio valido"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Aggiungi nuovo utente"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "L'utente '%(user)s' è stato creato"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "Esiste già un account per questa e-mail o nome."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "L'utente '%(nick)s' è stato eliminato"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "Impossibile eliminare l'utente Guest (ospite)"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Non rimarrebbe nessun utente amministratore, non è possibile eliminare l'utente"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr "L'e-mail non può essere vuota e deve essere un'e-mail valida"

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "L'utente '%(nick)s' è stato aggiornato"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Cerca"

#: cps/converter.py:31
msgid "not installed"
msgstr "non installato"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Mancano i permessi di esecuzione"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Nessuna"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Il file %(file)s è stato caricato"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Manca il formato di origine o di destinazione per la conversione"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Libro accodato correttamente per essere convertito in %(book_format)s"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Si è verificato un errore durante la conversione del libro: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Il libro selezionato non è disponibile. Il file non esiste o non è accessibile"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "L'utente non ha i permessi per caricare le copertine"

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "Gli identificatori non fanno distinzione tra maiuscole e minuscole e sovrascrivono il vecchio identificatore"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "%(langname)s non è una lingua valida"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "Metadati aggiornati correttamente"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "Errore nella modifica del libro: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Probabilmente il libro caricato esiste già nella biblioteca, cambialo prima di caricarlo di nuovo:"

#: cps/editbooks.py:755 cps/editbooks.py:1202
msgid "File type isn't allowed to be uploaded to this server"
msgstr "Non è consentito caricare questo tipo di file su questo server"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "Non è consentito caricare l'estensione del file '%(ext)s' su questo server"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Il file da caricare deve avere un'estensione"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Il file %(filename)s non può essere salvato nella cartella temporanea"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Impossibile spostare il file della copertina %(file)s: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Il formato del libro è stato eliminato correttamente"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Il libro è stato eliminato correttamente"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "Ti mancano le autorizzazioni per eliminare i libri"

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "modifica i metadati"

#: cps/editbooks.py:1016
#, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "Indice serie: %(seriesindex)s non è un numero valido, lo salto"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "L'utente non ha i permessi per caricare formati di file aggiuntivi"

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Impossibile creare il percorso %(path)s (autorizzazione negata)."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Impossibile archiviare il file %(file)s."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Formato file %(ext)s aggiunto a %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Configurazione di Google Drive non completata, prova a disattivare e attivare nuovamente Google Drive"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Il dominio di callback non è stato verificato, segui i passaggi per verificare il dominio nella console per sviluppatori di Google"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "Formato %(format)s non trovato per l'ID libro: %(book)d"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "%(format)s non trovato su Google Drive: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s non trovato: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
msgid "Send to eReader"
msgstr "Invia all'eReader"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
msgid "This Email has been sent via Calibre-Web."
msgstr "Questa e-mail è stata inviata tramite Calibre-Web."

#: cps/helper.py:123
msgid "Calibre-Web Test Email"
msgstr "E-mail di prova di Calibre-Web"

#: cps/helper.py:124
msgid "Test Email"
msgstr "E-mail di prova"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Inizia con Calibre-Web"

#: cps/helper.py:146
#, python-format
msgid "Registration Email for user: %(name)s"
msgstr "E-mail di registrazione per l'utente: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "Converti %(orig)s in %(format)s e invia all'eReader"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, python-format
msgid "Send %(format)s to eReader"
msgstr "Invia %(format)s all'eReader"

#: cps/helper.py:230
#, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s inviato all'eReader"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Il file richiesto non può essere letto. I permessi sono corretti?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "Impossibile impostare lo stato di lettura: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Eliminazione della cartella di libri per il libro %(id)s non riuscita, il percorso ha sottocartelle: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Eliminazione del libro %(id)s non riuscita: %(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Eliminazione del libro %(id)s solo dal database, percorso del libro nel database non valido: %(path)s"

#: cps/helper.py:439
#, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "La modifica dell'autore da '%(src)s' a '%(dest)s' è terminata con l'errore: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Il file %(file) non è stato trovato su Google Drive"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "La modifica del titolo da '%(src)s' a '%(dest)s' è terminata con l'errore: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Il percorso del libro %(path)s non è stato trovato su Google Drive"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr "Trovato un account esistente per questo indirizzo e-mail"

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Questo nome utente è già utilizzato"

#: cps/helper.py:679
msgid "Invalid Email address format"
msgstr "Formato dell'indirizzo e-mail non valido"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr "La password non è conforme alle regole di convalida della password"

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "Il modulo Python \"advocate\" non è installato ma è necessario per il caricamento delle copertine"

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Errore nello scaricare la copertina"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Errore nel formato della copertina"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "Non ti è consentito accedere all'host locale o alla rete locale per caricare le copertine"

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Impossibile creare il percorso per la copertina"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Il file della copertina non è in un formato di immagine valido o non può essere salvato"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Solo i file jpg/jpeg/png/webp/bmp sono supportati come file di copertina"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "Contenuto del file di copertina non valido"

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Solo i file jpg/jpeg sono supportati come file di copertina"

#: cps/helper.py:989 cps/helper.py:1149
msgid "Cover"
msgstr "Copertina"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "File binario UnRar non trovato"

#: cps/helper.py:1017
msgid "Error executing UnRar"
msgstr "Errore nell'eseguire UnRar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr "Impossibile trovare la cartella specificata"

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr "Specifica una cartella, non un file"

#: cps/helper.py:1042
msgid "Calibre binaries not viable"
msgstr "Eseguibili di Calibre non validi"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr "File eseguibili di Calibre mancanti: %(missing)s"

#: cps/helper.py:1053
#, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Permessi di esecuzione mancanti: %(missing)s"

#: cps/helper.py:1058
msgid "Error executing Calibre"
msgstr "Errore durante l'esecuzione di Calibre"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr "Metti in coda tutti i libri per il backup dei metadati"

#: cps/kobo_auth.py:92
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Accedi a Calibre-Web da un host non locale per ottenere un api_endpoint valido per il dispositivo Kobo"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Configurazione di Kobo"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Registra con %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "Tutto OK! Ora sei connesso come: %(nickname)s"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Collegamento riuscito a %(oauth)s"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Accesso non riuscito, non c'è nessun utente collegato all'account OAuth"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Scollegamento da %(oauth)s riuscito"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Scollegamento da %(oauth)s non riuscito"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Non collegato a %(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Impossibile accedere con GitHub."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Impossibile recuperare le informazioni dell'utente da GitHub."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Impossibile accedere con Google."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Impossibile recuperare le informazioni dell'utente da Google."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "Errore GitHub Oauth, riprova più tardi."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "Errore GitHub Oauth: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Errore OAuth di Google, riprova più tardi."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Errore OAuth di Google: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} stelle"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Accesso"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Token non trovato"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Il token è scaduto"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Tutto OK! Torna al tuo dispositivo"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Libri"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Mostra Libri recenti"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Libri hot"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Mostra Libri hot"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Libri scaricati"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Mostra Libri scaricati"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Libri più votati"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Mostra Libri più votati"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Libri letti"

#: cps/render_template.py:63
msgid "Show Read and Unread"
msgstr "Mostra Libri letti e Libri da leggere"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Libri da leggere"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Mostra da leggere"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Libri casuali"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Mostra Libri casuali"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Categorie"

#: cps/render_template.py:73 cps/templates/user_table.html:158
msgid "Show Category Section"
msgstr "Mostra sezione Categorie"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Serie"

#: cps/render_template.py:76 cps/templates/user_table.html:157
msgid "Show Series Section"
msgstr "Mostra sezione Serie"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Autori"

#: cps/render_template.py:79 cps/templates/user_table.html:160
msgid "Show Author Section"
msgstr "Mostra sezione Autori"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Editori"

#: cps/render_template.py:83 cps/templates/user_table.html:163
msgid "Show Publisher Section"
msgstr "Mostra sezione Editori"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Lingue"

#: cps/render_template.py:87 cps/templates/user_table.html:155
msgid "Show Language Section"
msgstr "Mostra sezione Lingue"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Valutazioni"

#: cps/render_template.py:90 cps/templates/user_table.html:164
msgid "Show Ratings Section"
msgstr "Mostra sezione Valutazioni"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Formati di file"

#: cps/render_template.py:93 cps/templates/user_table.html:165
msgid "Show File Formats Section"
msgstr "Mostra sezione Formati di file"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Libri archiviati"

#: cps/render_template.py:97 cps/templates/user_table.html:166
msgid "Show Archived Books"
msgstr "Mostra Libri archiviati"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Elenco libri"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Mostra Elenco libri"

#: cps/search.py:201
msgid "Published after "
msgstr "Pubblicato dopo il "

#: cps/search.py:208
msgid "Published before "
msgstr "Pubblicato prima del "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Valutazione <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Valutazione >= %(rating)s"

#: cps/search.py:234
#, python-format
msgid "Read Status = '%(status)s'"
msgstr "Stato di lettura = '%(status)s'"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "Errore nella ricerca delle colonne personalizzate. Per favore riavvia Calibre-Web"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Ricerca avanzata"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Scaffale specificato non valido"

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Spiacente, ma non sei autorizzato ad aggiungere libri a questo scaffale"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Il libro è gia presente nello scaffale: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr "%(book_id)s non è un valido ID libro. Impossibile aggiungerlo allo scaffale"

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Il libro è stato aggiunto allo scaffale: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "Non sei autorizzato ad aggiungere libri allo scaffale"

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "I libri sono già presenti nello scaffale: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "I libri sono stati aggiunti allo scaffale: %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Impossibile aggiungere libri allo scaffale: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Il libro è stato rimosso dallo scaffale: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "Spiacente, ma non sei autorizzato a rimuovere libri da questo scaffale"

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Crea uno scaffale"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Spiacente, ma non sei autorizzato a modificare questo scaffale"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Modifica uno scaffale"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "Errore nell'eliminare lo scaffale"

#: cps/shelf.py:239
msgid "Shelf successfully deleted"
msgstr "Lo scaffale è stato eliminato correttamente"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Cambia l'ordine dello scaffale: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "Spiacente, ma non sei autorizzato a creare scaffali pubblici"

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Lo scaffale %(title)s è stato creato"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Lo scaffale %(title)s è stato modificato"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "C'è stato un errore"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "Esiste già uno scaffale pubblico con il nome '%(title)s'."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "Esiste già uno scaffale privato con il nome '%(title)s'."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Scaffale: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Errore nell'apertura dello scaffale. Lo scaffale non esiste o non è accessibile"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Attività"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Attendi"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Non riuscito"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Avviato"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Finito"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "Terminato"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "Annullato"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Stato sconosciuto"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Dati imprevisti durante la lettura delle informazioni di aggiornamento"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Nessun aggiornamento disponibile. Hai già l'ultima versione installata"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "È disponibile un nuovo aggiornamento. Fai clic sul pulsante in basso per aggiornare all'ultima versione"

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "Impossibile recuperare le informazioni sull'aggiornamento"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Fai clic sul pulsante in basso per eseguire l'aggiornamento all'ultima versione stabile."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "È disponibile un nuovo aggiornamento. Fai clic sul pulsante in basso per aggiornare alla versione:%(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Nessuna informazione disponibile sulla versione"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Libri casuali"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Libri hot (i più scaricati)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Libri scaricati da %(user)s"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Autore: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Editore: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Serie: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "Valutazione: nessuna"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Valutazione: %(rating)s stelle"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Formato file: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Categoria: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Lingua: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "Scaricati"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Elenco valutazioni"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Elenco formati di file"

#: cps/web.py:1259
msgid "Please configure the SMTP mail settings first..."
msgstr "Configura prima le impostazioni della posta SMTP..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Tutto OK! Libro in coda per l'invio a %(eReadermail)s"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Si è verificato un errore durante l'invio del libro: %(res)s"

#: cps/web.py:1270
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Per favore aggiorna il tuo profilo con un'e-mail eReader valida."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr "Attendi un minuto per registrare il prossimo utente"

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Registrati"

#: cps/web.py:1290 cps/web.py:1393
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "Il server e-mail non è configurato, per favore contatta l'amministratore"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "Il server e-mail non è configurato, per favore contatta l'amministratore"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "La tua email non è consentita."

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "Tutto OK! L'e-mail di conferma è stata inviata."

#: cps/web.py:1376 cps/web.py:1399
msgid "Cannot activate LDAP authentication"
msgstr "Impossibile attivare l'autenticazione LDAP"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr "Attendi un minuto prima del prossimo accesso"

#: cps/web.py:1408
#, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "ora sei connesso come: '%(nickname)s'"

#: cps/web.py:1415
#, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "Accesso di riserva come: '%(nickname)s', il server LDAP non è raggiungibile o l'utente è sconosciuto"

#: cps/web.py:1420
#, python-format
msgid "Could not login: %(message)s"
msgstr "Impossibile accedere: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
msgid "Wrong Username or Password"
msgstr "Nome utente o password errati"

#: cps/web.py:1431
msgid "New Password was sent to your email address"
msgstr "La nuova password è stata inviata al tuo indirizzo e-mail"

#: cps/web.py:1435
msgid "An unknown error occurred. Please try again later."
msgstr "Si è verificato un errore sconosciuto, riprova più tardi."

#: cps/web.py:1437
msgid "Please enter valid username to reset password"
msgstr "Inserisci un nome utente valido per reimpostare la password"

#: cps/web.py:1445
#, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "Ora sei connesso come: '%(nickname)s'"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "Profilo di %(name)s"

#: cps/web.py:1526
msgid "Success! Profile Updated"
msgstr "Tutto OK! Profilo aggiornato"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "Esiste già un account per questa e-mail."

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Nessun file gmail.json valido con informazioni OAuth trovato"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr "Elimina contenuto della cartella temporanea"

#: cps/tasks/convert.py:112
#, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s inviato all'E-Reader"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Calibre ebook-convert %(tool)s non trovato"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "Formato %(format)s non trovato sul disco"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "La conversione del libro è terminata con un errore sconosciuto"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Errore con il convertitore Kepubify: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "File convertito non trovato o più di un file nella cartella %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Si è verificato un errore con Calibre: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "Errore nel convertitore: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "Converti"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Riconessione al database di Calibre"

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "E-mail"

#: cps/tasks/metadata_backup.py:34
msgid "Backing up Metadata"
msgstr "Backup dei metadati"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "Sono state generate %(count)s miniature delle copertine"

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "Miniature delle copertine"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "Sono state generate {0} miniature delle serie"

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "Cancellazione della cache delle miniature delle copertine"

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Caricamento"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Utenti"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Nome utente"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "E-mail"

#: cps/templates/admin.html:15
msgid "Send to eReader Email"
msgstr "Invia all'e-mail dell'eReader"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Amministratore"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Password"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "Download"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Visualizza libri"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Modifica"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Elimina"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Scaffale pubblico"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "Importa utenti LDAP"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "Impostazioni server e-mail"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "Nome host SMTP"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "Porta SMTP"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "Crittografia"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "Accesso SMTP"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Da e-mail"

#: cps/templates/admin.html:90
msgid "Email Service"
msgstr "Servizio e-mail"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via Oauth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Configurazione"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Cartella del database di Calibre"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Livello di log"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Porta"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Porta esterna"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Libri per pagina"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Caricamenti"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Navigazione anonima"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Registrazione pubblica"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Accesso remoto Magic Link"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Accesso reverse proxy"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Nome dell'intestazione reverse proxy"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Modifica la configurazione del database di Calibre"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Modifica la configurazione di base"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Modifica la configurazione dell'interfaccia utente"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "Attività pianificate"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr "Ora di inizio"

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr "Durata massima"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr "Genera miniature"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "Genera miniature delle copertine delle serie"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Riconnetti il database di Calibre"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr "Genera file di backup dei metadati"

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr "Aggiorna la cache delle miniature"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Amministrazione"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Scarica il pacchetto di debug"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Visualizza log"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Riavvia Calibre-Web"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Arresta Calibre-Web"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "Informazioni sulla versione"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Versione"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Dettagli"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "Versione attuale"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Ricerca aggiornamenti"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Esegui l'aggiornamento"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Sei sicuro di voler riavviare Calibre-Web?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "OK"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Annulla"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Sei sicuro di voler fermare Calibre-Web?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Aggiornamento in corso, non ricaricare la pagina."

#: cps/templates/author.html:15
msgid "via"
msgstr "via"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "Nella biblioteca"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Ordina secondo la data dei libri, prima i più recenti"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Ordina secondo la data dei libri, prima i più vecchi"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Ordina i titoli in ordine alfabetico"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Ordina i titoli in ordine alfabetico inverso"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Ordina secondo la data di pubblicazione, prima i più recenti"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Ordina secondo la data di pubblicazione, prima i più vecchi"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "riduci"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Altri di"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, python-format
msgid "Book %(index)s of %(range)s"
msgstr "Libro %(index)s di %(range)s"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Lingua"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Editore"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "Pubblicato"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "Descrizione:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "Precedente"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "Successivo"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "Nessun risultato trovato"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "Home"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "Cerca nella biblioteca"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "Disconnetti"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr "Tema normale"

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Elimina libro"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Elimina formati:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "Converti formato libro:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Converti da:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "seleziona un'opzione"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Converti in:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Converti libro"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "Caricamento in corso..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Chiudi"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "Errore"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "Caricamento terminato, elaborazione in corso..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Carica formato"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Titolo del libro"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Autore"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Etichette"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "ID della serie"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Data di pubblicazione"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Valutazione"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Descrizione"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "Identificatori"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "Tipo di identificatore"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "Valore dell'identificatore"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Rimuovi"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "Aggiungi identificatore"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Recupera la copertina dall'URL (JPEG: l'immagine verrà scaricata e archiviata nel database)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Carica la copertina dal disco locale"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Sì"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "No"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Visualizza il libro dopo averlo salvato"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Recupera i metadati"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Salva"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Parola chiave"

#: cps/templates/book_edit.html:240
msgid "Search keyword"
msgstr "Cerca parola chiave"

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Fai clic sulla copertina per caricare i metadati nel modulo"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Caricamento in corso..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Origine"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Errore nella ricerca!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Nessun risultato trovato! Prova con un'altra parola chiave."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "Questo campo è obbligatorio"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Unisci i libri selezionati"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Rimuovi le selezioni"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "Inverti autore e titolo"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "Aggiorna automaticamente l'ordinamento per titolo"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "Aggiorna automaticamente l'ordinamento per autore"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Inserisci titolo"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Titolo"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Inserisci ordinamento per titolo"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Ordina per titolo"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Inserisci ordinamento per autore"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Ordina per autore"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Inserisci autori"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Inserisci categorie"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Inserisci serie"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Indice serie"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Inserisci lingue"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Data di pubblicazione"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Inserisci editori"

#: cps/templates/book_table.html:73
msgid "Enter comments"
msgstr "Inserisci commenti"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "Commenti"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "Stato archivio"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Stato di lettura"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
msgid "Enter "
msgstr "Inserisci"

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Sei veramente sicuro?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "I libri con titolo verranno uniti da:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "Nel libro con il titolo:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Unisci"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Posizione del database di Calibre"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr "Separa i file dei libri dalla biblioteca"

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Usa Google Drive"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Autentica Google Drive"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Cartella Calibre di Google Drive"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "ID del canale di visualizzazione dei metadati"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Revoca"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "La nuova posizione del database non è valida, inserisci un percorso valido"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Configurazione del server"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Porta del server"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "Posizione del file del certificato SSL (lascia vuoto per una configurazione del server senza SSL)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "Posizione del file della chiave SSL (lascia vuoto per una configurazione del server senza SSL)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Canale d'aggiornamento"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Stabile"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Nightly"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "Host attendibili (separati da virgole)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Configurazione del file di log"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Posizione e nome del file di log (se non specificato sarà calibre-web.log)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Abilita il log degli accessi"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Posizione e nome del file di log degli accessi (se non specificato sarà access.log)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Configurazione funzionalità"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "Converti caratteri non inglesi nel titolo e nell'autore durante il salvataggio su disco"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr "Incorpora metadati nel file del libro al momento del download e della conversione per e-mail (sono necessari gli eseguibili Calibre/Kepubify)"

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Abilita il caricamento"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(assicurati che gli utenti dispongano anche delle autorizzazioni di caricamento)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Formati di file autorizzati ad essere caricati"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Abilita la navigazione anonima"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Abilita la registrazione pubblica"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "Usa l'e-mail come nome utente"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Abilita l'accesso remoto con Magic Link"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Abilita la sincronizzazione Kobo"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Inoltra richieste sconosciute al Kobo Store"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Porta esterna del server (per chiamate API con port forwarding)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Usa Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Chiave API Goodreads"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Consenti autenticazione con reverse proxy"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Tipo di accesso"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Usa autenticazione standard"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Usa l'autenticazione LDAP"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Usa OAuth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "Nome host o indirizzo IP del server LDAP"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "Porta del server LDAP"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "Crittografia LDAP"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "Percorso certificato CA LDAP (necessario solo per l'autenticazione del certificato client)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "Percorso certificato LDAP (necessario solo per l'autenticazione del certificato client)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "Percorso del file di chiavi LDAP (necessario solo per l'autenticazione del certificato client)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "Autenticazione LDAP"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anonimo"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Non autenticato"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Semplice"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "Nome utente amministratore LDAP"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "Password amministratore LDAP"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "Nome distinto LDAP (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "Filtro oggetto utente LDAP"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "Il server LDAP è OpenLDAP?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "Per l'importazione degli utenti sono necessarie le seguenti impostazioni"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "Filtro oggetto gruppo LDAP"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "Nome gruppo LDAP"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "Campo membri del gruppo LDAP"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "Rilevamento filtro utente membro LDAP"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "Trova automaticamente"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "Filtro personalizzato"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "Filtro utente membro LDAP"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "Ottieni le credenziali OAuth di %(provider)s"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s ID client OAuth"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s Secret OAuth Client"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "File eseguibili esterni"

#: cps/templates/config_edit.html:325
msgid "Path to Calibre Binaries"
msgstr "Percorso dei file eseguibili di Calibre"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Impostazioni del convertitore di libri di Calibre"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Percorso del convertitore di libri Kepubify"

#: cps/templates/config_edit.html:344
msgid "Location of Unrar binary"
msgstr "Posizione del file binario di UnRar"

#: cps/templates/config_edit.html:360
msgid "Security Settings"
msgstr "Impostazioni di sicurezza"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr "Limita tentativi di accesso non riusciti"

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr "Configura il backend per il limitatore"

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr "Opzioni per il limitatore del Backend"

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr "Controlla se le estensioni dei file corrispondono al contenuto del file al momento del caricamento"

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr "Protezione sessione"

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr "Di base"

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr "Forte"

#: cps/templates/config_edit.html:393
msgid "User Password policy"
msgstr "Politica password utente"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr "Lunghezza minima password"

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr "Obbliga numero"

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr "Obbliga caratteri minuscoli"

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr "Obbliga caratteri maiuscoli"

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr "Obbliga caratteri (necessario per caratteri cinesi, giapponesi e coreani)"

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr "Obbliga caratteri speciali"

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "Visualizza configurazione"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "Numero di libri casuali da mostrare"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "Numero di autori da mostrare prima di nascondere (0=disabilita nascondere)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "Tema"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "Tema standard"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "Tema caliBlur! scuro"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "Espressione regolare per ignorare le colonne"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "Collega lo stato letto/da leggere nella colonna di Calibre"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Visualizza le restrizioni basate sulla colonna di Calibre"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "Espressione regolare per l'ordinamento dei titoli"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "Impostazioni predefinite per i nuovi utenti"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "Utente amministratore"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "Consenti download"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "Consenti visualizzatore eBook"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "Consenti caricamenti"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "Consenti modifiche"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "Consenti eliminazione libri"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "Consenti modifica password"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "Consenti modifica scaffali pubblici"

#: cps/templates/config_view_edit.html:123
msgid "Default Language"
msgstr "Lingua predefinita"

#: cps/templates/config_view_edit.html:131
msgid "Default Visible Language of Books"
msgstr "Lingua predefinita di presentazione dei libri"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "Visibilità predefinita per i nuovi utenti"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "Mostra Libri casuali nella visualizzazione dettagliata"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "Aggiungi etichetta consentiti/negati"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "Aggiungi valori di colonna personalizzati consentiti/negati"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "Leggi nel browser"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "Ascolta nel browser"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "Contrassegna come da leggere"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "Contrassegna come letto"

#: cps/templates/detail.html:262
msgid "Mark Book as Read or Unread"
msgstr "Contrassegna il libro come letto o da leggere"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "Letto"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "Ripristina dall'archivio"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "Aggiungi all'archivio"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr "Contrassegna il libro come archiviato o no per nasconderlo in Calibre-Web ed eliminarlo da Kobo Reader"

#: cps/templates/detail.html:275
msgid "Archive"
msgstr "Archivio"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "Aggiungi allo scaffale"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(Pubblico)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "Modifica i metadati"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "Tipo di account e-mail"

#: cps/templates/email_edit.html:15
msgid "Standard Email Account"
msgstr "Account e-mail standard"

#: cps/templates/email_edit.html:16
msgid "Gmail Account"
msgstr "Account Gmail"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr "Configura l'account Gmail"

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Revoca l'accesso a Gmail"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "Password SMTP"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "Limite dimensione allegato"

#: cps/templates/email_edit.html:66
msgid "Save and Send Test Email"
msgstr "Salva e invia e-mail di prova"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "Indietro"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "Domini consentiti (lista bianca)"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "Aggiungi dominio"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "Aggiungi"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "Inserisci nome del dominio"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "Domini non consentiti (lista nera)"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "Apri il file .kobo/Kobo/Kobo eReader.conf in un editor di testo e aggiungi (o modifica):"

#: cps/templates/generate_kobo_auth_url.html:11
msgid "Kobo Token:"
msgstr "Token di Kobo:"

#: cps/templates/grid.html:21
msgid "List"
msgstr "Elenco"

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "L'istanza Calibre-Web non è configurata, per favore contatta l'amministratore"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Segnala problema"

#: cps/templates/http_error.html:52
msgid "Return to Database config"
msgstr "Ritorna alla configurazione del database"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "Ritorna alla pagina principale"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "Disconnetti utente"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr "Ordina in ordine ascendente in base al numero di download"

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr "Ordina in ordine discendente in base al numero di download"

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "Ordina gli autori in ordine alfabetico"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "Ordina gli autori in ordine alfabetico inverso"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "Ordina in ordine ascendente in base al numero della serie"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "Ordina in ordine discendente in base al numero della serie"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "Avvio"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "Libri in ordine alfabetico"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "Libri ordinati alfabeticamente"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "Pubblicazioni popolari in questo catalogo in base ai download."

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "Pubblicazioni popolari in questo catalogo in base alle valutazioni."

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "Libri aggiunti di recente"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "Gli ultimi libri"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "Libri casuali"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "Libri ordinati per autore"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "Libri ordinati per editore"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "Libri ordinati per categoria"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "Libri ordinati per serie"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "Libri ordinati per lingua"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "Libri ordinati per valutazione"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "Libri ordinati per formato di file"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "Scaffali"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "Libri organizzati in scaffali"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "Attiva/disattiva navigazione"

#: cps/templates/layout.html:59
msgid "Simple Theme"
msgstr "Tema semplice"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "Account"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "Impostazioni"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "Per favore non aggiornare la pagina"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "Naviga"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "Informazioni su"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "Dettagli del libro"

#: cps/templates/list.html:22
msgid "Grid"
msgstr "Griglia"

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "Archiviato"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "Ricordami"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "Password dimenticata?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "Accedi con Magic Link"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Mostra log di Calibre-Web: "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Calibre-Web log: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "Flusso in uscita, non può essere visualizzato"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "Mostra log di accesso: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Scarica il log di Calibre-Web"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "Scarica il log di accesso"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "Seleziona le etchette consentite/negate"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "Seleziona i valori personali consentiti/negati per le colonne"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "Seleziona le categorie consentite/negate per l'utente"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "Seleziona i valori personali consentiti/negati per le colonne dell'utente"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "Inserisci etichetta"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "Aggiungi restrizioni di visualizzazione"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "Questo formato di libro verrà definitivamente cancellato dal database"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "Questo libro verrà definitivamente cancellato dal database"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "e dal disco rigido"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Nota importante su Kobo: i libri eliminati rimarranno su qualsiasi dispositivo Kobo associato."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "I libri devono essere prima archiviati e il dispositivo sincronizzato prima che un libro possa essere eliminato in sicurezza."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "Scegli la posizione del file"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "tipo"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "nome"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "dimensione"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "Cartella superiore"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "Seleziona"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "OK"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Catalogo eBook Calibre-Web"

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr "Lettore epub"

#: cps/templates/read.html:80
msgid "Choose a theme below:"
msgstr "Scegli un tema qui sotto:"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "Chiaro"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "Scuro"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr "Seppia"

#: cps/templates/read.html:90
msgid "Black"
msgstr "Nero"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "Adatta il testo quando le barre laterali sono aperte."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr "Dimensioni caratteri"

#: cps/templates/read.html:105
msgid "Font"
msgstr "Carattere"

#: cps/templates/read.html:106
msgid "Default"
msgstr "Predefinito"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr "Yahei"

#: cps/templates/read.html:108
msgid "SimSun"
msgstr "SimSun"

#: cps/templates/read.html:109
msgid "KaiTi"
msgstr "KaiTi"

#: cps/templates/read.html:110
msgid "Arial"
msgstr "Arial"

#: cps/templates/read.html:113
msgid "Spread"
msgstr "Doppia pagina"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr "Due colonne"

#: cps/templates/read.html:115
msgid "One column"
msgstr "Una colonna"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr "Lettore di fumetti"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "Scorciatoie della tastiera"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "Pagina precedente"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "Pagina successiva"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr "Visualizzazione a pagina singola"

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr "Visualizzazione a striscia lunga"

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "Adatta al meglio"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "Adatta alla larghezza"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "Adatta all'altezza"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "Adatta all'originale"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "Ruota a destra"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "Ruota a sinistra"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "Capovolgi immagine"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr "Visualizzazione"

#: cps/templates/readcbr.html:113
msgid "Single Page"
msgstr "Pagina singola"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr "Striscia lunga"

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "Scala"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "Migliore"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "Larghezza"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "Altezza"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "Originale"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "Ruota"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "Capovolgi"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "Orizzontale"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "Verticale"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "Orientamento"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "Da sinistra a destra"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "Da destra a sinistra"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr "Reimposta in alto"

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr "Ricorda la posizione"

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "Barra di scorrimento"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "Mostra"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "Nascondi"

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr "Lettore DJVU"

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr "Lettore PDF"

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr "Lettore txt"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "Registra un nuovo account"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "Scegli un nome utente"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "La tua e-mail"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "Magic Link: autorizza nuovo dispositivo"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "Su un altro dispositivo, accedi e visita:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "Una volta verificato, accederai automaticamente a questo dispositivo."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "Questo link di verifica scadrà tra 10 minuti."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr "Genera miniature delle copertine delle serie"

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "Termine di ricerca:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "Risultati per:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "Data di pubblicazione dal"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "Data di pubblicazione fino al"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr "Qualsiasi"

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr "Vuoto"

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "Escludi etichette"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "Escludi serie"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "Escludi scaffali"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "Escludi lingue"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "Estensioni"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "Escludi estensioni"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "Valutazione superiore a"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "Valutazione inferiore a"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "Da:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "A:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "Elimina questo scaffale"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "Modifica le caratteristiche dello scaffale"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "Disponi libri manualmente"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "Disabilita la modifica della disposizione"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "Abilita la modifica della disposizione"

#: cps/templates/shelf.html:28
msgid "Sort according to book added to shelf, newest first"
msgstr "Ordina secondo il libro aggiunto allo scaffale, prima il più recente"

#: cps/templates/shelf.html:29
msgid "Sort according to book added to shelf, oldest first"
msgstr "Ordina secondo il libro aggiunto allo scaffale, prima il più vecchio"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "Condividi con tutti"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "Sincronizza questo scaffale con il lettore Kobo"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "Trascina per riordinare"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "Libro nascosto"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "Statistiche della biblioteca"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "Libri in questa biblioteca"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "Autori in questa biblioteca"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "Categorie in questa biblioteca"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "Serie in questa biblioteca"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "Statistiche del sistema"

#: cps/templates/stats.html:33
msgid "Program"
msgstr "Programma"

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "Versione installata"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "Utente"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "Attività"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "Stato"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "Avanzamento"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "Tempo d'esecuzione"

#: cps/templates/tasks.html:19
msgid "Message"
msgstr "Messaggio"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr "Azioni"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr "Questa attività verrà annullata. Tutti i progressi compiuti da questa attività verranno salvati."

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr "Se si tratta di un'attività pianificata, verrà eseguita nuovamente all'orario pianificato successivo."

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "Reimposta la password dell'utente"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr "Invia all'indirizzo e-mail dell'eReader. Usa la virgola per separare le email per più eReader"

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "Lingua dei libri"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "Impostazioni OAuth"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "Collega"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "Scollega"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Token di sincronizzazione Kobo"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "Crea/Visualizza"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr "Forza sincronizzazione kobo completa"

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "Aggiungi valori personali consentiti/negati nelle colonne"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "Sincronizza con Kobo unicamene i libri negli scaffali selezionati"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "Elimina utente"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Genera URL di autenticazione per Kobo"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "Seleziona..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "Modifica utente"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "Inserisci nome utente"

#: cps/templates/user_table.html:135
msgid "Enter Email"
msgstr "Inserisci e-mail"

#: cps/templates/user_table.html:136
msgid "Enter eReader Email"
msgstr "Inserisci e-mail dell'eReader"

#: cps/templates/user_table.html:136
msgid "eReader Email"
msgstr "E-mail dell'eReader"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "Locale"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "Lingue dei libri visibili"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "Modifica le etichette consentite"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "Etichette consentite"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "Modifica le etichette non consentite"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "Etichette non consentite"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "Modifica i valori delle colonne consentite"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "Valori delle colonne consentiti"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "Modifica i valori per le colonne non consentite"

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr "Valori per le colonne non consentite"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "Cambia password"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "Visualizza"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "Modifica gli scaffali pubblici"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "Sincronizza gli scaffali selezionati con Kobo"

#: cps/templates/user_table.html:156
msgid "Show Read/Unread Section"
msgstr "Mostra sezione Libri letti e Libri da leggere"

