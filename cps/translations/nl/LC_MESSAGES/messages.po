# Dutch translations for Calibre-Web
# Copyright (C) 2017 Calibre-Web
# This file is distributed under the same license as the Calibre-Web
# project.
# translation by <PERSON>
# <AUTHOR> <EMAIL>, 2017.
msgid ""
msgstr ""
"Project-Id-Version: Calibre-Web (GPLV3)\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/Calibre-Web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2023-12-20 22:00+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON> <michiel.cornelissen+gitbhun at proton.me>\n"
"Language: nl\n"
"Language-Team: <EMAIL>\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statistieken"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "De server is herstart, vernieuw de pagina"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "Bezig met het afsluiten van de server, sluit het venster"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr "Gelukt! Database opnieuw verbonden"

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Onbekende opdracht"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Test E-Mail wordt verzonden naar %(email)s, controleer de taken voor het resultaat"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Onbekend"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Systeembeheer"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Basisconfiguratie"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "Uiterlijk aanpassen"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, fuzzy, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "Aangepaste kolom Nr.%(column)d bestaat niet in de Calibre Database"

#: cps/admin.py:333 cps/templates/admin.html:51
#, fuzzy
msgid "Edit Users"
msgstr "Systeembeheerder"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Alles"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Gebruiker niet gevonden"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} gebruikers succesvol verwijderd"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Alle talen"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Misvormd verzoek"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Gast naam kan niet worden veranderd"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "Gast kan deze rol niet hebben"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Kan systeembeheerder rol niet verwijderen van de laatste systeembeheerder"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Waarde moet Waar of Onwaar zijn"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Ongeldige rol"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "Gast kan dit niet bekijken"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "Ongeldige waarde"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Gasts locale is automatisch bepaald en kan niet handmatig worden ingesteld"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Geen geldige locale is opgegeven"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Geen geldige boek taal is opgegeven"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Parameter is niet gevonden"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "Ongeldige gelezen kolom"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "Ongeldige beperkte kolom"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Calibre-Web-configuratie bijgewerkt"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Wil je je Kobo Token echt verwijderen?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Wil je dit domein echt verwijderen?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Wil je deze gebruiker echt verwijderen?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Weet je zeker dat je deze boekenplank wilt verwijderen?"

#: cps/admin.py:624
#, fuzzy
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Weet je zeker dat je de locales van de geselecteerde gebruiker(s) wil veranderen?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Weet je zeker dat je de zichtbare talen voor de geselecteerde gebruiker(s) wil veranderen?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Weet je zeker dat je de geselecteerde rol van de geselecteerde gebruiker(s) wil veranderen?"

#: cps/admin.py:630
#, fuzzy
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Weet je zeker dat je de geselecteerde beperkingen voor de geselecteerde gebruikers(s) wil verwijderen?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Weet je zeker dat je de geselecteerde zichtbaarheidsbeperkingen voor de geselecteerde gebruiker(s) wil veranderen?"

#: cps/admin.py:635
#, fuzzy
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Weet je zeker dat je de synchronisatiegedrag van boekenplanken voor de geselecteerde gebruiker(s) wil veranderen?"

#: cps/admin.py:637
#, fuzzy
msgid "Are you sure you want to change Calibre library location?"
msgstr "Weet je zeker dat je de locatie van de Calibre-bibliotheek wil veranderen?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-web gaat zoeken naar bijgewerkte omslagen en miniaturen bijwerken, dit kan even duren?"

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Weet u zeker dat u de volledige Calibre-Web synchronisatiedatabase wilt verwijderen om een volledige synchronisatie met uw Kobo Reader te forceren?"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Weigeren"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Toestaan"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{} synchronisatie objecten verwijderd"

#: cps/admin.py:987
msgid "Tag not found"
msgstr "Tag niet gevonden"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Ongeldige actie"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json is niet geconfigureerd voor webapplicatie"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "De locatie van het logbestand is onjuist, voer een geldige locatie in"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "De locatie vam het toegangslog is onjuist, voer een geldige locatie in"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Voer alsjeblieft een LDAP Provider, Port, DN en User Object Identifier in"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Voer een geldig LDAP Service Account en wachtwoord in"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "Voer een LDAP Service Account in"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAP Groep Object Filter Moet Een \"%s\" Formaat Identificiatie hebben"

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAP Groep Object Filter heeft een niet-gebalanceerd haakje"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP Gebruiker Object Filter moet \"%s\" Formaat Identificatie hebben"

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAP Gebruiker Filter heeft een niet-gebalanceerd haakje"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP Lid Gebruiker Filter moet een \"%s\" Formaat Identificatie hebben"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "LDAP Lid Gebruiker Filter heeft een niet-gebalanceerd haakje"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAP CACertficaat, Certificaat of Sleutel Locatie is ongeldig. Voer alsjeblieft een geldig pad in."

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Gebruiker toevoegen"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "SMTP-instellingen bewerken"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr "Gelukt! Gmail account geverifieerd."

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Database fout: %(error)s."

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "Test E-Mail wordt verzonden naar %(email)s, controleer de taken voor het resultaat"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Fout opgetreden bij het versturen van de test-e-mail: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Gelieve eerst je e-mail adres configureren..."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "E-mailserver-instellingen bijgewerkt"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "Bewerk instellingen van geplande taken"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "De starttijd van de taak is ongeldig"

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "De duur van de taak is ongeldig"

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "Instellingen van geplande taken bijgewerkt"

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "Onbekende fout opgetreden. Probeer het later nog eens."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "Instellingen database is niet schrijfbaar."

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Gebruiker '%(nick)s' bewerken"

#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Wachtwoord voor gebruiker %(user)s is hersteld"

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Stel eerst SMTP-mail in..."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Logbestand lezer"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Update opvragen"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Update downloaden"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Update uitpakken"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Update toepassen"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Databaseverbindingen zijn gesloten"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Bezig met stoppen van Calibre-Web"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Update voltooid, klik op 'Oké' en vernieuw de pagina"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Update mislukt:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTP-fout"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Verbindingsfout"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Time-out tijdens maken van verbinding"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Algemene fout"

#: cps/admin.py:1551
#, fuzzy
msgid "Update file could not be saved in temp dir"
msgstr "Geüpload bestand kon niet opgeslagen worden in de tijdelijke map"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "Bestanden kunnen niet vervangen worden tijdens een update"

#: cps/admin.py:1576
#, fuzzy
msgid "Failed to extract at least One LDAP User"
msgstr "Mislukt om minstens een LDAP gebruiker aan te maken"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Het is niet gelukt tenminste een LDAP gebruiker aan te maken"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Fout: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Fout: No user returned in response of LDAP server"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Minstens een LDAP Gebruiker is niet gevonden in de Database"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} Gebruiker succesvol geïmporteerd"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "Database niet gevonden, voer de juiste locatie in"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "Kan niet schrijven naar database"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "SSL-sleutellocatie is niet geldig, voer een geldig pad in"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "SSL-certificaatlocatie is niet geldig, voer een geldig pad in"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr "Het wachtwoord moet tussen de 1 en 40 tekens lang zijn"

#: cps/admin.py:1917
#, fuzzy
msgid "Database Settings updated"
msgstr "E-mailserver-instellingen bijgewerkt"

#: cps/admin.py:1925
#, fuzzy
msgid "Database Configuration"
msgstr "Databaseconfiguratie"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "Vul alle velden in!"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "Het e-mailadres bevat geen geldige domeinnaam"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Gebruiker toevoegen"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "Gebruiker '%(user)s' aangemaakt"

#: cps/admin.py:1972
#, fuzzy
msgid "Oops! An account already exists for this Email. or name."
msgstr "Bestaand account met dit e-mailadres of deze gebruikersnaam aangetroffen."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "Gebruiker '%(nick)s' verwijderd"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "Kan Gast gebruiker niet verwijderen"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Kan laatste systeembeheerder niet verwijderen"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr "E-mail kan niet leeg zijn en moet geldig zijn"

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "Gebruiker '%(nick)s' bijgewerkt"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Zoeken"

#: cps/converter.py:31
msgid "not installed"
msgstr "niet geïnstalleerd"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Kan programma niet uitvoeren"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Geen"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Bestand %(file)s geüpload"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Bron- of doelformaat ontbreekt voor conversie"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Het boek is in de wachtrij geplaatst voor conversie naar %(book_format)s"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Er is een fout opgetreden bij het converteren van dit boek: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Oeps! Geselecteerd boek is niet beschikbaar. Bestand bestaat niet of is niet toegankelijk"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "Gebruiker mist rechten om de omslag te uploaden"

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "Identificatoren zijn niet hoofdlettergevoelig, overschrijf huidige identificatoren"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, fuzzy, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "%(langname)s is geen geldige taal"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "De metagegevens zijn bijgewerkt"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "Fout tijdens bijwerken van boek: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Geüpload boek staat mogelijk al in de bibliotheek, controleer alvorens door te gaan: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "De bestandsextensie '%(ext)s' is niet toegestaan op deze server"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "De bestandsextensie '%(ext)s' is niet toegestaan op deze server"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Het te uploaden bestand moet voorzien zijn van een extensie"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Bestand %(filename)s kon niet opgeslagen worden in de tijdelijke map"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Omslag %(file)s niet verplaatst: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Het boekformaat is verwijderd"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Het boek is verwijderd"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "U mist rechten om boeken te verwijderen"

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "metagegevens bewerken"

#: cps/editbooks.py:1016
#, fuzzy, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "%(seriesindex)s is geen geldig nummer, sla het over"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "Gebruiker mist rechten om extra bestandsformaten te uploaden"

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Kan de locatie '%(path)s' niet aanmaken (niet gemachtigd)."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Kan %(file)s niet opslaan."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Bestandsformaat %(ext)s toegevoegd aan %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Het instellen van Google Drive is niet afgerond, heractiveer Google Drive"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Het callback-domein is niet geverifieerd. Volg de stappen in de Google-ontwikkelaarsconsole om het domein te verifiëren"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "%(format)s formaat niet gevonden voor boek met id: %(book)d"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "%(format)s niet aangetroffen op Google Drive: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s niet gevonden %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
#, fuzzy
msgid "Send to eReader"
msgstr "Versturen naar Kindle"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "Deze e-mail is verstuurd via Calibre-Web."

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web - test-e-mail"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "Test-e-mail"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Aan de slag met Calibre-Web"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "Registratie-e-mailadres van gebruiker: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "%(orig)s converteren naar %(format)s en versturen naar Kindle"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "%(format)s versturen naar Kindle"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s verzonden naar Kindle"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Het opgevraagde bestand kan niet worden gelezen. Ben je hiertoe gemachtigd?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "Gelezen/ongelezen status kan niet aangepast worden: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Het verwijderen van de boekenmap voor boek %(id)s is mislukt, het pad heeft submappen: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Verwijderen van boek %(id)s mislukt: %(message)s"

#: cps/helper.py:392
#, fuzzy, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Verwijder boek %(id)s alleen uit database, boek pad is ongeldig: %(path)s"

#: cps/helper.py:439
#, fuzzy, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Kan de titel '%(src)s' niet wijzigen in '%(dest)s': %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Bestand '%(file)s' niet aangetroffen op Google Drive"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Kan de titel '%(src)s' niet wijzigen in '%(dest)s': %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Boeken locatie '%(path)s' niet aangetroffen op Google Drive"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr "Bestaand account gevondne met dit e-mailadres"

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Deze gebruikersnaam is al in gebruik"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "Ongeldig E-Mail adres"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr "Het wachtwoord voldoet niet aan de validatieregels"

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "Pythonmodule 'advocate' is niet geïnstalleerd maar is nodig omslag uploads"

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Fout bij downloaden omslag"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Onjuist omslagformaat"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "Toegang tot localhost of het lokale netwerk niet toegestaant voor omslag uploaden"

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Locatie aanmaken voor omslag mislukt"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Omslag-bestand is geen afbeelding of kon niet opgeslagen worden"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Alleen jpg/jpeg/png/webp/bmp bestanden worden ondersteund als omslag"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "Ongeldig omslagbestand"

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Alleen jpg/jpeg bestanden zijn toegestaan als omslag"

#: cps/helper.py:989 cps/helper.py:1149
#, fuzzy
msgid "Cover"
msgstr "Willekeurige boeken"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "UnRar executable niet gevonden"

#: cps/helper.py:1017
#, fuzzy
msgid "Error executing UnRar"
msgstr "Fout bij het uitvoeren van UnRar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "Kan niet schrijven naar database"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Kan programma niet uitvoeren"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "Fout bij het uitvoeren van UnRar"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr "Voeg alle boeken toe aan de wachtrij voor het maken van een metagegevens backup"

#: cps/kobo_auth.py:92
#, fuzzy
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Je kunt Calibre-Web niet vanaf de lokale computer openen om een geldige api_endpoint te krijgen voor je kobo toestel"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo Instellen"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Aanmelden bij %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "je bent ingelogd als: '%(nickname)s'"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Koppeling gemaakt met %(oauth)s"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Inloggen mislukt, geen gebruiker gekoppeld aan OAuth account"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Koppeling met %(oauth)s verbroken"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Ontkoppelen van %(oauth)s mislukt"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Niet gelinkt aan %(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Inloggen bij GitHub mislukt."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Opvragen gebruikersinfo bij GitHub mislukt."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Inloggen bij Google mislukt."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Opvragen gebruikersinfo bij Google mislukt."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub OAuth fout, probeer het later nog eens."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "Github OAuth foutmelding: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google OAuth fout, probeer het later nog eens."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google OAuth foutmelding: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} sterren"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Inloggen"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Toegangssleutel niet gevonden"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Toegangssleutel is verlopen"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Gelukt! Ga terug naar je apparaat"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Boeken"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Recent toegevoegde boeken tonen"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Populaire boeken"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Populaire boeken tonen"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Gedownloade boeken"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Gedownloade boeken tonen"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Best beoordeelde boeken"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Best beoordeelde boeken tonen"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Gelezen boeken"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "Gelezen/Ongelezen boeken tonen"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Ongelezen boeken"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Ongelezen boeken tonen"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Willekeurige boeken"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Willekeurige boeken tonen"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Categorieën"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "Categoriekeuze tonen"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Boekenreeksen"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "Boekenreeksenkeuze tonen"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Auteurs"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "Auteurkeuze tonen"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Uitgevers"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "Uitgeverskeuze tonen"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Talen"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "Taalkeuze tonen"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Beoordelingen"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "Beoordelingen tonen"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Bestandsformaten"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "Bestandsformaten tonen"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Gearchiveerde boeken"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "Gearchiveerde boeken tonen"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Boekenlijst"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Boekenlijst tonen"

#: cps/search.py:201
msgid "Published after "
msgstr "Gepubliceerd na "

#: cps/search.py:208
msgid "Published before "
msgstr "Gepubliceerd vóór "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Beoordeling <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Beoordeling >= %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "Lees Status = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "Fout tijdens het zoeken van aangepaste kolommen, start Calibre-Web opnieuw op"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Geavanceerd zoeken"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Ongeldige boekenplank opgegeven"

#: cps/shelf.py:55
#, fuzzy
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Sorry, je mag geen boeken toevoegen aan boekenplank: %(shelfname)s"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Dit boek maakt al deel uit van boekenplank: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Het boek is toegevoegd aan boekenplank: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "U heeft niet voldoende rechten om een boek aan deze boekenplank toe te voegen"

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "Deze boeken maken al deel uit van boekenplank: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "De boeken zijn toegevoegd aan boekenplank: %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Kan boeken niet toevoegen aan boekenplank: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Het boek is verwijderd van boekenplank: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "U heeft niet voldoende rechten om een boek van deze boekenplank te verwijderen"

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Boekenplank maken"

#: cps/shelf.py:226
#, fuzzy
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Je bent niet gemachtigd deze boekenplank aan te passen"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Pas een boekenplank aan"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "Fout bij verwijderen boekenplank!"

#: cps/shelf.py:239
#, fuzzy
msgid "Shelf successfully deleted"
msgstr "Het boek is verwijderd"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Volgorde van boekenplank veranderen: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "Je mist rechten om een openbare boekenplank te maken "

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Boekenplank '%(title)s' aangemaakt"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Boekenplank '%(title)s' is aangepast"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "Er is een fout opgetreden"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "Een openbare boekenplank met de naam '%(title)s' bestaat al."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "Een persoonlijke boekenplank met de naam '%(title)s' bestaat al."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Boekenplank: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Kan boekenplank niet openen: de boekenplank bestaat niet of is ontoegankelijk"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Taken"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Wachten"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Mislukt"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Gestart"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Voltooid"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "Beëindigd"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "Geannuleerd"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Onbekende status"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Onverwachte gegevens tijdens het uitlezen van de update-informatie"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Er is geen update beschikbaar."

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "Er is een update beschikbaar. Klik op de knop hieronder om te updaten naar de nieuwste versie."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "De update-informatie kan niet worden opgehaald"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Klik op de onderstaande knop om de laatste stabiele versie te installeren."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "Er is een update beschikbaar. Klik op de knop hieronder om te updaten naar versie: %(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Geen update-informatie beschikbaar"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Verkennen (willekeurige boeken)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Populaire boeken (meest gedownload)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Gedownloade boeken door %(user)s"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Auteur: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Uitgever: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Reeks: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "Beoordeling: geen"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Beoordeling: %(rating)s sterren"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Bestandsformaat: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Categorie: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Taal: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "Downloads"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Beoordelingen"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Alle bestandsformaten"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "Stel eerst SMTP-mail in..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Het boek is in de wachtrij geplaatst om te worden verstuurd aan %(eReadermail)s"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Fout opgetreden bij het versturen van dit boek: %(res)s"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Stel je kindle-e-mailadres in..."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr "Wacht alstublieft één minuut voor het registreren van de volgende gebruiker"

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Registreren"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "E-mailserver is niet geconfigureerd, neem contact op met de beheerder!"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "E-mailserver is niet geconfigureerd, neem contact op met de beheerder!"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "Dit e-mailadres mag niet worden gebruikt voor registratie"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "Er is een bevestigings-e-mail verstuurd naar je e-mailadres."

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "Kan de LDAP authenticatie niet activeren"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr "Wacht alstublieft één minuut voor de volgende inlogpoging"

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "je bent ingelogd als: '%(nickname)s'"

#: cps/web.py:1415
#, fuzzy, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "Terugvallen op login: '%(nickname)s', LDAP Server is onbereikbaar, of de gebruiker is onbekend"

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "Inloggen mislukt: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "Verkeerde gebruikersnaam of wachtwoord"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "Een nieuw wachtwoord is verzonden naar je e-mailadres"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "Onbekende fout opgetreden. Probeer het later nog eens."

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "Geef een geldige gebruikersnaam op om je wachtwoord te herstellen"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "je bent ingelogd als: '%(nickname)s'"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "%(name)ss profiel"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "Profiel bijgewerkt"

#: cps/web.py:1530
#, fuzzy
msgid "Oops! An account already exists for this Email."
msgstr "Bestaand account met dit e-mailadres aangetroffen."

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Geen geldig gmail.json bestand gevonden met OAuth informatie"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, fuzzy, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s verzonden naar Kindle"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Calibre ebook-convert %(tool)s niet gevonden"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s formaat is niet gevonden op de schijf"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "E-Book converter mislukt met een onbekende foutmelding"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify-converteerder mislukt: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "Omgezette bestand is niet gevonden of meer dan een bestand in map %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre mislukt met foutmelding: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "E-boek-conversie mislukt: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "Overzetten"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Opnieuw verbinding aan het maken met Calibre database"

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "E-mail"

#: cps/tasks/metadata_backup.py:34
#, fuzzy
msgid "Backing up Metadata"
msgstr "metagegevens bewerken"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "%{count}s omslagminiaturen gegenereerd"

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "Omslag miniaturen"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "{0} serieminiaturen gegenereerd"

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "Cache met omslagminiaturen aan het opschonen"

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Uploaden"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Gebruikers"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Gebruikersnaam"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "E-mailadres"

#: cps/templates/admin.html:15
#, fuzzy
msgid "Send to eReader Email"
msgstr "Kindle-e-mailadres"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Beheer"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Wachtwoord"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "Downloaden"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Boeken lezen"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Bewerken"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Verwijderen"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Openbare boekenplank"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "LDAP gebruikers importeren"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "SMTP-serverinstellingen"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTP-hostnaam (gebruik mail.example.org om wachtwoordherstel uit te schakelen)"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTP-poort"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "Encryptie"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTP-gebruikersnaam"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Van e-mail"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "EMail Service"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via OAuth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Instellingen"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Calibre-database locatie"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Logniveau"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Poort"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Externe poort"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Aantal boeken per pagina"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Uploaden toestaan"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Anoniem verkennen"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Openbare registratie"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Inloggen op afstand"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Reverse Proxy Login"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Reverse proxy header naam"

#: cps/templates/admin.html:159
#, fuzzy
msgid "Edit Calibre Database Configuration"
msgstr "Bewerk Calibre databaseconfiguratie"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Bewerk basisconfiguratie"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Bewerk gebruikersinterface configuratie"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "Geplande taken"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr "Starttijd"

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr "Maximale duur"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr "Genereer miniaturen"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "Genereer serie miniaturen"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Opnieuw verbinding maken met Calibre database"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr "Genereer backupbestanden voor metagegevens"

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr "Ververs cache met omslagminiaturen"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Systeembeheer"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Download foutopsporingspakket"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Logboeken bekijken"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Calibre-Web herstarten"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Calibre-Web stoppen"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "Versie informatie"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Versie"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Details"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "Huidige versie"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Controleren op updates"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Update uitvoeren"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Weet je zeker dat je Calibre-Web wilt herstarten?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "Oké"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Annuleren"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Weet je zeker dat je Calibre-Web wilt stoppen?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Bezig met bijwerken, vernieuw de pagina niet"

#: cps/templates/author.html:15
msgid "via"
msgstr "via"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "In bibliotheek"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Sorteren op datum, nieuwste boeken eerst"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Sorteren op datum, oudste boeken eerst"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Sorteren op alfabetische volgorde"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Sorteren op omgekeerde alfabetische volgorde"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Sorteren op publicatiedatum, nieuwste boeken eerst"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Sorteren op publicatiedatum, oudste boeken eerst"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "beperken"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Meer van"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, fuzzy, python-format
msgid "Book %(index)s of %(range)s"
msgstr "Boek %(index)s van %(range)s"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Taal"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Uitgever"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "Gepubliceerd"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "Beschrijving:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "Vorige"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "Volgende"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "Geen resultaten gevonden"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "Startpagina"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "Zoek in bibliotheek"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "Afmelden"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Boek verwijderen"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Formaten verwijderen:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "Boekformaat converteren:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Converteren van:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "kies een optie"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Converteren naar:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Boek converteren"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "Bezig met uploaden..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Sluiten"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "Fout"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "Uploaden voltooid, bezig met verwerken..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Uploadformaat"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Boektitel"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Auteur"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Labels"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "Boekenreeks volgnummer"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Publicatiedatum"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Beoordeling"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Omschrijving"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "Identificatoren"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "Identificatie type"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "Identificatie waarde"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Verwijderen"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "Identificator toevoegen"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Omslag-url (jpg) (de omslag wordt gedownload en opgeslagen in de database)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Omslag uploaden vanaf de harde schijf"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Ja"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "Nee"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Boek inkijken na bewerking"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Metagegevens ophalen"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Opslaan"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Trefwoord"

#: cps/templates/book_edit.html:240
#, fuzzy
msgid "Search keyword"
msgstr " Trefwoord zoeken "

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Klik op de omslag om de metagegevens in het formulier te laden"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Bezig met laden..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Bron"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Zoekfout!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Geen resultaten gevonden! Gebruik een ander trefwoord."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "Dit veld is verplicht"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Geselecteerde boeken samenvoegen"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Geselecteerde boeken verwijderen"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "Auteur en titel omwisselen"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "Automatisch sorteren op titel"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "Automatisch sorteren op auteur"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Geef titel"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Titel"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Voer Titel sorteervolgorde in"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Titel sorteren"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Voer Auteur sorteervolgorde in"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Auteur sorteren"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Voer Auteurs in"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Voer categorieën in"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Voer serie in"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Serie index"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Voer talen in"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Voer publicatiedatum in"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Voer uitgevers in"

#: cps/templates/book_table.html:73
#, fuzzy
msgid "Enter comments"
msgstr "Voer domeinnaam in"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "Opmerkingen"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "Archiefstatus"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Lees Status"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
#, fuzzy
msgid "Enter "
msgstr "Identificatoren"

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Weet je het zeker?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "Boeken met de titel zullen worden samengevoegd van:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "In boek met titel:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Samenvoegen"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Locatie van de Calibre-database"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Google Drive gebruiken?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Google Drive goedkeuren"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Google Drive Calibre-map"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "Metagegevens Watch Channel ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Intrekken"

#: cps/templates/config_db.html:80
#, fuzzy
msgid "New db location is invalid, please enter valid path"
msgstr "Database niet gevonden, voer een geldig pad in"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Serverinstellingen"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Serverpoort"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "SSL-certificaatlocatie ('certfile' - laat leeg voor niet-SSL-servers)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "SSL-sleutellocatie ('keyfile' - laat leeg voor niet-SSL-servers)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Updatekanaal"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Stabiel"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Bèta"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "Vertrouwde hosts (komma gescheiden)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Logbestanden"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Locatie en naam van logbestand (calibre-web.log indien niet opgegeven)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Toegangslog aanzetten"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Locatie en naam van het toegangslog (access.log indien niet opgegeven)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Geavanceerde opties"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "Zet niet Engelse tekens in titel en auteur om tijdens het opslaan"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Uploaden inschakelen"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(Zorg dat gebruikers uploadrechten hebben)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Toegelaten upload bestandsformaten"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Anoniem verkennen inschakelen"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Openbare registratie inschakelen"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "Gebruik e-mail als inlognaam"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Inloggen op afstand inschakelen ('magic link')"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Zet Kobo sync aan"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Proxy onbekende verzoeken naar Kobo winkel"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Server Externe Port (voor port doorgestuurde API oproepen)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Gebruik Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Goodreads API-sleutel"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Reverse Proxy authenticatie toestaan"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Login type"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Gebruik standaard authenticatie"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Gebruik LDAP authenticatie"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Gebruik OAuth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAP Server hostnaam of IP-adres"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAP Server Poort"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAP encryptie"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP CACertificataat Path (Alleen nodig voor Client Certificaat Autorisatie)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP Certificaat Pad (Alleen nodig voor Client Certificaat Autorisatie)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP SleutelBestand Pad (alleen nodig voor Client Certificaat Autorisatie)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP Authenticatie"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anoniem"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Niet geverifieerd"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Eenvoudig"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP Administrator naam"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP Administrator wachtwoord"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP Distinguished Name (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP User Object Filter"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "LDAP Server is OpenLDAP?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "De volgende instellingen zijn nodig voor het importeren van gebruikers:"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAP Groep Object Filter"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAP groepnaam"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "LDAP groepleden veld"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAP Lid Gebruiker Filter Detectie"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "Automatisch detecteren"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "Aangepast Filter"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "LDAP Lid Gebruiker Filter"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "Verkrijg %(provider)s OAuth Verificatiegegevens"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth Client Id"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth Client geheim"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "Externe programma's"

#: cps/templates/config_edit.html:325
#, fuzzy
msgid "Path to Calibre Binaries"
msgstr "Pad naar Calibre E-Book Converteerder"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Calibre E-Book omzet instellingen"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Pad naar Kepubify E-book Converteerder"

#: cps/templates/config_edit.html:344
#, fuzzy
msgid "Location of Unrar binary"
msgstr "Locatie van UnRar-programma"

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "OAuth Instellingen"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr "Beperk aantal mislukte inlogpogingen"

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr "Sessiebescherming"

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr "Basis"

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr "Sterk"

#: cps/templates/config_edit.html:393
#, fuzzy
msgid "User Password policy"
msgstr "Gebruikerswachtwoord herstellen"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr "Minimale wachtwoordlengte"

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr "Forceer getal"

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr "Forceer kleine letter"

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr "Forceer hoofdletter"

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr "Forceer speciaal teken"

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "Instellingen bekijken"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "Aantal te tonen willekeurige boeken"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "Aantal te tonen auteurs alvorens te verbergen (0=nooit verbergen)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "Thema"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "Standaard thema"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "caliBlur! donker thema"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "Reguliere expressie om kolommen te negeren"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "Gelezen/ongelezen-status koppelen aan Calibre-kolom"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Bekijk restricties gebaseerd op Calibre kolommen"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "Reguliere expressie voor het sorteren op titel"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "Standaardinstellingen voor nieuwe gebruikers"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "Systeembeheerder"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "Downloads toestaan"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "Boeken lezen toestaan"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "Uploads toestaan"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "Bewerken toestaan"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "Verwijderen van boeken toestaan"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "Wachtwoord wijzigen toestaan"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "Bewerken van openbare boekenplanken toestaan"

#: cps/templates/config_view_edit.html:123
#, fuzzy
msgid "Default Language"
msgstr "Talen uitsluiten"

#: cps/templates/config_view_edit.html:131
#, fuzzy
msgid "Default Visible Language of Books"
msgstr "Taal van boeken"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "Standaard zichtbaar voor nieuwe gebruikers"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "Willekeurige boeken tonen in gedetailleerde weergave"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "Voeg toegestane/geweigerde tags toe"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "Voeg toegestane/geweigerde aangepaste kolomwaarden toe"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "Lezen in webbrowser"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "Luisteren in webbrowser"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "Markeren als ongelezen"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "Markeren als gelezen"

#: cps/templates/detail.html:262
#, fuzzy
msgid "Mark Book as Read or Unread"
msgstr "Markeren als ongelezen"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "Gelezen"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "Terughalen uit archief"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "Toevoegen aan archief"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "Gearchiveerd"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "Toevoegen aan boekenplank"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(Openbaar)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "Metagegevens bewerken"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "Kies Server Type"

#: cps/templates/email_edit.html:15
#, fuzzy
msgid "Standard Email Account"
msgstr "Gebruik Standaard E-Mail Account"

#: cps/templates/email_edit.html:16
#, fuzzy
msgid "Gmail Account"
msgstr "Kies Server Type"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr "Gmail account instellen"

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Gmail toegang intrekken"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "SMTP-wachtwoord"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "Bijlage bestandsgrootte limiet"

#: cps/templates/email_edit.html:66
#, fuzzy
msgid "Save and Send Test Email"
msgstr "Opslaan en test-e-mail versturen"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "Annuleren"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "Toegelaten domeinen voor registratie"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "Domein toevoegen"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "Toevoegen"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "Voer domeinnaam in"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "Geweigerde domeinen voor registratie"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "Open het .kobo/Kobo/Kobo eReader.conf bestand in een teksteditor en voeg toe (of bewerk):"

#: cps/templates/generate_kobo_auth_url.html:11
#, fuzzy
msgid "Kobo Token:"
msgstr "Kobo Sync Token"

#: cps/templates/grid.html:21
msgid "List"
msgstr "Lijst"

#: cps/templates/http_error.html:34
#, fuzzy
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "E-mailserver is niet geconfigureerd, neem contact op met de beheerder!"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Probleem melden"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "Databaseconfiguratie"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "Terug naar startpagina"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "Gebruiker uitloggen"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr "Sorteer oplopend volgens aantal downloads"

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr "Sorteer aflopend volgens aantal downloads"

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "Auteurs sorteren op alfabetische volgorde"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "Auteurs sorteren op omgekeerde alfabetische volgorde"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "Sorteer oplopend volgens de serie index"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "Sorteer aflopend volgens de serie index"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "Starten"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "Alfabetische Boeken"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "Boeken Alfabetisch gesorteerd"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "Populaire publicaties uit deze catalogus, gebaseerd op Downloads."

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "Populaire publicaties uit deze catalogus, gebaseerd op Beoordeling."

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "Recent toegevoegde boeken"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "Nieuwe boeken"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "Willekeurige boeken"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "Boeken gesorteerd op auteur"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "Boeken gesorteerd op uitgever"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "Boeken gesorteerd op categorie"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "Boeken gesorteerd op reeks"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "Boeken gesorteerd op taal"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "Boeken gesorteerd op beoordeling"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "Boeken gesorteerd op bestandsformaat"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "Boekenplanken"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "Boeken georganiseerd op boekenplanken"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "Navigatie aanpassen"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "Eenvoudig"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "Account"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "Instellingen"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "Deze pagina niet vernieuwen"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "Verkennen"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "Informatie"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "Boekgegevens"

#: cps/templates/list.html:22
msgid "Grid"
msgstr "Raster"

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "Gearchiveerd"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "Gegevens opslaan"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "Wachtwoord Vergeten?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "Inloggen met magische koppeling"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Toon Calibre-Web logbestand: "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Calibre-Web Logbestand: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "Stream uitvoer, kan niet worden weergegeven"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "Toon toegangslog: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Download Calibre-Web log"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "Download toegangslog"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "Selecteer toegestane/geweigerde tags"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "Selecteer toegestane/geweigerde aangepaste kolom waarden"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "Selecteer toegestane/geweigerde tags van gebruikers"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "Selecteer toegestane/geweigerde aangepaste kolom waarden van gebruikers"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "Voer Tag in"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "Voeg inkijk restrictie toe"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "Het boekformaat wordt permanent gewist uit de database"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "Het boek wordt verwijderd uit de Calibre-database"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "en van de harde schijf"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Belangrijke Kobo Opmerking: Verwijderde boeken zullen op alle gekoppelde Kobo Apparaten blijven."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "Boeken moeten eerst worden gearchiveerd en het apparaat moet worden gesynchroniseerd, voordat een boek veilig kan worden verwijderd."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "Kies bestandslocatie"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "type"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "naam"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "grootte"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "Bovenliggende map"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "Selecteer"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
#, fuzzy
msgid "Ok"
msgstr "Oké"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Calibre-Web - e-boekcatalogus"

#: cps/templates/read.html:7
#, fuzzy
msgid "epub Reader"
msgstr "PDF lezer"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "Kies een gebruikersnaam"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "Licht"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "Donker"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr "Sepia"

#: cps/templates/read.html:90
#, fuzzy
msgid "Black"
msgstr "Annuleren"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "Tekstindeling automatisch aanpassen als het zijpaneel geopend is."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr "Lettertypegrootte"

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "Verwijderen"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "Wachten"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "Verticaal"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "Gelezen"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "Ongeldige gelezen kolom"

#: cps/templates/readcbr.html:8
#, fuzzy
msgid "Comic Reader"
msgstr "Comic Reader"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "Sneltoetsen"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "Vorige pagina"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "Volgende pagina"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr "Weergave van één pagina"

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "Optimaal inpassen"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "Aanpassen aan breedte"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "Aanpassen aan hoogte"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "Ware grootte"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "Rechtsom draaien"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "Linksom draaien"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "Afbeelding omdraaien"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr "Weergeven"

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "Systeembeheer"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "Schaal"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "Beste"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "Breedte"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "Hoogte"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "Ware grootte"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "Draaien"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "Omdraaien"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "Horizontaal"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "Verticaal"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "Richting"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "Links-naar-rechts"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "Rechts-naar-links"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr "Terug naar boven"

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr "Onthoud positie"

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "Schuifbalk"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "Toon"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "Verberg"

#: cps/templates/readdjvu.html:5
#, fuzzy
msgid "DJVU Reader"
msgstr "PDF lezer"

#: cps/templates/readpdf.html:31
#, fuzzy
msgid "PDF Reader"
msgstr "PDF lezer"

#: cps/templates/readtxt.html:6
#, fuzzy
msgid "txt Reader"
msgstr "PDF lezer"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "Nieuw account registreren"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "Kies een gebruikersnaam"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "Je e-mailadres"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "Magische link - autoriseer een nieuw apparaat"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "Meld je aan op een ander apparaat en ga naar:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "Na controle wordt je automatisch op dit apparaat ingelogd."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "De link vervalt na 10 minuten."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr "Genereer serie omslagminiaturen"

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "Zoekterm:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "Resultaten voor:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "Publicatiedatum van"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "Publicatiedatum tot"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "Labels uitsluiten"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "Boekenreeksen uitsluiten"

#: cps/templates/search_form.html:96
#, fuzzy
msgid "Exclude Shelves"
msgstr "Boekenreeksen uitsluiten"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "Talen uitsluiten"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "Extenties"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "Extenties uitsluiten"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "Met beoordeling hoger dan"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "Met beoordeling lager dan"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "Van:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "Tot:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "Deze boekenplank verwijderen"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "Bewerk boekenplank eigenschappen"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "Boeken handmatig rangschikken"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "Schakel verandering van de volgorde uit"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "Schakel verandering van de volgorde in"

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "Sorteren op datum, nieuwste boeken eerst"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "Sorteren op datum, oudste boeken eerst"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "Delen met iedereen"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "Synchroniseer deze boekenplank met een Kobo-apparaat"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "Pas de volgorde aan middels slepen-en-neerzetten"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "Verborgen boek"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "Calibre bibliotheekstatistieken"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "Boeken in deze bibliotheek"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "Auteurs in deze bibliotheek"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "Categorieën in deze bibliotheek"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "Boekenreeksen in deze bibliotheek"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "Systeeminformatie"

#: cps/templates/stats.html:33
msgid "Program"
msgstr ""

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "Geïnstalleerde Versie"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "Gebruiker"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "Taak"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "Status"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "Voortgang"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "Looptijd"

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "Samenvoegen"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr "Acties"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr "Deze taak wordt geannuleerd. De voortgang van deze taak wordt opgeslagen."

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr "Als dit een geplande taak is wordt deze opnieuw uitgevoerd tijdens de volgende geplande tijd."

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "Gebruikerswachtwoord herstellen"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "Taal van boeken"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "OAuth Instellingen"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "Koppelen"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "Ontkoppelen"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Kobo Sync Token"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "Aanmaken/Bekijken"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr "Forceer volledige Kobo synchronisatie."

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "Voeg toegestane/geweigerde aangepaste kolomwaarden toe"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "Synchroniseer alleen boeken op geselecteerde boekenplanken met Kobo"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "Gebruiker verwijderen"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Genereer Kobo Auth URL"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "Selecteer..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "Gebruiker Bewerken"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "Voer gebruikersnaam in"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "Test-e-mail"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "Enter eReader Email"
msgstr "Kindle-e-mailadres"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "eReader Email"
msgstr "Test-e-mail"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "Locale"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "Zichtbare Boek Talen"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "Toegestaande labels bewerken"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "Toegestaande Labels"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "Verboden labels bewerken"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "Verboden Labels"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "Toegestane kolomwaarden bewerken"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "Toegestane kolomwaarden"

#: cps/templates/user_table.html:142
#, fuzzy
msgid "Edit Denied Column Values"
msgstr "Geweigerde kolomwaarden bewerken"

#: cps/templates/user_table.html:142
#, fuzzy
msgid "Denied Column Values"
msgstr "Geweigerde kolomwaarden"

#: cps/templates/user_table.html:144
#, fuzzy
msgid "Change Password"
msgstr "Wachtwoord wijzigen"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "Toon"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "Openbare boekenplank bewerken"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "Synchroniseer geselecteerde boekenplanken met Kobo"

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "Toon gelezen/niet gelezen selectie"

