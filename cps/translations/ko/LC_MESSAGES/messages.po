# Korean translation for Calibre-Web
# Copyright (C) 2024 limeade23
# This file is distributed under the same license as the Calibre-Web
# First Author <https://www.epubguide.net/>, 2022.
#
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/calibre-web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2024-11-01 17:50+0900\n"
"Last-Translator: limeade23 <<EMAIL>>\n"
"Language: ko\n"
"Language-Team: Korean <>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "통계"

#: cps/admin.py:151
msgid "Server restarted, please reload page."
msgstr "서버가 재시작 되었습니다. 페이지를 새로고침 해주세요."

#: cps/admin.py:153
msgid "Performing Server shutdown, please close window."
msgstr "서버를 종료하고 있습니다. 창을 닫아주세요."

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr "성공적으로 데이터베이스를 다시 연결했습니다."

#: cps/admin.py:164
msgid "Unknown command"
msgstr "알 수 없는 명령입니다."

#: cps/admin.py:175
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "메타데이터 백업이 작업에 추가되었습니다. 작업 결과를 확인해주세요."

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "알 수 없음"

#: cps/admin.py:233
msgid "Admin page"
msgstr "관리자 페이지"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "기본 설정"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "UI 설정"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "사용자 정의 열 번호 %(column)d이(가) calibre 데이터베이스에 없습니다."

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "사용자 관리"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "모두"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "사용자를 찾을 수 없습니다."

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} 사용자를 삭제했습니다."

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "모두 보기"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "잘못된 요청입니다."

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "게스트 이름은 변경할 수 없습니다."

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "게스트는 이 권한을 가질 수 없습니다."

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "남아있는 관리자가 없어 권한을 삭제할 수 없습니다."

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "true 또는 false여야 합니다."

#: cps/admin.py:507
msgid "Invalid role"
msgstr "잘못된 권한입니다."

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "게스트는 사용할 수 없습니다."

#: cps/admin.py:521
msgid "Invalid view"
msgstr "잘못된 설정입니다."

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "게스트의 언어는 자동으로 설정되며 변경할 수 없습니다."

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "올바른 언어가 아닙니다."

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "제공된 책의 언어가 올바르지 않습니다."

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "매개변수를 찾을 수 없습니다."

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "읽기 항목이 유효하지 않습니다."

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "제한된 항목이 유효하지 않습니다."

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Calibre-Web 설정이 업데이트되었습니다."

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Kobo 토큰을 삭제하시겠습니까?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "이 도메인을 삭제하시겠습니까?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "이 사용자를 삭제하시겠습니까?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "이 서재를 삭제하시겠습니까?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "선택한 사용자의 언어를 변경하시겠습니까?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "선택한 사용자에 대해 표시되는 책 언어를 변경하시겠습니까?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "선택한 사용자의 권한을 변경하시겠습니까?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "선택한 사용자의 제한을 변경하시겠습니까?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "선택한 사용자의 보기 제한을 변경하시겠습니까?"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "선택한 사용자의 책장 동기화 설정을 변경하시겠습니까?"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Calibre 라이브러리 경로를 변경하시겠습니까?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-Web이 업데이트된 표지를 검색하고 표지 미리보기를 업데이트합니다. 시간이 걸릴 수 있습니다."

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Kobo Reader와 전체 동기화를 강제하기 위해 Calibre-Web의 동기화 데이터베이스를 삭제하시겠습니까?"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "거부"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "허용"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{} 동기화 항목이 삭제되었습니다."

#: cps/admin.py:987
msgid "Tag not found"
msgstr "태그를 찾을 수 없습니다."

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "잘못된 작업입니다."

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json이 설정되지 않았습니다."

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "올바른 로그 파일의 경로를 입력해주세요."

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "올바른 액섹스 로그 파일의 경로를 입력해주세요."

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "LDAP 공급자, 포트, DN 및 사용자 개체 식별자를 입력해주세요."

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "LDAP 서비스 계정과 비밀번호를 입력해주세요."

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "LDAP 서비스 계정을 입력해주세요."

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAP 그룹 개체 필터에는 하나의 \"%s\" 형식 식별자가 필요합니다."

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAP 그룹 개체 필터에 맞지 않는 괄호가 있습니다."

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP 사용자 개체 필터에는 하나의 \"%s\" 형식 식별자가 필요합니다."

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAP 사용자 개체 필터에 맞지 않는 괄호가 있습니다."

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP 구성원 사용자 필터에는 하나의 \"%s\" 형식 식별자가 필요합니다."

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "LDAP 구성원 사용자 필터에 맞지 않는 괄호가 있습니다."

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAP CA인증서, 인증서 또는 키 경로가 유효하지 않습니다."

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "사용자 생성"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "이메일 서버 설정"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr "Gmail 계정 인증에 성공했습니다."

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "데이터베이스 오류가 발생했습니다: %(error)s."

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "%(email)s로 테스트 이메일을 전송했습니다."

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "테스트 이메일 전송 중 오류가 발생했습니다: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "이메일 주소를 설정해주세요."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "이메일 서버 설정이 업데이트되었습니다."

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "예약 작업 설정"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "작업 시작 시간이 올바르지 않습니다."

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "작업 종료 시간이 올바르지 않습니다."

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "예약 작업 설정이 업데이트되었습니다."

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "알 수 없는 오류가 발생했습니다. 잠시 후 다시 시도해 주세요."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "설정 데이터베이스에 저장할 수 없습니다."

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "사용자 %(nick)s 수정"

#: cps/admin.py:1457
#, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "사용자 %(user)s의 비밀번호가 재설정되었습니다."

#: cps/admin.py:1463
msgid "Oops! Please configure the SMTP mail settings."
msgstr "SMTP를 설정해주세요."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "로그 파일 보기"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "업데이트 패키지 요청 중"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "업데이트 패키지 다운로드 중"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "업데이트 패키지 압축 해제 중"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "파일 적용 중"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "데이터베이스 연결이 종료됩니다."

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "서버를 중지하고 있습니다."

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "업데이트가 완료되었습니다. 확인을 누르고 페이지를 새로고침해주세요."

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "업데이트 실패: "

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTP 오류가 발생했습니다."

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "연결 오류가 발생했습니다."

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "연결 시도 중 시간이 초과되었습니다."

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "일반 오류가 발생했습니다."

#: cps/admin.py:1551
msgid "Update file could not be saved in temp dir"
msgstr "임시 폴더에 업데이트 파일을 저장할 수 없습니다."

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "업데이트 중 파일을 변경할 수 없습니다."

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "최소 한 명의 LDAP 사용자 정보를 가져오지 못했습니다."

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "최소 한 명의 LDAP 사용자 정보를 생성하지 못했습니다."

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "오류: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "오류: LDAP 서버에서 반환된 사용자가 없습니다."

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "데이터베이스에서 찾을 수 없는 LDAP 사용자가 있습니다."

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} 명의 사용자를 가져왔습니다."

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr "책 경로가 유효하지 않습니다."

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "데이터베이스 경로가 유효하지 않습니다."

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "데이터베이스에 쓰기 권한이 없습니다."

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "키 파일 경로가 유효하지 않습니다."

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "인증서 파일 경로가 유효하지 않습니다."

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr "비밀번호는 1~40자 사이로 입력해주세요."

#: cps/admin.py:1917
msgid "Database Settings updated"
msgstr "데이터베이스 설정이 업데이트되었습니다."

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "데이터베이스 설정"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "모든 항목을 입력해주세요."

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "이메일 주소가 올바르지 않습니다."

#: cps/admin.py:1955
msgid "Add new user"
msgstr "새 사용자 생성"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "사용자 '%(user)s'이(가) 생성되었습니다."

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "이미 등록된 이메일 주소 또는 이름입니다."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "사용자 '%(nick)s'이(가) 삭제되었습니다."

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "게스트 사용자는 삭제할 수 없습니다."

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "남은 관리자가 없어 사용자를 삭제할 수 없습니다."

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr "이메일 주소는 비워둘 수 없고, 올바른 형식이어야 합니다."

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "사용자 '%(nick)s의 정보를 수정했습니다."

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "검색"

#: cps/converter.py:31
msgid "not installed"
msgstr "설치되지 않았습니다."

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "실행 권한이 없습니다."

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "None"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "파일 %(file)s이(가) 업로드되었습니다."

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "변환할 원본 또는 대상 형식이 지정되지 않았습니다."

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "%(book_format)s 형식으로 변환하기 위해 작업에 추가되었습니다."

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "책 변환 중 오류가 발생했습니다: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "선택한 책을 사용할 수 없습니다. 파일이 없거나 접근할 수 없습니다."

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "표지 업로드 권한이 없습니다."

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "식별자는 대소문자 구분 없어 덮어씁니다."

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "'%(langname)s'은(는) 올바른 언어가 아닙니다."

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "메타데이터가 업데이트되었습니다."

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "책 편집 중 오류가 발생했습니다: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "업로드된 책이 이미 존재하는 것 같습니다. 확인이 필요합니다: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
msgid "File type isn't allowed to be uploaded to this server"
msgstr "업로드할 수 없는 파일 형식입니다."

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "파일 확장자 '%(ext)s'은(는) 서버에 업로드할 수 없습니다"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "확장자가 없는 파일은 업로드할 수 없습니다."

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "파일 %(filename)s을(를) 임시 폴더에 저장할 수 없습니다."

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "표지 파일 %(file)s을(를) 이동하지 못했습니다 :%(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "책 형식이 삭제되었습니다."

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "책이 삭제되었습니다."

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "책을 삭제할 권한이 없습니다."

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "메타데이터 편집"

#: cps/editbooks.py:1016
#, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "시리즈 순서 %(seriesindex)s은(는) 올바르지 않아 건너뜁니다."

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "추가 파일 형식을 업로드할 권한이 없습니다."

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "쓰기 권한이 없어 %(path)s 경로 생성에 실패했습니다."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "%(file)s 파일 저장에 실패했습니다."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "파일 형식 %(ext)s이(가) %(book)s에 추가되었습니다"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Google 드라이브 설정이 완료되지 않았습니다. Google 드라이브를 비활성화한 후 다시 활성화해 주세요."

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "콜백 도메인이 인증되지 않았습니다. Google 개발자 콘솔에서 도메인 인증 절차를 진행해 주세요."

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "책 ID %(book)d의 %(format)s 형식을 찾을 수 없습니다."

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "Google 드라이브에서 %(format)s 파일을 찾을 수 없습니다: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s을(를) 찾을 수 없습니다: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
msgid "Send to eReader"
msgstr "전자책 리더로 보내기"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
msgid "This Email has been sent via Calibre-Web."
msgstr "이 이메일은 Calibre-Web으로 전송되었습니다."

#: cps/helper.py:123
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web 테스트 이메일"

#: cps/helper.py:124
msgid "Test Email"
msgstr "테스트 이메일"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Calibre-Web 시작하기"

#: cps/helper.py:146
#, python-format
msgid "Registration Email for user: %(name)s"
msgstr "사용자 %(name)s의 등록 이메일"

#: cps/helper.py:157 cps/helper.py:163
#, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "%(orig)s을(를) %(format)s로 변환하여 전자책 리더로 보내기"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, python-format
msgid "Send %(format)s to eReader"
msgstr "%(format)s을 전자책 리더로 보내기"

#: cps/helper.py:230
#, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s을(를) 전자책 리더로 보내기"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "요청한 파일을 읽을 수 없습니다. 파일 접근 권한을 확인해주세요."

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "읽기 상태를 설정할 수 없습니다: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "책 %(id)s의 폴더 삭제에 실패했습니다. %(path)s에 하위 폴더가 있습니다."

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "책 %(id)s 삭제에 실패했습니다: %(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "데이터베이스 경로가 올바르지 않아 책 %(id)s을(를) 데이터베이스에서만 삭제합니다: %(path)s"

#: cps/helper.py:439
#, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "저자명 '%(src)s'에서 '%(dest)s'(으)로 변경하지 못했습니다: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Google 드라이브에서 %(file)s 파일을 찾을 수 없습니다."

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "제목을 '%(src)s'에서 '%(dest)s'(으)로 변경하지 못했습니다: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Google 드라이브에서 경로 %(path)s을(를) 찾을 수 없습니다."

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr "이미 사용 중인 이메일 주소입니다."

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "이미 사용 중인 사용자 이름입니다."

#: cps/helper.py:679
msgid "Invalid Email address format"
msgstr "올바른 이메일 주소가 아닙니다."

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr "비밀번호가 설정된 규칙에 맞지 않습니다."

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "표지 업로드에 필요한 Python 모듈 'advocate'이 설치되지 않았습니다."

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "표지 다운로드 중 오류가 발생했습니다."

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "표지 형식 오류입니다."

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "표지 업로드를 위해 로컬 호스트나 로컬 네트워크에 접근할 수 없습니다."

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "표지 경로 생성에 실패했습니다."

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "표지 파일이 올바른 이미지 파일이 아니거나 저장할 수 없습니다."

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "jpg/jpeg/png/webp/bmp 형식만 지원합니다."

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "올바르지 않은 표지 파일입니다."

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "표지 파일은 jpg/jpeg 형식만 지원합니다."

#: cps/helper.py:989 cps/helper.py:1149
msgid "Cover"
msgstr "표지"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "UnRar 바이너리 파일을 찾을 수 없습니다."

#: cps/helper.py:1017
msgid "Error executing UnRar"
msgstr "UnRar 실행 중 오류가 발생했습니다."

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr "지정된 디렉토리를 찾을 수 없습니다."

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr "파일이 아닌 폴더를 지정하세요."

#: cps/helper.py:1042
msgid "Calibre binaries not viable"
msgstr "Calibre 바이너리 파일을 사용할 수 없습니다."

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr "Calibre 바이너리 파일이 누락되었습니다: %(missing)s"

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "실행 권한이 누락되었습니다: %(missing)s"

#: cps/helper.py:1058
msgid "Error executing Calibre"
msgstr "Calibre 실행 오류"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr "메타데이터 백업이 작업에 추가되었습니다."

#: cps/kobo_auth.py:92
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Kobo 장치에 대한 유효한 api_endpoint를 얻으려면 로컬 호스트가 아닌 곳에서 Calibre-Web에 접근하세요."

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo 설정"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "%(provider)s에 등록"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "%(nickname)s로 로그인했습니다."

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "%(oauth)s에 성공적으로 연결되었습니다."

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "로그인에 실패했습니다. OAuth 계정과 연결된 사용자가 없습니다."

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "%(oauth)s에 연결 해제에 성공했습니다."

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "%(oauth)s에 연결 해제에 실패했습니다."

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "%(oauth)s에 연결되지 않았습니다."

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "GitHub 로그인에 실패했습니다."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "GitHub에서 사용자 정보를 불러오지 못했습니다."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Google 로그인에 실패했습니다."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Google에서 사용자 정보를 불러오지 못했습니다."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub Oauth 오류입니다. 잠시 후 다시 시도해 주세요."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "GitHub 인증 오류가 발생했습니다: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google 인증 오류입니다. 잠시 후 다시 시도해 주세요."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google 인증 오류가 발생했습니다: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} Stars"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "로그인"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "토큰을 찾을 수 없습니다."

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "토큰이 만료되었습니다."

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "인증되었습니다. 기기로 돌아가 주세요."

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "전체"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "최근 책 보기"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "인기"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "인기 책 보기"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "받은 적 있는"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "다운로드 받은 적 있는 책 보기"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "베스트"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "평점 높은 책 보기"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "읽음"

#: cps/render_template.py:63
msgid "Show Read and Unread"
msgstr "읽은 책과 안 읽은 책 보기"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "읽지 않음"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "읽지 않은 책 보기"

#: cps/render_template.py:68
msgid "Discover"
msgstr "둘러보기"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "무작위 추천 보기"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "카테고리"

#: cps/render_template.py:73 cps/templates/user_table.html:158
msgid "Show Category Section"
msgstr "카테고리 보기"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "시리즈"

#: cps/render_template.py:76 cps/templates/user_table.html:157
msgid "Show Series Section"
msgstr "시리즈 보기"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "저자"

#: cps/render_template.py:79 cps/templates/user_table.html:160
msgid "Show Author Section"
msgstr "저자 보기"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "출판사"

#: cps/render_template.py:83 cps/templates/user_table.html:163
msgid "Show Publisher Section"
msgstr "출판사 보기"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "언어"

#: cps/render_template.py:87 cps/templates/user_table.html:155
msgid "Show Language Section"
msgstr "언어 보기"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "평점"

#: cps/render_template.py:90 cps/templates/user_table.html:164
msgid "Show Ratings Section"
msgstr "평점 보기"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "파일 형식"

#: cps/render_template.py:93 cps/templates/user_table.html:165
msgid "Show File Formats Section"
msgstr "파일 형식 보기"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "보관함"

#: cps/render_template.py:97 cps/templates/user_table.html:166
msgid "Show Archived Books"
msgstr "보관된 책 보기"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "책 목록"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "책 목록 보기"

#: cps/search.py:201
msgid "Published after "
msgstr "발행일 이후"

#: cps/search.py:208
msgid "Published before "
msgstr "발행일 이전"

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "평점 <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "평점 >= %(rating)s"

#: cps/search.py:234
#, python-format
msgid "Read Status = '%(status)s'"
msgstr "읽은 상태 = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "사용자 정의 열을 검색하는 동안 오류가 발생했습니다. Calibre-Web을 다시 시작해 주세요."

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "상세 검색"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "잘못된 서재가 지정되었습니다."

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "추가할 권한이 없습니다."

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "이미 등록된 책입니다: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr "추가할 수 없습니다. %(book_id)s는 잘못된 책 ID입니다."

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "책이 등록되었습니다: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "책을 추가할 권한이 없습니다."

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "이미 등록된 책입니다: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "책들이 등록되었습니다: %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "책을 추가할 수 없습니다: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "책이 서재에서 삭제되었습니다: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "서재에서 책을 삭제할 권한이 없습니다."

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "서재 생성"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "서재를 수정할 권한이 없습니다."

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "서재 편집"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "서재를 삭제하는 중 오류가 발생했습니다."

#: cps/shelf.py:239
msgid "Shelf successfully deleted"
msgstr "서재가 삭제되었습니다."

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "서재 순서가 변경되었습니다: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "공개 서재를 생성할 권한이 없습니다."

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "서재 %(title)s가 생성되었습니다."

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "서재 %(title)s가 변경되었습니다."

#: cps/shelf.py:358
msgid "There was an error"
msgstr "오류가 발생했습니다."

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "'%(title)s' 공개 서재가 이미 존재합니다."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "'%(title)s' 개인 서재가 이미 존재합니다."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "서재: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "서재를 여는 동안 오류가 발생했습니다. 서재가 존재하지 않거나 접근할 수 없습니다"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "작업"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "대기 중"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "실패"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "시작됨"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "종료됨"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "종료됨"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "취소됨"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "알 수 없는 상태"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "업데이트 정보를 읽는 중 알 수 없는 문제가 발생했습니다."

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "업데이트가 없습니다. 최신 버전이 설치되어 있습니다."

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "새로운 업데이트가 있습니다. 아래 버튼을 클릭해 최신 버전으로 업데이트하세요."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "업데이트 정보를 가져올 수 없습니다."

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "아래 버튼을 클릭해 최신 버전으로 업데이트하세요."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "새로운 업데이트가 있습니다. %(version)s으로 업데이트하려면 아래 버튼을 클릭하세요."

#: cps/updater.py:538
msgid "No release information available"
msgstr "업데이트 정보가 없습니다."

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "추천 (무작위)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "인기 책 (가장 많이 다운로드된 책)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "%(user)s이(가) 다운로드한 책"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "저자: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "출판사: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "시리즈: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "평점: 없음"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "평점: %(rating)s"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "파일 형식: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "카테고리: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "언어: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "다운로드"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "평점 목록"

#: cps/web.py:1100
msgid "File formats list"
msgstr "파일 형식 목록"

#: cps/web.py:1259
msgid "Please configure the SMTP mail settings first..."
msgstr "SMTP 메일 설정을 해주세요."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "%(eReadermail)s에 전송 작업이 추가되었습니다."

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "전송 중 오류가 발생했습니다: %(res)s"

#: cps/web.py:1270
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "문제가 발생했습니다. 프로필에 유효한 전자책 리더 이메일을 설정해 주세요."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr "다음 사용자를 등록은 1분 뒤 가능합니다."

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "등록"

#: cps/web.py:1290 cps/web.py:1393
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "Flask-Limiter 시스템 연결에 문제가 발생했습니다. 관리자에게 문의하세요."

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "이메일 서버가 설정되지 않았습니다. 관리자에게 문의하세요."

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "사용할 수 없는 이메일입니다."

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "인증 이메일을 발송했습니다."

#: cps/web.py:1376 cps/web.py:1399
msgid "Cannot activate LDAP authentication"
msgstr "LDAP 인증을 활성화할 수 없습니다."

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr "1분 후 시도해 주세요."

#: cps/web.py:1408
#, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "'%(nickname)s'로 로그인했습니다."

#: cps/web.py:1415
#, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "'%(nickname)s'(으)로 대체 로그인했습니다. LDAP 서버에 연결할 수 없거나 존재하지 않는 사용자입니다."

#: cps/web.py:1420
#, python-format
msgid "Could not login: %(message)s"
msgstr "로그인할 수 없습니다: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
msgid "Wrong Username or Password"
msgstr "사용자 이름 또는 비밀번호가 올바르지 않습니다."

#: cps/web.py:1431
msgid "New Password was sent to your email address"
msgstr "새 비밀번호가 이메일로 전송되었습니다"

#: cps/web.py:1435
msgid "An unknown error occurred. Please try again later."
msgstr "알 수 없는 오류가 발생했습니다. 나중에 다시 시도해 주세요."

#: cps/web.py:1437
msgid "Please enter valid username to reset password"
msgstr "비밀번호를 재설정하려면 올바른 사용자 이름을 입력하세요."

#: cps/web.py:1445
#, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "'%(nickname)s'로 로그인했습니다."

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "%(name)s 프로필"

#: cps/web.py:1526
msgid "Success! Profile Updated"
msgstr "프로필이 업데이트되었습니다."

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "이미 등록된 이메일 주소입니다."

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "인증 정보가 포함된 유요한 gmail.json을 찾을 수 없습니다."

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr "임시 폴더 비우기"

#: cps/tasks/convert.py:112
#, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s을 전자책 리더로 전송"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Calibre ebook-convert %(tool)s을(를) 찾을 수 없습니다."

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s 형식을 찾을 수 없습니다."

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "알 수 없는 오류로 변환에 실패했습니다."

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify 변환에 실패했습니다: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "변환된 파일을 찾을 수 없거나 %(folder)s 폴더에 하나 이상의 파일이 존재합니다."

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre가 다음 오류로 실패했습니다: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "변환에 실패했습니다: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "변환"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Calibre 데이터베이스를 다시 연결합니다."

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "이메일"

#: cps/tasks/metadata_backup.py:34
msgid "Backing up Metadata"
msgstr "메타데이터를 백업하고 있습니다."

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "%(count)s개의 표지 섬네일을 생성했습니다."

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "표지 섬네일"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "{0}개의 시리즈 섬네일을 생성했습니다."

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "표지 섬네일 캐시를 삭제합니다."

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "업로드"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "사용자"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "사용자 이름"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "이메일 주소"

#: cps/templates/admin.html:15
msgid "Send to eReader Email"
msgstr "전자책 리더로 보낼 이메일"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "관리자"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "비밀번호"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "다운로드"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "책 보기"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "편집"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "삭제"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "공개 서재"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "LDAP 사용자 가져오기"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "이메일 서버 설정"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTP 서버명"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTP 포트"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "보안 연결"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTP 로그인"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "보내는 이메일"

#: cps/templates/admin.html:90
msgid "Email Service"
msgstr "이메일 서비스"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via Oauth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "설정"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Calibre 데이터베이스 경로"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "로그 레벨"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "포트"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "외부 포트"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "페이지당 표시 개수"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "업로드"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "익명 보기"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "공개 등록"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "매직 링크 원격 로그인"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "리버스 프록시 로그인"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "리버스 프록시 헤더 이름"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Calibre 데이터베이스 설정"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "기본 설정"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "UI 설정"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "예약된 작업"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr "시작 시간"

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr "최대 기간"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr "섬네일 생성"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "시리즈 표지 섬네일 생성"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Calibre 데이터베이스 다시 연결"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr "메타데이터 백업"

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr "섬네일 캐시 새로 고침"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "관리"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "디버그 패키지 다운로드"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "로그 보기"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "재시작"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "종료"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "버전 정보"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "버전"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "세부 정보"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "현재 버전"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "업데이트 확인"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "업데이트 실행"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "재시작하시겠습니까?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "예"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "취소"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "종료하시겠습니까?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "업데이트 중입니다. 이 페이지를 새로고침 하지 마세요."

#: cps/templates/author.html:15
msgid "via"
msgstr "통해"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "서재에 있음"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "등록 최신순"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "등록 오래된 순"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "이름순"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "이름 역순"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "발행일 최신순"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "발행일 오래된 순"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "줄이기"

#: cps/templates/author.html:97
msgid "More by"
msgstr "더보기"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, python-format
msgid "Book %(index)s of %(range)s"
msgstr "책 %(index)s / %(range)s"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "언어"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "출판사"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "발행일"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "설명:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "이전"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "다음"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "검색 결과가 없습니다."

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "홈"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "검색"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "로그아웃"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "책 삭제"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "형식 삭제:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "책 형식 변경:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "변환 전 유형:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "옵션 선택"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "변환 후 유형:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "책 변환"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "업로드 중"

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "닫기"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "오류"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "업로드 완료, 처리 중입니다. 잠시만 기다려 주세요."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "다른 형식 업로드"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "책 제목"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "저자"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "태그"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "시리즈 ID"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "발행일"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "평점"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "설명"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "식별자"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "식별자 유형"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "식별자 값"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "삭제"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "식별자 추가"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "URL로 표지 추가 (JPEG 이미지를 다운로드합니다)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "내 컴퓨터에서 표지 업로드"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "예"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "아니오"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "저장 후 상세정보로"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "메타데이터 가져오기"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "저장"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "키워드"

#: cps/templates/book_edit.html:240
msgid "Search keyword"
msgstr "키워드 검색"

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "검색 결과를 사용하려면 표지를 클릭하세요."

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "불러오는 중"

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "출처"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "오류가 발생했습니다!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "검색 결과가 없습니다. 다른 검색어로 검색해 보세요."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "이 항목은 필수입니다."

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "선택한 책 병합"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "선택 항목 삭제"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "저자와 제목 바꾸기"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "자동으로 제목 정렬 업데이트"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "자동으로 저자 정렬 업데이트"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "제목 입력"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "제목"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "제목 정렬 입력"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "제목 정렬"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "저자 정렬 입력"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "저자 정렬"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "저자 입력"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "카테고리 입력"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "시리즈 입력"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "시리즈 순서"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "언어 입력"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "발행일"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "출판사 입력"

#: cps/templates/book_table.html:73
msgid "Enter comments"
msgstr "설명 입력"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "설명"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "보관 상태"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "읽은 상태"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
msgid "Enter "
msgstr "입력 "

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "정말로 하시겠습니까?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "다음 책들을 병합합니다:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "다음 책으로 병합:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "병합"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Calibre 데이터베이스 경로"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr "책 저장 경로 분리"

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Google 드라이브 사용"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Google 드라이브 인증"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Google 드라이브 Calibre 폴더"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "Google 드라이브 API ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "취소"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "데이터베이스 경로가 올바르지 않습니다. 유효한 경로를 입력해주세요."

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "서버 설정"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "서버 포트"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "SSL 인증서 파일 경로 (미사용 시 공란)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "SSL 키 파일 경로 (미사용시 공란)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "업데이트 채널"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "안정 버전"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "실험 버전"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "신뢰할 수 있는 호스트 (쉼표로 구분)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "로그 파일 설정"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "로그 파일 경로 및 파일명 (공란일 경우 calibre-web.log로 저장됩니다)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "액세스 로그 사용"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "액세스 로그 파일 경로 및 파일명 (공란일 경우 access.log로 저장됩니다)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "기능 설정"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "제목과 저자가 영문이 아닐 경우 영문(발음 그대로)으로 변환하여 저장"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr "다운로드/변환/이메일 전송 시 전자책 파일에 메타데이터 포함 (Calibre/Kepubify 필요)"

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "업로드 사용"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(사용자에게 업로드 권한이 있어야 가능)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "허용할 업로드 파일 형식"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "익명 탐색 사용"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "공개 등록 사용"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "이메일을 사용자 이름으로 사용"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "매직 링크 원격 로그인 사용"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Kobo sync 사용"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Kobo 스토어에 알 수 없는 요청 프록시 전달"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "서버 외부 포트 (포트 포워딩 API 호출용)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Goodreads 사용"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Goodreads API 키"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "리버스 프록시 인증 허용"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "로그인 방식"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "기본 인증 사용"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "LDAP 인증 사용"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "OAuth 사용"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAP 서버 호스트 이름 또는 IP 주소"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAP 서버 포트"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAP 암호화"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP CA 인증서 경로 (클라이언트 인증서 인증 시에만 필요)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP 인증서 경로 (클라이언트 인증서 인증 시에만 필요)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP 키 파일 경로 (클라이언트 인증서 인증 시에만 필요)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP 인증"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "익명"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "미인증"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "단순 인증"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP 관리자 아이디"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP 관리자 비밀번호"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP 식별 이름 (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP 사용자 객체 필터"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "OpenLDAP 서버인 경우 체크"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "사용자를 가져오기 위해 다음 설정이 필요합니다."

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAP 그룹 객체 필터"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAP 그룹 이름"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "LDAP 그룹 구성원 필드"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAP 사용자 감지 필터"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "자동 감지"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "사용자 지정 필터"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "LDAP 구성원 사용자 필터"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "%(provider)s OAuth 인증 정보 받기"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth 클라이언트 ID"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth 클라이언트 비밀키"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "외부 바이너리 설정"

#: cps/templates/config_edit.html:325
msgid "Path to Calibre Binaries"
msgstr "Calibre 바이너리 경로"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Calibre E-Book 컨버터 설정"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Kepubify 전자책 컨버터 경로"

#: cps/templates/config_edit.html:344
msgid "Location of Unrar binary"
msgstr "UnRar 바이너리 경로"

#: cps/templates/config_edit.html:360
msgid "Security Settings"
msgstr "보안 설정"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr "로그인 실패 횟수 제한"

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr "Flask-Limiter 데이터베이스 주소"

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr "Flask-Limiter 옵션"

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr "업로드 시 파일 형식 일치 검사"

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr "세션 보호"

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr "기본"

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr "강력"

#: cps/templates/config_edit.html:393
msgid "User Password policy"
msgstr "비밀번호 강도 규칙 사용"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr "최소 비밀번호 길이"

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr "숫자 필수"

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr "소문자 필수"

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr "대문자 필수"

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr "한자/일본어/한국어 문자 필수"

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr "특수문자 필수"

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "환경 설정"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "랜덤 보기시 표시할 책 수"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "숨기기 전 표시할 저자 수 (0=숨기기 비활성화)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "테마"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "기본 테마"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "caliBlur! 다크 테마"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "열을 무시하는 정규식"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "읽음/읽지 않음 상태를 Calibre 데이터에 저장"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Calibre 데이터 기반 화면 표시 제한"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "제목 정렬 정규식"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "새 사용자 기본 설정"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "관리자"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "다운로드 허용"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "책 보기 허용"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "업로드 허용"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "편집 허용"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "책 삭제 허용"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "비밀번호 변경 허용"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "공개 서재 편집 허용"

#: cps/templates/config_view_edit.html:123
msgid "Default Language"
msgstr "기본 언어"

#: cps/templates/config_view_edit.html:131
msgid "Default Visible Language of Books"
msgstr "책의 기본 표시 언어"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "새 사용자 기본 기능"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "목록 보기에서 무작위 추천 표시"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "허용/거부 태그 추가"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "허용/거부 사용자 정의 열 값 추가"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "웹에서 보기"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "웹에서 듣기"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "읽지 않음으로 표시"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "읽음으로 표시"

#: cps/templates/detail.html:262
msgid "Mark Book as Read or Unread"
msgstr "읽음/읽지 않음으로 표시"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "읽음"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "보관함에서 복원"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "보관함에 추가"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr "책을 보관하면 Calibre-Web에서 숨겨지고 Kobo 리더에서 삭제됩니다."

#: cps/templates/detail.html:275
msgid "Archive"
msgstr "보관"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "서재에 추가"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(공개)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "메타데이터 편집"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "이메일 유형 선택"

#: cps/templates/email_edit.html:15
msgid "Standard Email Account"
msgstr "표준 이메일 계정"

#: cps/templates/email_edit.html:16
msgid "Gmail Account"
msgstr "Gmail 계정"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr "Gmail 계정 설정"

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Gmail 접근 권한 해제"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "SMTP 비밀번호"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "첨부파일 최대 크기"

#: cps/templates/email_edit.html:66
msgid "Save and Send Test Email"
msgstr "저장 및 테스트 메일 전송"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "뒤로"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "허용하는 도메인 (화이트 리스트)"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "도메인 추가"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "추가"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "도메인 입력"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "차단 도메인 (블랙리스트)"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "텍스트 편집기에서 .kobo/Kobo/Kobo eReader.conf 파일을 열고 다음을 추가하거나 수정하세요:"

#: cps/templates/generate_kobo_auth_url.html:11
msgid "Kobo Token:"
msgstr "Kobo 토큰:"

#: cps/templates/grid.html:21
msgid "List"
msgstr "목록"

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "Calibre-Web이 설정되지 않았습니다. 관리자에게 문의하세요."

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "이슈 생성"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "데이터베이스 설정"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "홈으로"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "로그아웃"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr "다운로드 수 오름차순 정렬"

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr "다운로드 수 내림차순 정렬"

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "저자명 오름차순 정렬"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "저자명 내림차순 정렬"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "시리즈 순서 오름차순 정렬"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "시리즈 순서 내림차순 정렬"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "시작하기"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "책 이름 오름차순 정렬"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "책 이름 오름차순 정렬"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "다운로드 기준 인기 책"

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "평점 기준 인기 책"

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "최근 추가된 책"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "최신 책"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "랜덤 책"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "저자별 정렬"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "출판사별 정렬"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "카테고리별 정렬"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "시리즈별 정렬"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "언어별 정렬"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "평점별 정렬"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "파일 형식별 정렬"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "서재"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "서재별 정렬"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "메뉴 열기/닫기"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "단순 인증"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "계정"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "설정"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "페이지를 새로고침하지 마세요."

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "탐색"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "서재 정보"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "책 상세정보"

#: cps/templates/list.html:22
msgid "Grid"
msgstr "그리드"

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "보관됨"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "로그인 유지"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "비밀번호를 잊으셨나요?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "매직 링크로 로그인"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Calibre-Web 로그 표시: "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Calibre-Web 로그: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "스트림 출력, 표시할 수 없음"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "액세스 로그 표시: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Calibre-Web 로그 다운로드"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "액세스 로그 다운로드"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "허용/거부 태그 선택"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "허용/거부 사용자 정의 항목 선택"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "사용자의 허용/거부 태그 선택"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "사용자 정의 항목 허용/거부 선택"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "태그 입력"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "보기 제한 추가"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "이 책의 형식은 데이터베이스에서 삭제됩니다."

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "이 책은 데이터베이스에서 삭제됩니다."

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "그리고 디스크에서도 삭제됩니다."

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Kobo 알림: 삭제된 책은 연결된 Kobo 기기에 남아있습니다."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "책을 안전하게 삭제하려면 먼저 보관 처리 후 기기 동기화가 필요합니다."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "파일 위치 선택"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "유형"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "이름"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "크기"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "상위 폴더"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "선택"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "확인"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Calibre-Web 책 목록"

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr "epub 리더"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "사용자 이름 선택"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "밝은색"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "어두운색"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr "세피아"

#: cps/templates/read.html:90
msgid "Black"
msgstr "검은색"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "사이드바가 열릴 경우 텍스트를 재정렬합니다."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr "글자 크기"

#: cps/templates/read.html:105
msgid "Font"
msgstr "글꼴"

#: cps/templates/read.html:106
msgid "Default"
msgstr "기본값"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr "Yahei"

#: cps/templates/read.html:108
msgid "SimSun"
msgstr "SimSun"

#: cps/templates/read.html:109
msgid "KaiTi"
msgstr "KaiTi"

#: cps/templates/read.html:110
msgid "Arial"
msgstr "Arial"

#: cps/templates/read.html:113
msgid "Spread"
msgstr "페이지 보기"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr "두쪽 보기"

#: cps/templates/read.html:115
msgid "One column"
msgstr "한쪽 보기"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr "만화 리더"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "키보드 단축키"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "이전 페이지"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "다음 페이지"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr "한 페이지 보기"

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr "연속 보기"

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "최적 크기"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "너비에 맞춤"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "높이에 맞춤"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "원본 크기"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "오른쪽으로 회전"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "왼쪽으로 회전"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "이미지 반전"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr "표시 방식"

#: cps/templates/readcbr.html:113
msgid "Single Page"
msgstr "단일 페이지"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr "세로 스크롤"

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "크기 조절"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "최적"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "폭"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "높이"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "원본 크기"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "회전"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "뒤집기"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "수평"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "수직"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "방향"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "왼쪽에서 오른쪽"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "오른쪽에서 왼쪽"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr "맨 위로 이동"

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr "위치 기억하기"

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "스크롤바"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "보기"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "숨기기"

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr "DJVU 리더"

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr "PDF 리더"

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr "텍스트 리더"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "새 사용자 등록"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "사용자 이름 선택"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "이메일 주소"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "매직 링크 - 새 장치 승인"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "다른 기기에서 로그인하여 방문하세요:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "승인되면 자동으로 로그인 됩니다."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "인증 링크는 10분 후 만료됩니다."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr "시리즈 미리보기 표지 생성"

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "검색어:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "결과:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "발행일(시작)"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "발행일(종료)"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr "모두"

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr "없음"

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "태그 제외"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "시리즈 제외"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "서재 제외"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "언어 제외"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "확장자"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "확장자 제외"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "평점(이상)"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "평점(이하)"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "시작:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "종료:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "이 서재 삭제"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "서재 속성 편집"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "수동으로 책 정렬"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "정렬 변경 비활성화"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "정렬 변경 활성화"

#: cps/templates/shelf.html:28
msgid "Sort according to book added to shelf, newest first"
msgstr "추가된 기준으로 최신순"

#: cps/templates/shelf.html:29
msgid "Sort according to book added to shelf, oldest first"
msgstr "추가된 기준으로 오래된 순"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "모두와 공유"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "이 서재를 Kobo 기기와 동기화"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "드래그하여 순서 변경"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "숨긴 책"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "서재 통계"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "이 서재의 책들"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "이 서재의 저자"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "이 서재의 카테고리"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "이 서재의 시리즈"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "시스템 통계"

#: cps/templates/stats.html:33
msgid "Program"
msgstr "프로그램"

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "설치 버전"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "사용자"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "작업"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "상태"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "진행률"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "실행 시간"

#: cps/templates/tasks.html:19
msgid "Message"
msgstr "메시지"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr "작업"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr "작업이 취소됩니다. 진행된 부분은 저장됩니다."

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr "예약된 작업일 경우, 다음 예약 시간에 이어서 실행됩니다."

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "사용자 비밀번호 초기화"

#: cps/templates/user_edit.html:28
#, fuzzy
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr "전자책 리더로 전송할 이메일 (여러 전자책 리더를 사용할 경우 쉼표로 구분하여 입력)"

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "책 언어"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "OAuth 설정"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "연결"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "연결 해제"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Kobo 동기화 토큰"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "생성/보기"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr "강제 Kobo 전체 동기화"

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "허용/거부할 사용자 정의 항목 추가"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "선택한 서재의 책만 Kobo와 동기화"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "사용자 삭제"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Kobo 인증 URL 생성"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "선택"

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "사용자 편집"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "사용자 이름 입력"

#: cps/templates/user_table.html:135
msgid "Enter Email"
msgstr "이메일 입력"

#: cps/templates/user_table.html:136
msgid "Enter eReader Email"
msgstr "전자책 리더 이메일 입력"

#: cps/templates/user_table.html:136
msgid "eReader Email"
msgstr "전자책 리더 이메일"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "언어"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "표시할 책 언어"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "허용 태그 편집"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "허용 태그"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "제외 태그 편집"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "제외 태그"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "허용 항목 편집"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "허용 항목"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "제외 항목 편집"

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr "제외 항목"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "비밀번호 변경"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "보기"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "공개 서재 편집"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "선택한 서재 Kobo 동기화"

#: cps/templates/user_table.html:156
msgid "Show Read/Unread Section"
msgstr "읽음/읽지 않음 표시"

