# Indonesian translations for Calibre-Web.
# Copyright (C) 2017 Calibre-Web
# This file is distributed under the same license as the Calibre-Web
# project.
# FIRST AUTHOR Arief Hidayat<<EMAIL>>, 2021-2023.
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/Calibre-Web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2023-01-21 10:00+0700\n"
"Last-Translator: Arief <PERSON>dayat<<EMAIL>>\n"
"Language: id\n"
"Language-Team: Arief Hidayat <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statistik"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "Server dimulai ulang, harap muat ulang halaman"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "Mematikan server, silakan tutup jendela"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr ""

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Perintah tidak diketahui"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Uji email diantrean untuk dikirim ke %(email), harap periksa Tasks untuk hasilnya"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Tidak diketahui"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Halaman Admin"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Pengaturan Dasar"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "Pengaturan Antarmuka"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "Kolom Kustom No.%(column)d tidak ada di basis data kaliber"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "Edit pengguna"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Semua"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Pengguna tidak ditemukan"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} pengguna berhasil dihapus"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Tampilkan semua"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Permintaan salah"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Nama Tamu tidak dapat diganti"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "Tamu tidak dapat memiliki peran ini"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Tidak ada pengguna admin yang tersisa, tidak dapat menghapus peran admin"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Nilai harus benar atau salah"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Peran tidak valid"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr " Tamu tidak dapat mengakses tampilan ini"

#: cps/admin.py:521
msgid "Invalid view"
msgstr " Tampilan tidak valid"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Lokal Tamu ditentukan secara otomatis dan tidak dapat disetel"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Tidak Ada Lokal yang Valid Diberikan"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Tidak Ada Bahasa Buku yang Valid Diberikan"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Parameter tidak ditemukan"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "Kolom Baca Tidak Valid"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "Kolom Dibatasi Tidak Valid"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Pengaturan Calibre-Web telah diperbarui"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Apakah Anda yakin ingin menghapus Token Kobo?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Apakah Anda yakin ingin menghapus domain ini?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Apakah Anda yakin ingin menghapus pengguna ini?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Apakah Anda yakin ingin menghapus rak ini?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Apakah Anda yakin ingin merubah lokalisasi untuk pengguna yang dipilih?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Apakah Anda yakin ingin merubah bahasa buku yang terlihat untuk pengguna yang dipilih?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Apakah Anda yakin ingin merubah peran untuk pengguna yang dipilih?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Apakah Anda yakin ingin mengubah batasan yang dipilih untuk pengguna yang dipilih?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Apakah Anda yakin ingin merubah batasan visibilitas untuk pengguna yang dipilih?"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Apakah Anda yakin ingin mengubah perilaku sinkronisasi rak untuk pengguna yang dipilih?"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Apakah Anda yakin ingin mengubah lokasi perpustakaan Calibre?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-Web akan mencari Sampul yang diperbarui dan memperbarui Thumbnail Sampul, ini mungkin memakan waktu cukup lama?"

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Apakah Anda yakin ingin menghapus database sinkronisasi Calibre-Web untuk memaksakan sinkronisasi penuh dengan Kobo Reader Anda?"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Tolak"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Izinkan"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{} entri sinkronisasi dihapus"

#: cps/admin.py:987
msgid "Tag not found"
msgstr "Tag tidak ditemukan"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Tindakan Tidak Valid"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json Tidak Diatur Untuk Aplikasi Web"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokasi Logfile tidak Valid, Harap Masukkan Jalur yang Benar"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Akses Logfile Catatan tidak Valid, Harap Masukkan Jalur yang Benar"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Harap Masukkan Provider LDAP, Port, DN dan User Obect Identifier"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Masukkan Akun Layanan LDAP dan Kata Sandi"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "Masukkan Akun Layanan LDAP"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "Filter Objek Grup LDAP Harus Memiliki Satu Pengidentifikasi Format \"%s\""

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "Filter Objek Grup LDAP Memiliki Tanda kurung yang Tak Berpasangan"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "Filter Objek Pengguna LDAP harus Memiliki Satu Pengidentifikasi Format \"%s\""

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "Filter Objek Pengguna LDAP Memiliki Tanda kurung yang Tak Berpasangan"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "Filter Pengguna Anggota LDAP harus Memiliki Satu Pengenal Format \"%s\""

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "Filter Pengguna Anggota LDAP Memiliki Tanda Kurung yang Tak Berpasangan"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "Lokasi LDAP Sertifikat CA, Sertifikat, atau Kunci tidak Valid, Harap Masukkan Jalur yang Benar "

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Tambah Pengguna Baru"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "Edit Pengaturan Server Email"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr ""

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Kesalahan basis data: %(error)s"

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "Uji email diantrean untuk dikirim ke %(email), harap periksa Tasks untuk hasilnya"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Terjadi kesalahan saat mengirim email tes: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Harap atur alamat email Anda terlebih dahulu.."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "Setelan server email diperbarui"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "Edit Pengaturan Tugas Terjadwal"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "Waktu mulai tidak valid untuk tugas yang ditentukan"

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "Durasi tidak valid untuk tugas yang ditentukan"

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "Pengaturan tugas terjadwal diperbarui"

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "Terjadi kesalahan yang tidak diketahui. Coba lagi nanti."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "Pengaturan DB tidak dapat ditulisi"

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Edit pengguna %(nick)s"

#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Kata sandi untuk pengaturan ulang pengguna %(user) "

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Harap atur pengaturan email SMTP terlebih dahulu..."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Penampil berkas log"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Meminta paket pembaruan"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Mengunduh paket pembaruan"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Mengekstrak paket pembaruan"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Mengganti berkas"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Koneksi basis data ditutup"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Menghentikan server"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Pembaruan selesai, silakan tekan OK dan muat ulang halaman"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Pembaruan gagal:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "Kesalahan HTTP"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Kesalahan koneksi"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Batas waktu saat membuat koneksi"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Kesalahan umum"

#: cps/admin.py:1551
msgid "Update file could not be saved in temp dir"
msgstr "Berkas pembaruan tidak dapat disimpan di direktori temp"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "Berkas tidak dapat diganti selama pembaruan"

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "Gagal mengekstrak setidaknya Satu Pengguna LDAP"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Gagal Membuat Sedikitnya Satu Pengguna LDAP"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Kesalahan: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Error: Tidak ada pengguna yang dikembalikan sebagai respons dari server LDAP"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Setidaknya Satu Pengguna LDAP Tidak Ditemukan di Basis Data"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} Pengguna Berhasil Diimpor"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "Lokasi Basis Data tidak Valid, Harap Masukkan Jalur yang Benar"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "Basis Data tidak dapat ditulisi"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokasi keyfile tidak Valid, Harap Masukkan Jalur yang Benar "

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "Lokasi Sertifikat tidak Valid, Harap Masukkan Jalur yang Benar "

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr ""

#: cps/admin.py:1917
msgid "Database Settings updated"
msgstr "Pengaturan Basis Data diperbarui"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "Pengaturan Basis Data"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "Harap masukkan seluruh isian!"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "Email bukan dari domain yang valid"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Tambahkan pengguna baru"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "Pengguna '%(user)s' telah dibuat"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "Ditemukan akun yang ada untuk alamat email atau nama ini."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "Pengguna '%(nick)s' telah dihapus"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "Tidak dapat menghapus Pengguna Tamu"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Tidak ada pengguna admin tersisa, tidak dapat menghapus pengguna"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr "Alamat email tidak boleh kosong dan harus berupa email yang valid"

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "Pengguna '%(nick)s' diperbarui"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Telusuri"

#: cps/converter.py:31
msgid "not installed"
msgstr "belum dipasang"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Izin eksekusi hilang"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Tidak ada"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Berkas %(file)s telah diunggah"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Format sumber atau tujuan untuk konversi tidak ada"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Buku berhasil diantrekan untuk dikonversi ke %(book_format)s"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Terjadi kesalahan saat mengonversi buku ini: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Ups! Judul buku yang dipilih tidak tersedia. Berkas tidak ada atau tidak dapat diakses"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "Pengguna tidak berhak mengganti sampul"

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "IDは大文字小文字を区別しません。元のIDを上書きします"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "'%(langname)s' bukan bahasa yang valid"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "Metadata berhasil diperbarui"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "Kesalahan pengeditan buku: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Buku yang diunggah mungkin ada di perpustakaan, pertimbangkan untuk mengubahnya sebelum mengunggah yang baru: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "Ekstensi berkas '%(ext)s' tidak diizinkan untuk diunggah ke server ini"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "Ekstensi berkas '%(ext)s' tidak diizinkan untuk diunggah ke server ini"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Berkas yang akan diunggah harus memiliki ekstensi"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Berkas %(filename)s tidak dapat disimpan ke direktori temp"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Gagal Memindahkan Berkas Sampul %(file)s: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Format Buku Berhasil Dihapus"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Buku Berhasil Dihapus"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "Anda tidak memiliki izin untuk menghapus buku"

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "edit metadata"

#: cps/editbooks.py:1016
#, fuzzy, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "%(seriesindex)s dilewati karena bukan angka yang valid"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "Pengguna tidak memiliki izin untuk mengunggah format berkas tambahan"

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Gagal membuat jalur %(path)s (Izin ditolak)."

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Gagal menyimpan berkas %(file)s."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Format berkas %(ext)s ditambahkan ke %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Pengaturan Google Drive belum selesai, coba nonaktifkan dan aktifkan kembali Google Drive"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Domain panggilan balik tidak diverifikasi, ikuti langkah-langkah untuk memverifikasi domain di konsol pengembang google"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "%(format)s format tidak ditemukan untuk id buku: %(book)d"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "%(format)s tidak ditemukan di Google Drive: %(fn)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s tidak ditemukan: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
msgid "Send to eReader"
msgstr "Kirim ke E-Reader"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "Email ini telah dikirim melalui Calibre-Web."

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Email tes Calibre-Web"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "Email tes"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Memulai dengan Calibre-Web"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "Email pendaftaran untuk pengguna: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "Ubah %(orig)s menjadi %(format)s dan kirim ke E-Reader"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "Kirim %(format)s ke E-Reader"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "%%(buku)s telah dikirim ke E-Reader"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Berkas yang diminta tidak dapat dibaca. Mungkin izinnya salah?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "Status baca tidak bisa disetel: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Menghapus folder buku untuk buku %(id)s gagal, jalur memiliki subfolder: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Gagal menghapus buku %(id)s: %(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Menghapus buku %(id)s hanya dari basis data, jalur buku di basis data tidak valid: %(path)s"

#: cps/helper.py:439
#, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Ganti nama pengarang dari: '%(src)s' menjadi '%(dest)s' gagal dengan kesalahan: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Berkas %(file)s tidak ditemukan di Google Drive"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Ganti nama judul dari: '%(src)s' menjadi '%(dest)s' gagal dengan kesalahan: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Jalur buku %(path)s tidak ditemukan di Google Drive"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr ""

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Nama pengguna ini sudah digunakan"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "Format alamat email tidak valid"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr ""

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "Modul 'advocate' Python tidak diinstal tetapi diperlukan untuk unggahan sampul"

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Kesalahan Mengunduh Sampul"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Kesalahan Format Sampul"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "Anda tidak diizinkan mengakses localhost atau jaringan lokal untuk unggahan sampul"

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Gagal membuat jalur untuk sampul"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Berkas sampul bukan berkas gambar yang valid, atau tidak dapat disimpan"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Hanya berkas jpg/jpeg/png/webp/bmp yang didukung sebagai berkas sampul"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "Konten berkas sampul tidak valid"

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Hanya berkas jpg/jpeg yang didukung sebagai berkas sampul"

#: cps/helper.py:989 cps/helper.py:1149
#, fuzzy
msgid "Cover"
msgstr "Sampul"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "Berkas biner unrar tidak ditemukan"

#: cps/helper.py:1017
#, fuzzy
msgid "Error executing UnRar"
msgstr "Kesalahan saat menjalankan UnRar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "Basis Data tidak dapat ditulisi"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Izin eksekusi hilang"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "Kesalahan saat menjalankan UnRar"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr "Antrian semua buku untuk cadangan metadata"

#: cps/kobo_auth.py:92
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Mohon akses calibre-web dari non localhost untuk mendapatkan api_endpoint yang valid untuk perangkat kobo"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Pengaturan Kobo"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Daftar dengan %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "Anda sekarang login sebagai: %(nickname)s"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Tautan ke %(oauth)s Berhasil"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Login gagal, Tidak Ada Pengguna yang Tertaut Dengan Akun OAuth"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Membatalkan tautan ke %(oauth)s Berhasil"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Membatalkan tautan ke %(oauth)s Gagal"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Tidak Tertaut ke %(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Gagal masuk dengan GitHub."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Gagal mengambil info pengguna dari GitHub."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Gagal masuk dengan Google."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Gagal mengambil info pengguna dari Google."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "Kesalahan GitHub Oauth, silakan coba lagi nanti."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "Kesalahan GitHub OAuth: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Kesalahan Google Oauth, harap coba lagi nanti."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Kesalahan Google OAuth: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{}★"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Masuk"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Token tidak ditemukan"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Token telah kedaluwarsa"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Berhasil! Silakan kembali ke perangkat Anda"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Buku"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Tampilkan buku terbaru"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Buku Populer"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Tampilkan Buku Populer"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Buku yang Diunduh"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Tampilkan Buku yang Diunduh"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Buku Berperingkat Teratas"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Tampilkan Buku Berperingkat Teratas"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Buku Telah Dibaca"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "Tampilkan sudah dibaca dan belum dibaca"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Buku yang Belum Dibaca"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Tampilkan belum dibaca"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Temukan"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Tampilkan Buku Acak"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Kategori"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "Tampilkan pilihan kategori"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Seri"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "Tampilkan pilihan seri"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Penulis"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "Tampilkan pilihan penulis"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Penerbit"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "Tampilkan pilihan penerbit"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Bahasa"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "Tampilkan pilihan bahasa"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Peringkat"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "Tampilkan pilihan peringkat"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Format berkas"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "Tampilkan pilihan format berkas"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Buku yang Diarsipkan"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "Tampilkan buku yang diarsipkan"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Daftar Buku"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Tampilkan Daftar Buku"

#: cps/search.py:201
msgid "Published after "
msgstr "Terbit setelah "

#: cps/search.py:208
msgid "Published before "
msgstr "Terbit sebelum "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Peringkat ≤ %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Peringkat ≥ %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "Status Baca = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "Terjadi kesalahan saat mencari kolom khusus, harap mulai ulang Calibre-Web"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Penelusuran Lanjutan"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Rak yang ditentukan tidak valid"

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Maaf Anda tidak diperbolehkan menambahkan buku ke rak: %(shelfname)s"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Buku sudah menjadi bagian dari rak: %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Buku telah ditambahkan ke rak: %(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "Anda tidak diperbolehkan menambahkan buku ke rak: %(name)s"

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "Buku sudah menjadi bagian dari rak: %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "Buku telah ditambahkan ke rak: %(sname)s"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Tidak dapat menambahkan buku ke rak: %(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Buku telah dihapus dari rak: %(sname)s"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "Maaf Anda tidak diizinkan untuk menghapus buku dari rak ini: %(sname)s"

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Buat Rak"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Maaf, Anda tidak diizinkan mengedit rak ini"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Edit Rak"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "Kesalahan menghapus Rak"

#: cps/shelf.py:239
msgid "Shelf successfully deleted"
msgstr "Rak berhasil dihapus"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Ubah urutan Rak: '%(name)s'"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "Maaf, Anda tidak diizinkan membuat rak publik"

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Rak %(title)s dibuat"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Rak %(title)s diubah"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "Ada kesalahan"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "Rak publik dengan nama '%(title)s' sudah ada."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "Rak pribadi dengan nama '%(title)s' sudah ada."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Rak: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Terjadi kesalahan saat membuka rak. Rak tidak ada atau tidak dapat diakses"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Tugas"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Menunggu"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Gagal"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Dimulai"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Selesai"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "Berakhir"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "Dibatalkan"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Status Tidak Diketahui"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Data tak terduga saat membaca informasi pembaruan"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Tidak ada pembaruan yang tersedia. Anda telah memasang versi terbaru"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "Pembaruan tersedia. Klik tombol di bawah untuk memperbarui ke versi terbaru."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "Tidak dapat mengambil informasi pembaruan"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Klik tombol di bawah untuk memperbarui ke versi stabil terbaru."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "Pembaruan tersedia. Klik tombol di bawah untuk memperbarui ke versi: %(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Tidak ada informasi rilis yang tersedia"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Temukan (Buku Acak)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Buku Populer (Paling Banyak Diunduh)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Buku telah diunduh oleh %(user)s"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Penulis: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Penerbit: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Seri: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "Peringkat: Tidak ada"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Peringkat: %(rating)s★"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Format berkas: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Kategori: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Bahasa: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "Unduhan"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Daftar peringkat"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Daftar format berkas"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "Harap atur pengaturan email SMTP terlebih dahulu..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Buku telah diantrikan untuk dikirim ke %(eReadermail)s"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Oops! Terjadi kesalahan saat mengirim buku: %(res)s"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Harap perbarui profil Anda dengan alamat e-mail Kirim ke Kindle yang valid."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr ""

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Daftar"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "Server email belum diatur, silakan hubungi administrator!"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "Server email belum diatur, silakan hubungi administrator!"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "Alamat email Anda tidak diizinkan untuk mendaftar"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "E-mail konfirmasi telah dikirimkan ke alamat email Anda."

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "Tidak dapat mengaktifkan autentikasi LDAP."

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr ""

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "Anda sekarang login sebagai: %(nickname)s"

#: cps/web.py:1415
#, fuzzy, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "Login Pengganti sebagai: '%(nickname)s', Server LDAP tidak dapat dijangkau, atau pengguna tidak diketahui."

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "Tidak dapat login: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "Pengguna atau Kata Sandi salah"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "Kata Sandi baru telah dikirimkan ke alamat email Anda"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "Terjadi kesalahan yang tidak diketahui. Coba lagi nanti."

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "Harap masukkan pengguna valid untuk mengatur ulang kata sandi"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "Anda sekarang login sebagai: %(nickname)s"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "Profil %(name)s"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "Profil diperbarui"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "Ditemukan akun yang ada untuk alamat email ini"

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Tidak ditemukan berkas gmail.json yang valid dengan informasi OAuth"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, python-format
msgid "%(book)s send to E-Reader"
msgstr "%%(buku)s telah dikirim ke E-Reader"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Calibre ebook-convert %(tool)s tidak ditemukan"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s format tidak ditemukan dalam disk"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "Konverter ebook gagal dengan kesalahan yang tidak diketahui."

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kebupify-converter gagal: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "Berkas yang telah dikonversi tidak ditemukan atau terdapat duplikat dalam folder %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre gagal dengan kesalahan: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "Ebook-converter gagal: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "Konversi"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Menghubungkan kembali basis data Calibre"

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "Email"

#: cps/tasks/metadata_backup.py:34
#, fuzzy
msgid "Backing up Metadata"
msgstr "Mencadangkan Metadata"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "%(count)s thumbnail sampul dibuat"

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "Thumbnail Sampul"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "{0} thumbnail seri dihasilkan"

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "Menghapus cache thumbnail sampul"

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Unggah"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Pengguna"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Nama Pengguna"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "Alamat Email"

#: cps/templates/admin.html:15
msgid "Send to eReader Email"
msgstr "Alamat E-mail untuk Kirim ke E-Reader"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Admin"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Kata Sandi"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "Unduh"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Lihat Buku"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Edit"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Hapus"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Rak Publik"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "Impor Pengguna LDAP"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "Pengaturan Server Email"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "Hostname SMTP"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "Port SMTP"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "Enkripsi"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "Login SMTP"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Dari Email"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "Layanan Email"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via Oauth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Pengaturan"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Jalur Database Calibre"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Log Level"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Port"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Port Eksternal"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Buku per halaman"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Unggah"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Penjelajahan Anonim"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Registrasi Publik"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Login Jarak Jauh dengan Magic Link"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Login Reverse Proxy"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Nama Header Reverse Proxy"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Edit Pengaturan Basis Data Caliber"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Edit Pengaturan Dasar"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Edit Pengaturan Antarmuka"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "Tugas Terjadwal"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr "Waktu saat tugas mulai dijalankan"

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr "Durasi tugas maksimum"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr "Buat thumbnail sampul buku"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "Buat thumbnail sampul seri"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Sambungkan kembali ke Perpustakaan Caliber"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr ""

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr "Segarkan Cache Thumbnail Sampul"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Administrasi"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Unduh Paket Debug"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Tampilkan Log"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Mulai Ulang"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Matikan"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "Informasi Versi"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Versi"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Detail"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "Versi saat ini"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Periksa Pembaruan"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Lakukan Pembaruan"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Apa Anda yakin untuk memulai ulang?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "OK"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Batal"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Apa Anda yakin untuk mematikan layanan?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Memperbarui, jangan memuat ulang halaman ini"

#: cps/templates/author.html:15
msgid "via"
msgstr "melalui"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "Dalam Pustaka"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Urutkan menurut tanggal buku, terbaru dulu"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Urutkan menurut tanggal buku, terlama dulu"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Urutkan judul dalam urutan abjad"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Urutkan judul dalam urutan abjad terbalik"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Urutkan menurut tanggal penerbitan, yang terbaru dulu"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Urutkan menurut tanggal penerbitan, yang terlama dulu"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "kurangi"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Lainnya oleh"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, python-format
msgid "Book %(index)s of %(range)s"
msgstr "Buku"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Bahasa"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Penerbit"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "Diterbitkan"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "Deskripsi:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "Sebelumnya"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "Selanjutnya"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "Tidak ada hasil yang ditemukan"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "Beranda"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "Cari di Pustaka"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "Keluar"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Hapus"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Hapus format:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "KOnversi format buku:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Konversi dari:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "pilih opsi"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Konversi ke:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Konversi buku"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "Mengunggah..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Tutup"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "Terjadi Kesalahan"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "Unggahan selesai, harap tunggu, data sedang diproses..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Format Unggahan"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Judul Buku"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Penulis"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Tag"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "ID Seri"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Tanggal Terbit"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Rating"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Deskripsi"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "Pengidentifikasi"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "Tipe Pengidentifikasi"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "Nilai Pengidentifikasi"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Hapus"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "Tambah Pengidentifikasi"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Ambil Sampul dari URL (JPEG - Gambar akan diunduh dan disimpan dalam basis data)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Unggah Sampul dari disk lokal"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Ya"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "Tidak"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Tampilkan Buku setelah Disimpan"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Ambil Metadata"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Simpan"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Kata Kunci"

#: cps/templates/book_edit.html:240
msgid "Search keyword"
msgstr "Cari Kata kunci"

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Klik sampul untuk memuat metadata ke formulir"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Memuat..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Sumber"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Kesalahan pencarian!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Tidak ada hasil yang ditemukan! Silakan coba kata kunci lain."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "Isian Ini Diperlukan"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Gabungkan buku terpiih"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Hapus Pilihan"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "Tukar penulis dan judul"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "Perbarui Pengurutan Judul secara otomatis"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "Perbarui Pengurutan Penulis secara otomatis"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Masukkan Judul"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Judul"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Masukkan Urutan Judul"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Urutan Judul"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Masukkan Urutan Penulis"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Urutan Penulis"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Masukkan Penulis"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Masukkan Kategori"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Masukkan Seri"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Indeks Pencarian"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Masukkan Bahasa"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Tanggal Terbit"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Masukkan Penerbit"

#: cps/templates/book_table.html:73
msgid "Enter comments"
msgstr "Masukkan komentar"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "Komentar"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "Status Arsip"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Status Baca"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
msgid "Enter "
msgstr "入力: "

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Apakah Anda yakin?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "Buku dengan Judul akan digabungkan dari:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "Ke dalam Buku dengan Judul:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Gabungkan"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Lokasi Database Calibre"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Gunakan Google Drive?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Autentikasi Google Drive"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Silakan tekan simpan untuk melanjutkan penyiapan"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "ID Saluran Metadata Watch"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Tarik Kembali"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "Lokasi basis data baru tidak valid, harap masukkan jalur yang valid"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Pengaturan Server"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Port Server"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "Lokasi berkas sertifikat SSL (biarkan kosong untuk Server non-SSL)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "Lokasi SSL Keyfile (biarkan kosong untuk Server non-SSL)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Saluran Pembaruan"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Stabil"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Nightly"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "Host Tepercaya (Pisahkan dengan Koma)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Pengaturan Logfile"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Lokasi dan nama logfile (caliber-web.log jika tanpa entri)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Aktifkan Log Akses"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Lokasi dan nama access logfile (access.log jika tanpa entri)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Pengaturan Fitur"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "Konversikan karakter non-Inggris dalam judul dan penulis saat menyimpan ke disk"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Izinkan Unggahan"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(Harap pastikan pengguna juga memiliki hak mengunggah)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Format Berkas Unggahan yang Diizinkan"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Izinkan Penjelajahan Anonim"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Izinkan Registrasi Publik"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "Gunakan Email sebagai Nama Pengguna"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Izinkan Login Jarak Jauh dengan Magic Link"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Aktifkan sinkronisasi Kobo"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Proxy permintaan tidak dikenal ke Kobo Store"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Port Eksternal Server (untuk panggilan API melalui port forward)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Gunakan Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Kunci API Goodreads"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Izinkan Reverse Proxy Authentication"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Tipe Login"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Gunakan Otentikasi Standar"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Gunakan Otentikasi LDAP"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Gunakan Oauth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "Nama Host Server atau Alamat IP LDAP"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "Port Server LDAP"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "Enkripsi LDAP"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "Jalur CACertificate LDAP (Hanya diperlukan untuk Autentikasi Sertifikat Klien)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "Jalur Sertifikat LDAP (Hanya diperlukan untuk Otentikasi Sertifikat Klien)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "Jalur Keyfile LDAP (Hanya diperlukan untuk Otentikasi Sertifikat Klien)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "Otentikasi LDAP"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anonim"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Tidak diautentikasi"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Sederhana"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "Nama Pengguna Administrator LDAP"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "Kata Sandi Administrator LDAP"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "Distinguished Name (DN) LDAP"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "Filter Objek Pengguna LDAP"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "Server LDAP adalah OpenLDAP?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "Pengaturan Berikut Diperlukan Untuk Impor Pengguna"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "Filter Objek Grup LDAP"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "Nama Grup LDAP"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "Bidang Group Members LDAP"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "Deteksi Filter Pengguna Anggota LDAP"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "Deteksi otomatis"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "Filter Kustom"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "Filter Pengguna Anggota LDAP"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "Dapatkan %(provider)s Kredensial OAuth"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "ID Klien %(provider)s OAuth"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "Client Secret %(provider)s OAuth"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "Binari Eksternal"

#: cps/templates/config_edit.html:325
#, fuzzy
msgid "Path to Calibre Binaries"
msgstr "Jalur ke Konverter E-Book Calibre"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Pengaturan Konverter E-Book Caliber"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Jalur ke Konverter E-Book Kepubify"

#: cps/templates/config_edit.html:344
#, fuzzy
msgid "Location of Unrar binary"
msgstr "Lokasi binari UnRar"

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "Pengaturan OAuth"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr ""

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr ""

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr ""

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr ""

#: cps/templates/config_edit.html:393
#, fuzzy
msgid "User Password policy"
msgstr "Atur ulang kata sandi pengguna"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr ""

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr ""

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr ""

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr ""

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr ""

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "Pengaturan Tampilan"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "Jumlah Buku Acak untuk Ditampilkan"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "Jumlah Penulis untuk Ditampilkan Sebelum Disembunyikan (0=Nonaktifkan Penyembunyian)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "Tema"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "Tema Standar"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "Tema Gelap caliBlur!"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "Ekspresi Reguler untuk Mengabaikan Kolom"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "Tautkan Status Baca/Belum Dibaca ke Kolom Kaliber"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Lihat Batasan berdasarkan kolom Caliber"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "Ekspresi Reguler untuk Penyortiran Judul"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "Pengaturan Default untuk Pengguna Baru"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "Pengguna Admin"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "Izinkan Unduhan"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "Izinkan Penampil eBook"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "Izinkan Unggahan"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "Izinkan Edit"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "Izinkan Menghapus Buku"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "Izinkan Mengganti Kata Sandi"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "Izinkan Mengedit Rak Publik"

#: cps/templates/config_view_edit.html:123
msgid "Default Language"
msgstr "Bahasa Bawaan"

#: cps/templates/config_view_edit.html:131
msgid "Default Visible Language of Books"
msgstr "Bahasa Buku Bawaan yang Terlihat"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "Visibilitas Default untuk Pengguna Baru"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "Tampilkan Buku Acak dalam Tampilan Detail"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "Tambahkan Tag yang Diizinkan/Ditolak"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "Tambahkan nilai kolom khusus yang Diizinkan/Ditolak"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "Baca di Peramban"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "Dengarkan di Browser"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "Tandai sebagai Belum dibaca"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "Tandai sebagai dibaca"

#: cps/templates/detail.html:262
#, fuzzy
msgid "Mark Book as Read or Unread"
msgstr "Tandai sebagai Belum dibaca"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "Baca"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "Pulihkan dari arsip"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "Tambahkan ke Arsip"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "Diarsipkan"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "Tambah ke rak"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(Publik)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "Edit Metadata"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "Pilih Jenis Server"

#: cps/templates/email_edit.html:15
#, fuzzy
msgid "Standard Email Account"
msgstr "Gunakan Akun Email Standar"

#: cps/templates/email_edit.html:16
#, fuzzy
msgid "Gmail Account"
msgstr "Pilih Jenis Server"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Cabut Akses G-Mail"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "Kata Sandi SMTP"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "Batas Ukuran Lampiran"

#: cps/templates/email_edit.html:66
#, fuzzy
msgid "Save and Send Test Email"
msgstr "Simpan dan Kirim Email Percobaan"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "Kembali"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "Domain yang Diizinkan (Daftar Putih)"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "Tambahkan Domain"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "Tambah"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "Masukkan Nama Domain"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "Domain yang Ditolak (Daftar Hitam)"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "Buka berkas .kobo/Kobo/Kobo eReader.conf di editor teks dan tambahkan (atau edit):"

#: cps/templates/generate_kobo_auth_url.html:11
msgid "Kobo Token:"
msgstr "Token Kobo:"

#: cps/templates/grid.html:21
msgid "List"
msgstr "Daftar"

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "Instans Calibre-Web belum diatur, harap hubungi administrator Anda"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Buat Isu"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "Pengaturan Basis Data"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "Kembali ke Beranda"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "Keluar Pengguna"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr "Urutkan naik menurut jumlah unduhan"

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr "Urutkan turun menurut jumlah unduhan"

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "Urutkan penulis dalam urutan abjad"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "Urutkan penulis dalam urutan abjad terbalik"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "Urutkan naik menurut indeks seri"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "Urutkan menurun menurut indeks seri"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "Mulai"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "Buku Abjad"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "Buku diurutkan menurut abjad"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "Publikasi populer dari katalog ini berdasarkan Unduhan."

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "Publikasi populer dari katalog ini berdasarkan Rating."

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "Buku yang baru ditambahkan"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "Buku-buku terbaru"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "Buku Acak"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "Buku yang diurutkan menurut Penulis"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "Buku yang diurutkan menurut Penerbit"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "Buku yang diurutkan menurut Kategori"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "Buku yang diurutkan menurut Seri"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "Buku yang diurutkan menurut Bahasa"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "Buku yang diurutkan menurut Peringkat"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "Buku yang diurutkan menurut format berkas"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "Rak"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "本棚に整理された本"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "Alihkan Navigasi"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "Sederhana"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "Akun"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "Pengaturan"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "Harap jangan segarkan halaman"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "Jelajahi"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "Tentang"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "Detail Buku"

#: cps/templates/list.html:22
msgid "Grid"
msgstr "Terjadi Kesalahan"

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "Diarsipkan"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "Ingat saya"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "Lupa Kata Sandi?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "Masuk dengan Magic Link"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Tampilkan Log Caliber-Web:"

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Log Calibre-Web: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "Output aliran, tidak dapat ditampilkan"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "Tampilkan Log Akses:"

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Unduh Calibre-Web Log"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "Unduh Log Akses"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "Pilih Tag yang Diizinkan/Ditolak"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "Pilih Nilai Kolom Kustom yang Diizinkan/Ditolak"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "Pilih Tag Pengguna yang Diizinkan/Ditolak"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "Pilih Nilai Kolom Pengguna yang Diizinkan/Ditolak"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "Masukkan Tag"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "Tambahkan Batasan Tampilan"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "Format buku ini akan dihapus secara permanen dari database"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "Buku ini akan dihapus secara permanen dari basis data"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "dan harddisk"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Catatan Penting Kobo: buku yang dihapus akan tetap ada di perangkat Kobo yang telah dipasangkan."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "Buku harus diarsipkan terlebih dahulu dan perangkat disinkronkan sebelum buku dapat dihapus dengan aman."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "Pilih Lokasi Berkas"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "tipe"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "nama"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "ukuran"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "Direktori Induk"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "Pilih"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "Ok"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Katalog eBook Calibre-Web"

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr "Pembaca EPUB"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "Pilih nama pengguna"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "Terang"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "Gelap"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr "Sepia"

#: cps/templates/read.html:90
msgid "Black"
msgstr "Hitam"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "Reflow teks saat sidebar terbuka."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr ""

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "Hapus"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "Menunggu"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "Vertical"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "Baca"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "Kolom Baca Tidak Valid"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr "Pembaca Komik"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "Pintasan Keyboard"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "Halaman Sebelumnya"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "Halaman Selanjutnya"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr ""

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "Skala ke Terbaik"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "Skala ke Lebar"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "Skala ke Tinggi"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "Skalakan ke Asli"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "Putar ke Kanan"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "Putar ke Kiri"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "Balikkan Gambar"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr ""

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "Halaman Admin"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "Skala"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "Terbaik"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "Lebar"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "Tinggi"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "Asli"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "Putar"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "Balik"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "Horizontal"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "Vertical"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "Arah Baca"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "Kiri ke Kanan"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "Kanan ke Kiri"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr ""

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr ""

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "Scrollbar"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "Tampilkan"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "Sembunyikan"

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr "Pembaca DJVU"

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr "Pembaca PDF"

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr "Pembaca txt"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "Daftar Akun Baru"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "Pilih nama pengguna"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "Alamat email Anda"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "Magic Link - Otorisasi Perangkat Baru"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "Di perangkat lain, masuk dan kunjungi:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "Setelah diverifikasi, Anda akan secara otomatis masuk ke perangkat ini."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "Tautan verifikasi ini akan kedaluwarsa dalam 10 menit."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr "Hasilkan Thumbnail Sampul Seri"

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "Istilah Penelusuran:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "Hasil untuk:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "Tanggal Diterbitkan Dari"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "Tanggal Diterbitkan Hingga"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "Kecualikan Tag"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "Kecualikan Seri"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "Kecualikan Rak"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "Kecualikan Bahasa"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "Ekstensi"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "Kecualikan Ekstensi"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "Peringkat Diatas"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "Peringkat Dibawah"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "Dari:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "Hingga:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "Hapus rak ini"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "Edit Properti Rak"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "Atur buku secara manual"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "Nonaktifkan Ubah urutan"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "Aktifkan Ubah urutan"

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "Urutkan menurut tanggal buku, terbaru dulu"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "Urutkan menurut tanggal buku, terlama dulu"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "Bagikan dengan Semua Orang"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "Sinkronkan rak ini dengan perangkat Kobo"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "Seret untuk Mengatur Ulang Urutan"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "Buku Tersembunyi"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "Statistik Pustaka"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "Buku dalam Pustaka Ini"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "Penulis dalam Pustaka Ini"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "Kategori dalam Pustaka Ini"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "Seri dalam Pustaka Ini"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "Statistik Sistem"

#: cps/templates/stats.html:33
msgid "Program"
msgstr "Pustaka Program"

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "Versi Terpasang"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "Pengguna"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "Tugas"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "Status"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "Kemajuan"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "Waktu Jalan"

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "Gabungkan"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr "Tindakan"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr "Tugas ini akan dibatalkan. Setiap kemajuan yang dibuat oleh tugas ini akan disimpan."

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr "Jika ini adalah tugas terjadwal, ini akan dijalankan ulang selama waktu terjadwal berikutnya."

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "Atur ulang kata sandi pengguna"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "Bahasa Buku"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "Pengaturan OAuth"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "Tautan"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "Batalkan tautan"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Token Kobo Sync"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "Buat/Lihat"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr "Paksa sinkronisasi kobo penuh"

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "Tambahkan Nilai Kolom Kustom yang Diizinkan/Ditolak"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "Sinkronkan hanya buku di rak yang dipilih dengan Kobo"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "Hapus Pengguna"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Hasilkan URL Autentikasi Kobo"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "Pilih..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "Edit Pengguna"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "Masukkan Nama Pengguna"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "Email tes"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "Enter eReader Email"
msgstr "Alamat E-mail untuk Kirim ke E-Reader"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "eReader Email"
msgstr "Email E-Reader"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "Lokal"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "Bahasa Buku Terlihat"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "Edit Tag yang Diizinkan"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "Tag yang Diizinkan"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "Edit Tag yang Ditolak"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "Tag yang Ditolak"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "Edit Nilai Kolom yang Diizinkan"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "Nilai Kolom yang Diizinkan"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "Edit Nilai Kolom yang Ditolak"

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr "Nilai Kolom yang Ditolak"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "Ubah Kata Sandi"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "Tampilan"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "Edit Rak Publik"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "Sinkronkan Rak yang dipilih dengan Kob"

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "Tampilkan pilihan baca/belum dibaca"

