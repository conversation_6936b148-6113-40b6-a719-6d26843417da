# German translations for Calibre-Web.
# Copyright (C) 2016 <PERSON><PERSON> Isaacs
# This file is distributed under the same license as the Calibre-Web
# project.
# FIRST AUTHOR OzzieIsaacs, 2016.
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/Calibre-Web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2024-11-16 20:41+0100\n"
"Last-Translator: <PERSON><PERSON>\n"
"Language: de\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "Statistiken"

#: cps/admin.py:151
msgid "Server restarted, please reload page."
msgstr "Server neu gestartet, Seite bitte neu laden."

#: cps/admin.py:153
msgid "Performing Server shutdown, please close window."
msgstr "Server wird heruntergefahren, Fenster bitte schließen."

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr "Datenbank wurde erneut verbunden"

#: cps/admin.py:164
msgid "Unknown command"
msgstr "Unbekannter Befehl"

#: cps/admin.py:175
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "Bücher wurden für Metadaten Backup eingereiht, für das Ergebnis bitte Aufgaben überprüfen"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "Unbekannt"

#: cps/admin.py:233
msgid "Admin page"
msgstr "Admin Seite"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "Basiskonfiguration"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "Benutzeroberflächenkonfiguration"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "Benutzerdefinierte Spalte Nr. %(column)d ist nicht in Calibre Datenbank vorhanden"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "Benutzer bearbeiten"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "Alle"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "Benutzer nicht gefunden"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{} Benutzer erfolgreich gelöscht"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "Zeige alle"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "Ungültige Anfrage"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "Name 'Guest' kann nicht geändert werden"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "Benutzer 'Guest' kann diese Rolle nicht haben"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "Kein Admin Benutzer verblieben, Admin Berechtigung kann nicht entfernt werden"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "Wert muss true oder false sein"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "Ungültige Rolle"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "Benutzer 'Guest' kann diese Ansicht nicht haben"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "Ungültige Ansicht"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "Sprache von 'Guest' wird automatisch bestimmt und kann nicht eingestellt werden"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "Keine gültige Sprache gewählt"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "Keine gültige Buchsprache gewählt"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "Parameter wurde nicht gefunden"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "Ungültige Gelesen Spalte"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "Ungültiger Spaltenname für Einschränkung"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Konfiguration von Calibre-Web wurde aktualisiert"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Möchten Sie den Kobo Token wirklich löschen?"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "Möchten Sie diese Domain wirklich löschen?"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "Möchten Sie diesen Benutzer wirklich löschen?"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "Möchten Sie dieses Bücherregal wirklich löschen?"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "Möchten Sie die Anzeigesprache der ausgewählten Benutzer wirklich ändern?"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "Möchten Sie die Büchersprachen für die ausgewählten Benutzer wirklich ändern?"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "Möchten Sie die ausgewählte Rolle für die ausgewählten Benutzer wirklich verändern?"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "Möchten Sie die ausgewählten Sichtbarkeitsbeschränkungen der ausgewählten Benutzer wirklich ändern?"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "Möchten Sie die Sichtbarkeiten für die ausgewählten Benutzer wirklich verändern?"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "Möchten Sie die Synchronisation von Bücherregalen für die ausgewählten Benutzer wirklich verändern?"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Ort der Calibre Datenbank editieren?"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-Web wird nach neuen Covern suchen und Cover Miniaturansichten aktualisieren, dies kann eine Weile dauern?"

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Möchten Sie die Synchronisationsdatenbank von Calibre-Web wirklich löschen, um eine komplette Synchronisation zu erzwingen?"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "Verbieten"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "Erlauben"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{} Synchronisationseinträge gelöscht"

#: cps/admin.py:987
msgid "Tag not found"
msgstr "Tag nicht gefunden"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "Ungültige Aktion"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json ist nicht für Web Anwendungen konfiguriert"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Logdatei Pfad ist ungültig, bitte einen gültigen Pfad angeben"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "Zugriffs-Logdatei Pfad ist ungültig, bitte einen gültigen Pfad angeben"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "Bitte einen LDAP Server, Port, DN und Benutzer Objekt Identifikator angeben"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "Bitte einen LDAP Service Account und Passwort eingeben"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "Bitte einen LDAP Service Account eingeben"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAP Gruppen Objekt Filter benötigt genau eine \"%s\" Format Kennung"

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAP Gruppen Objekt Filter hat ungleiche Anzahl von Klammern"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP Benutzer Objekt Filter benötigt genau eine \"%s\" Format Kennung"

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAP Benutzer Objekt Filter hat ungleiche Anzahl von Klammern"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "Der LDAP Member User Filter benötigt genau eine \"%s\" Formatierungsmarkierung"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "LDAP Member User Filter hat eine ungleiche Anzahl von geöffneten und geschlossenen Klammern"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAP CA-Zertifikat, Zertifikat oder Key Datei ist kein gültiger Pfad"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "Neuen Benutzer hinzufügen"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "SMTP-Einstellungen ändern"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr "G-Mail Konto verifiziert."

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "Datenbankfehler: %(error)s."

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "Test E-Mail an %(email)s wurde zum Senden in die Warteschlange eingereiht, für das Ergebnis bitte Aufgaben überprüfen"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "Es trat ein Fehler beim Versenden der Test-E-Mail auf: %(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "Bitte zuerst E-Mail Adresse konfigurieren..."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "Einstellungen des E-Mail-Servers aktualisiert"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "Einstellungen für Geplante Aufgaben"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "Ungültigen Startzeitpunkt für Aufgaben spezifiziert"

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "Ungültige Laufzeit für Aufgaben spezifiziert"

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "Einstellungen für Geplante Aufgaben aktualisiert"

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "Es ist ein unbekannter Fehler aufgetreten. Bitte später erneut versuchen."

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "Einstellungsdatenbank ist nicht schreibbar"

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "Benutzer %(nick)s bearbeiten"

#: cps/admin.py:1457
#, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "Passwort für Benutzer %(user)s wurde zurückgesetzt"

#: cps/admin.py:1463
msgid "Oops! Please configure the SMTP mail settings."
msgstr "Bitte zuerst die SMTP-Einstellungen konfigurieren."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "Logdatei Anzeige"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "Frage Update an"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "Lade Update herunter"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "Entpacke Update"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "Ersetze Dateien"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "Schließe Datenbankverbindungen"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "Stoppe Server"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "Update abgeschlossen, bitte okay drücken und Seite neu laden"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "Update fehlgeschlagen:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTP Fehler"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "Verbindungsfehler"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "Timeout beim Verbindungsaufbau"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "Allgemeiner Fehler"

#: cps/admin.py:1551
msgid "Update file could not be saved in temp dir"
msgstr "Updatedatei konnte nicht in temporärem Ordner gespeichert werden"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "Dateien konnten während des Updates nicht ausgetauscht werden"

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "Mindestens ein LDAP Benutzer konnte nicht extrahiert werden"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "Mindestens ein LDAP Benutzer konnte nicht erzeugt werden"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "Fehler: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "Fehler: Keine Benutzerinformationen von LDAP Server empfangen"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "Mindestens ein LDAP Benutzer wurde nicht in der Datenbank gefudnen"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} Benutzer erfolgreich importiert"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr "Bücherpfad ungültig"

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "DB Pfad ist nicht gültig, bitte einen gültigen Pfad angeben"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "Datenbank ist nicht schreibbar"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "Schlüsseldatei ist ungültig, bitte einen gültigen Pfad angeben"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "Zertifikatsdatei ist ungültig, bitte einen gültigen Pfad angeben"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr "Passwortlänge muss zwischen 1 und 40 Zeichen liegen"

#: cps/admin.py:1917
msgid "Database Settings updated"
msgstr "Datenbankeinstellung aktualisiert"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "Datenbank-Konfiguration"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "Bitte alle Felder ausfüllen."

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "E-Mail bezieht sich nicht auf eine gültige Domain"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "Neuen Benutzer hinzufügen"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "Benutzer '%(user)s' angelegt"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "Es existiert bereits ein Account für diese E-Mail Adresse oder diesen Benutzernamen."

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "Benutzer '%(nick)s' gelöscht"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "Benutzer 'Guest' kann nicht gelöscht werden"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "Benutzer kann nicht gelöscht werden, es wäre kein Admin Benutzer übrig"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr "E-Mail kann nicht leer sein und muss gültig sein"

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "Benutzer '%(nick)s' aktualisiert"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "Suchen"

#: cps/converter.py:31
msgid "not installed"
msgstr "Nicht installiert"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "Ausführberechtigung fehlt"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "Keine"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "Datei %(file)s hochgeladen"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "Quell- oder Zielformat für Konvertierung fehlt"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "Buch wurde erfolgreich für die Konvertierung nach %(book_format)s eingereiht"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "Es trat ein Fehler beim Konvertieren des Buches auf: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "Öffnen des Buches fehlgeschlagen. Datei existiert nicht oder ist nicht zugänglich"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "Benutzer hat keine Berechtigung Cover hochzuladen"

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "IDs unterscheiden nicht zwischen Groß- und Kleinschreibung, alte ID wird überschrieben"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "'%(langname)s' ist keine gültige Sprache"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "Metadaten wurden erfolgreich aktualisiert"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "Fehler beim Editieren des Buches: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "Das hochgeladene Buch existiert evtl. schon in der Bibliothek: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
msgid "File type isn't allowed to be uploaded to this server"
msgstr "Dateityp kann nicht auf diesen Server hochgeladen werden"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "Dateiendung '%(ext)s' kann nicht auf diesen Server hochgeladen werden"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "Dateien müssen eine Erweiterung haben, um hochgeladen zu werden"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "Die Datei %(filename)s konnte nicht im temporären Ordner gespeichert werden"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "Fehler beim Verschieben der Cover Datei %(file)s: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "Buch Format erfolgreich gelöscht"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "Buch erfolgreich gelöscht"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "Keine Erlaubnis zum Löschen von Büchern"

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "Metadaten editieren"

#: cps/editbooks.py:1016
#, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "Serienindex %(seriesindex)s ist keine gültige Zahl, Eintrag wird ignoriert"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "Benutzer hat kein Recht zusätzliche Dateiformate hochzuladen"

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "Fehler beim Erzeugen des Pfads %(path)s (Zugriff verweigert)"

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "Fehler beim Speichern der Datei %(file)s."

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "Dateiformat %(ext)s zu %(book)s hinzugefügt"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Google Drive Setup is nicht komplett, bitte versuche Google Drive zu deaktivieren und aktiviere es anschließend erneut"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "Callback Domain ist nicht verifiziert, bitte Domain in der Google Developer Console verifizieren"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "%(format)s Format für Buch-ID %(book)d nicht gefunden"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "%(format)s von Buch %(fn)s nicht auf Google Drive gefunden"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s nicht gefunden: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
msgid "Send to eReader"
msgstr "An E-Reader senden"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
msgid "This Email has been sent via Calibre-Web."
msgstr "Diese E-Mail wurde durch Calibre-Web versendet."

#: cps/helper.py:123
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web Test-E-Mail"

#: cps/helper.py:124
msgid "Test Email"
msgstr "Test E-Mail"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Loslegen mit Calibre-Web"

#: cps/helper.py:146
#, python-format
msgid "Registration Email for user: %(name)s"
msgstr "Registrierungs-E-Mail für Benutzer: %(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "Konvertiere %(orig)s nach %(format)s und sende an E-Reader"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, python-format
msgid "Send %(format)s to eReader"
msgstr "Sende %(format)s an E-Reader"

#: cps/helper.py:230
#, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s an E-Reader gesendet"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "Die angeforderte Datei konnte nicht gelesen werden. Evtl. falsche Zugriffsrechte?"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "Gelesen-Status konnte nicht aktualisiert werden: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "Löschen des Ordners für Buch %(id)s ist fehlgeschlagen, der Pfad hat Unterordner: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "Löschen von Buch %(id)s fehlgeschlagen: %(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "Lösche Buch %(id)s nur aus Datenbank, Pfad zum Buch in Datenbank ist nicht gültig: %(path)s"

#: cps/helper.py:439
#, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Umbenennen des Autors '%(src)s' zu '%(dest)s' fehlgeschlagen: %(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Datei %(file)s wurde nicht auf Google Drive gefunden"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "Umbenennen des Titels '%(src)s' zu '%(dest)s' fehlgeschlagen: %(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Buchpfad %(path)s wurde nicht auf Google Drive gefunden"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr "Es existiert bereits ein Benutzerkonto für diese E-Mail Adresse"

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "Benutzername ist schon vorhanden"

#: cps/helper.py:679
msgid "Invalid Email address format"
msgstr "Ungültiges E-Mail Adressformat"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr "Passwort stimmt nicht mit den Passwortregeln überein"

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "Python Modul 'advocate' ist nicht installiert, wird aber für das Hochladen von Covern benötigt"

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "Fehler beim Herunterladen des Covers"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "Coverdatei fehlerhaft"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "Keine Berechtigung Cover von localhost oder dem lokalen Netzwerk hochzuladen"

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "Fehler beim Erzeugen des Ordners für die Coverdatei"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "Coverdatei ist keine gültige Bilddatei, kann nicht gespeichert werden"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "Nur jpg/jpeg/png/webp/bmp Dateien werden als Coverdatei unterstützt"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "Ungültiger Coverdatei-Inhalt"

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "Es werden nur jpg/jpeg Dateien als Cover unterstützt"

#: cps/helper.py:989 cps/helper.py:1149
msgid "Cover"
msgstr "Titelbild"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "UnRar Programm nicht gefunden"

#: cps/helper.py:1017
msgid "Error executing UnRar"
msgstr "Fehler beim Ausführen von UnRar"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr "Angegebener Ordner konnte nicht gefunden werden"

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr "Bitte keine Datei sondern einen Ordner angeben"

#: cps/helper.py:1042
msgid "Calibre binaries not viable"
msgstr "Calibre Programm ist nicht nutzbar"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr "Fehlende Calibre Binärdateien: %(missing)s"

#: cps/helper.py:1053
#, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "Ausführberechtigung fehlt: %(missing)s"

#: cps/helper.py:1058
msgid "Error executing Calibre"
msgstr "Fehler beim Ausführen von Calibre"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr "Alle Bücher für Metadaten Backup einreihen"

#: cps/kobo_auth.py:92
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "Bitte nicht von \"localhost\" auf Calibre-Web zugreifen, um einen gültigen api_endpoint für Kobo Geräte zu erhalten"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo Setup"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "Anmelden mit %(provider)s"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "Du bist nun eingeloggt als '%(nickname)s'"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "Verbindung mit %(oauth)s erfolgreich"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "Login fehlgeschlagen, es ist kein Benutzer mit diesem Account verbunden"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "Verbindung zu %(oauth)s erfolgreich getrennt"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "Verbindung mit %(oauth)s fehlgeschlagen"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "Nicht mit  %(oauth)s verbunden"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "Login mit GitHub fehlgeschlagen."

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "Laden der Benutzerinformationen von GitHub fehlgeschlagen."

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Login mit Google fehlgeschlagen."

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Laden der Benutzerinformationen von Google fehlgeschlagen."

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub OAuth Fehler, bitte später erneut versuchen."

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "Github OAuth Fehler {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google OAuth Fehler, bitte später erneut versuchen."

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google OAuth Fehler: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} Sterne"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "Login"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "Token wurde nicht gefunden"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Token ist abgelaufen"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "Erfolg! Bitte zum Gerät zurückkehren"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "Bücher"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "Zeige kürzlich hinzugefügte Bücher"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "Beliebte Bücher"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "Zeige beliebte Bücher"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "Heruntergeladene Bücher"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "Zeige heruntergeladene Bücher"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "Am besten bewertete Bücher"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "Zeige am besten bewertete Bücher"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "Gelesene Bücher"

#: cps/render_template.py:63
msgid "Show Read and Unread"
msgstr "Zeige gelesene/ungelesene Bücher"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "Ungelesene Bücher"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "Zeige ungelesene Bücher"

#: cps/render_template.py:68
msgid "Discover"
msgstr "Entdecken"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "Zeige zufällige Bücher"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "Kategorien"

#: cps/render_template.py:73 cps/templates/user_table.html:158
msgid "Show Category Section"
msgstr "Zeige Kategorien"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "Serien"

#: cps/render_template.py:76 cps/templates/user_table.html:157
msgid "Show Series Section"
msgstr "Zeige Serien"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "Autoren"

#: cps/render_template.py:79 cps/templates/user_table.html:160
msgid "Show Author Section"
msgstr "Zeige Autoren"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "Verleger"

#: cps/render_template.py:83 cps/templates/user_table.html:163
msgid "Show Publisher Section"
msgstr "Zeige Verleger"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "Sprachen"

#: cps/render_template.py:87 cps/templates/user_table.html:155
msgid "Show Language Section"
msgstr "Zeige Sprachen"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "Bewertungen"

#: cps/render_template.py:90 cps/templates/user_table.html:164
msgid "Show Ratings Section"
msgstr "Zeige Bewertungen"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "Dateiformate"

#: cps/render_template.py:93 cps/templates/user_table.html:165
msgid "Show File Formats Section"
msgstr "Zeige Dateiformate"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "Archivierte Bücher"

#: cps/render_template.py:97 cps/templates/user_table.html:166
msgid "Show Archived Books"
msgstr "Zeige archivierte Bücher"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "Bücherliste"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "Zeige Bücherliste"

#: cps/search.py:201
msgid "Published after "
msgstr "Herausgegeben nach dem "

#: cps/search.py:208
msgid "Published before "
msgstr "Herausgegeben vor dem "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "Bewertung <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "Bewertung >= %(rating)s"

#: cps/search.py:234
#, python-format
msgid "Read Status = '%(status)s'"
msgstr "Gelesenstatus = '%(status)s'"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "Fehler bei der Suche nach eigenen Spalten, bitte Calibre-Web neustarten"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "Erweiterte Suche"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "Ungültiges Bücherregal angegeben"

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "Du hast keine Berechtigung, ein Buch zu diesem Bücherregal hinzuzufügen"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "Buch ist bereits Teil des Bücherregals %(shelfname)s"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr "%(book_id)s ist eine ungültige Buch ID. Buch konnte nicht zu Bücherregal hinzugefügt werden"

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "Das Buch wurde zum Bücherregal %(sname)s hinzugefügt"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "Keine Erlaubnis Bücher zu diesem Bücherregal hinzuzufügen"

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "Bücher sind bereits Teil des Bücherregals %(name)s"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "Bücher wurden zum Bücherregal %(sname)s hinzugefügt"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "Bücher konnten nicht zum Bücherregal %(sname)s hinzugefügt werden"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "Das Buch wurde aus dem Bücherregal: %(sname)s entfernt"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "Sie haben keine Berechtigung um Bücher aus diesem Bücherregal zu löschen"

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "Bücherregal erzeugen"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "Sie dürfen dieses Bücherregal nicht editieren"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "Bücherregal editieren"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "Fehler beim Löschen des Bücherregals"

#: cps/shelf.py:239
msgid "Shelf successfully deleted"
msgstr "Bücherregal erfolgreich gelöscht"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "Reihenfolge in Bücherregal '%(name)s' verändern"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "Sie haben keine Berechtigung um ein öffentliches Bücherregal zu erzeugen"

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "Bücherregal %(title)s erzeugt"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "Bücherregal %(title)s verändert"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "Es trat ein Fehler auf"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "Es existiert bereit ein öffentliches Bücherregal mit dem Namen '%(title)s'."

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "Es existiert bereit ein privates Bücherregal mit dem Namen '%(title)s'."

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "Bücherregal: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "Fehler beim Öffnen des Bücherregals. Bücherregal exisitert nicht oder ist nicht zugänglich"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "Aufgaben"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "Wartend"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "Fehlgeschlagen"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "Gestartet"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "Fertiggestellt"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "Beendet"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "Abgebrochen"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "Unbekannter Status"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "Updateinformationen enthalten unbekannte Daten"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "Kein Update verfügbar. Es ist bereits die aktuellste Version installiert"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "Es sind Updates verfügbar. Klicke auf den Button unten, um auf die aktuellste Version zu aktualisieren."

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "Updateinformationen konnten nicht geladen werden"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "Klicke auf den Button unten, um auf die letzte stabile Version zu aktualisieren."

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "Ein neues Update ist verfügbar. Klicke auf den Button unten, um auf Version: %(version)s zu aktualisieren"

#: cps/updater.py:538
msgid "No release information available"
msgstr "Keine Releaseinformationen verfügbar"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "Entdecken (Zufällige Bücher)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "Beliebte Bücher (meiste Downloads)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "Von %(user)s heruntergeladene Bücher"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "Author: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "Verleger: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "Serie: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "Bewertung: Keine"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "Bewertung: %(rating)s Sterne"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "Dateiformat: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "Kategorie: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "Sprache: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "Downloads"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "Bewertungsliste"

#: cps/web.py:1100
msgid "File formats list"
msgstr "Liste der Dateiformate"

#: cps/web.py:1259
msgid "Please configure the SMTP mail settings first..."
msgstr "Bitte zuerst die SMTP-Einstellung konfigurieren..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "Buch erfolgreich zum Senden an %(eReadermail)s eingereiht"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "Beim Senden des Buches trat ein Fehler auf: %(res)s"

#: cps/web.py:1270
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "Bitte zuerst die E-Reader E-Mailadresse konfigurieren."

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr "Bitte eine Minute warten vor der Registrierung des nächsten Benutzers"

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "Registrieren"

#: cps/web.py:1290 cps/web.py:1393
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "Verbindugnsfehler zu Limiter Backend, bitte Administrator kontaktieren"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "Der E-Mail Server ist nicht konfiguriert, bitte den Administrator kontaktieren."

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "Diese E-Mail ist nicht für die Registrierung zugelassen."

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "Eine Bestätigungs-E-Mail wurde an deinen E-Mail Account versendet."

#: cps/web.py:1376 cps/web.py:1399
msgid "Cannot activate LDAP authentication"
msgstr "LDAP-Authentifizierung kann nicht aktiviert werden"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr "Bitte eine Minute vor dem nächsten Loginversuch warten"

#: cps/web.py:1408
#, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "Du bist nun eingeloggt als '%(nickname)s'"

#: cps/web.py:1415
#, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "Fallback Login als: '%(nickname)s', LDAP Server ist nicht erreichbar, oder der Nutzer ist unbekannt"

#: cps/web.py:1420
#, python-format
msgid "Could not login: %(message)s"
msgstr "Login nicht erfolgreich: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
msgid "Wrong Username or Password"
msgstr "Falscher Benutzername oder Passwort"

#: cps/web.py:1431
msgid "New Password was sent to your email address"
msgstr "Das neue Passwort wurde an die E-Mail Adresse verschickt"

#: cps/web.py:1435
msgid "An unknown error occurred. Please try again later."
msgstr "Es ist ein unbekannter Fehler aufgetreten. Bitte später erneut versuchen."

#: cps/web.py:1437
msgid "Please enter valid username to reset password"
msgstr "Bitte einen gültigen Benutzernamen zum Zurücksetzen des Passworts angeben"

#: cps/web.py:1445
#, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "Du bist nun eingeloggt als: '%(nickname)s'"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "%(name)s's Profil"

#: cps/web.py:1526
msgid "Success! Profile Updated"
msgstr "Profil aktualisiert"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "Es existiert bereits ein Benutzer für diese E-Mailadresse."

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "Keine gültige gmail.json Datei mit OAuth informationen gefunden"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr "Temp Ordner Inhalt löschen"

#: cps/tasks/convert.py:112
#, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s an E-Reader gesendet"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Calibre E-Book Konverter %(tool)s nicht gefunden"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s Format nicht gefunden"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "E-Book Converter mit unbekanntem Fehler fehlgeschlagen"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify Konverter Aufruf fehlgeschlagen: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "Konvertierte Datei nicht gefunden, oder mehr als eine Datei im Pfad %(folder)s"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre fehlgeschlagen mit Fehler: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "Fehler des E-Book-Converters: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "Konvertiere"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Calibre Datenbank wird neu verbunden"

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "E-Mail"

#: cps/tasks/metadata_backup.py:34
msgid "Backing up Metadata"
msgstr "Metadaten Backup läuft"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "%(count)s Cover Miniaturansichten erzeugt"

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "Cover Miniaturansichten"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "{0} Serien Miniaturansichten erzeugt"

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "Cover Miniaturansichten Cache wird gelöscht"

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "Upload"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "Benutzerliste"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "Benutzername"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "E-Mail"

#: cps/templates/admin.html:15
msgid "Send to eReader Email"
msgstr "An E-Reader E-Mail Adresse senden"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "Admin"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "Passwort"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "Download"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "Bücher ansehen"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "Editieren"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "Löschen"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "Öffentliches Bücherregal"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "LDAP Benutzer importieren"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "Einstellungen des SMTP-Servers"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTP-Hostname"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTP Port"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "Verschlüsselung"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTP-Login"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Absenderadresse"

#: cps/templates/admin.html:90
msgid "Email Service"
msgstr "E-Mail Service"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "Gmail via OAuth2"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "Konfiguration"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Ordner der Calibre Datenbank"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "Loglevel"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "Port"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "Externer Port"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "Bücher pro Seite"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "Hochladen"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "Anonymes Durchsuchen"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "Öffentliche Registrierung"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "Remotelogin ('Magischer Link')"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "Reverse Proxy Login"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "Reverse Proxy Header Name"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Calibre Datenbank Konfiguration editieren"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "Basiskonfiguration"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "Benutzeroberflächenkonfiguration"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "Geplante Aufgabe"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr "Startzeit"

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr "Maximale Aufgabendauer"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr "Buchcover Miniaturansichten erzeugen"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "Seriencover Miniaturansichten erzeugen"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Mit Calibre Bibliothek neu verbinden"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr "Metadaten Backup Datei erzeugen"

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr "Cover Miniaturansichten aktualisieren"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "Administration"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "Debug Daten herunterladen"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "Logdateien ansehen"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "Neustart"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "Calibre-Web beenden"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "Versionsinformation"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "Version"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "Details"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "Aktuelle Version"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "Nach Update suchen"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "Update durchführen"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "Calibre-Web wirklich neustarten?"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "OK"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "Abbruch"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "Calibre-Web wirklich anhalten?"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "Updatevorgang, Seite bitte nicht neu laden"

#: cps/templates/author.html:15
msgid "via"
msgstr "via"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "In Bibliothek"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "Sortiere nach Buchdatum, neuestes zuerst"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "Sortiere nach Buchdatum, ältestes zuerst"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "Sortiere Titel in alphabetischer Reihenfolge"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "Sortiere Titel in umgekehrt alphabetischer Reihenfolge"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "Sortiere nach Herausgabedatum, neueste zuerst"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "Sortiere nach Herausgabedatum, älteste zuerst"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "Reduzieren"

#: cps/templates/author.html:97
msgid "More by"
msgstr "Mehr von"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, python-format
msgid "Book %(index)s of %(range)s"
msgstr "Buch %(index)s von %(range)s"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "Sprache"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "Herausgeber"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "Herausgabedatum"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "Beschreibung:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "Vorheriger Eintrag"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "Nächste"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "Keine Ergebnisse"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "Home"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "Bibliothek durchsuchen"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "Logout"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "Buch löschen"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "Lösche Formate:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "Buchformat konvertieren:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "Konvertiere von:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "Wähle eine Option"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "Konvertiere nach:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "Buch konvertieren"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "Lade hoch..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "Schließen"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "Fehler"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "Hochladen beendet, verarbeite Daten, bitte warten..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "Format hochladen"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "Buchtitel"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "Autor"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "Tags"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "Serien ID"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "Herausgabedatum"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "Bewertung"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "Beschreibung"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "IDs"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "ID Typ"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "ID Wert"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "Entfernen"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "ID hinzufügen"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "Cover-URL (jpg, Cover wird heruntergeladen und in der Datenbank gespeichert, Feld erscheint anschließend wieder leer)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "Coverdatei von lokalem Laufwerk hochladen"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "Ja"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "Nein"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "Buch nach Bearbeitung ansehen"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "Metadaten laden"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "Speichern"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "Suchbegriff"

#: cps/templates/book_edit.html:240
msgid "Search keyword"
msgstr "Suche Schlüsselbegriff"

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "Klicke auf das Bild, um die Metadaten zu übertragen"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "Lade..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "Quelle"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "Fehler bei der Suche!"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "Keine Ergebnisse gefunden! Bitte ein anderes Schlüsselwort benutzen."

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "Dieses Feld ist erforderlich"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "Buchauswahl zusammenführen"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "Auswahl aufheben"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "Autor und Titel tauschen"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "Titelsortierung automatisch aktualisieren"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "Autorensortierung automatisch aktualisieren"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "Titel eingeben"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "Titel"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "Titelsortierung eingeben"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "Titelsortierung"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "Autorensortierung eingeben"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "Autorensortierung"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "Autoren eingeben"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "Kategorien eingeben"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "Serie eingeben"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "Serienindex"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "Sprache eingeben"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "Herausgabedatum"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "Herausgeber eingeben"

#: cps/templates/book_table.html:73
msgid "Enter comments"
msgstr "Kommentare eingeben"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "Kommentare"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "Archivstatus"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "Lesestatus"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
msgid "Enter "
msgstr "Eingeben "

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "Sicher?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "Bücher werden zusammengeführt. Von Titel:"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "In Buch mit Titel:"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "Zusammenführen"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Speicherort der Calibre-Datenbank"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr "Bücherdateien von Bibliotheksdatei separieren"

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Google Drive benutzen?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Google Drive authentifizieren"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Google Drive Calibre-Ordner"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "Metadaten Überwachungs-ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "Widerrufen"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "Neuer Datenbank Pfad ist nicht gültig, bitte einen gültigen Pfad angeben"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "Server-Konfiguration"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "Server Port"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "Speicherort der SSL-Certdatei (leerlassen, falls kein SSL-Server)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "Speicherort der SSL-Keydatei (leerlassen, falls kein SSL-Server)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "Update Kanal"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "Stabil"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "Nightly"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "Trusted Hosts (kommasepariert)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "Konfiguration der Logdatei"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "Position und Name der Logdatei (calibre-web.log bei keiner Eingabe)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "Zugriffs-Logdatei aktivieren"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "Position und Name der Zugriffs-Logdatei (access.log bei keiner Eingabe)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "Feature-Konfiguration"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "Nicht-englische Zeichen in Titel und Autor beim Speichern auf Festplatte ersetzen"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr "Metadaten bei Download/Konvertierung/E-Mail zu Ebook Datei hinzufügen (benötigt Calibre/Kepubify)"

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "Hochladen aktivieren"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(Bitte stellen Sie sicher, dass Sie über die Upload Berechtigung verfügen)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "Erlaubte Dateiformate zum Hochladen"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "Anonymes Durchsuchen aktivieren"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "Öffentliche Registrierung aktivieren"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "Benutze E-Mail als Benutzername"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "Remotelogin ('Magischer Link') aktivieren"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Synchronisation mit Kobo aktivieren"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Unbekannte Anfragen an kobo.com weiterleiten"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "Externer Server Port (für Portweiterleitung von API-Aufrufen)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Benutze Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Öffentlicher Goodreads API Schlüssel"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "Reverse Proxy Authentifizierung zulassen"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "Logintyp"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "Benutze Standard Authentifizierung"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "Benutze LDAP-Login"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "Benutze OAuth"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAP-Server Hostname oder IP-Adresse"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAP Server Port"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAP Verschlüsselung"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP CA-Zertifikat Pfad (Nur für Client Zertifikat Authentifizierung benötigt)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP Zertifikat Pfad (Nur für Client Zertifikat Authentifizierung benötigt)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP Keyfile Pfad (Nur für Client Zertifikat Authentifizierung benötigt)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP Authentifizierung"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "Anonym"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "Unauthentifiziert"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "Einfach"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP Administrator Benutzername"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP Administrator Passwort"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP Distinguished Name (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP User Object Filter"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "Ist der LDAP-Server ein OpenLDAP-Server?"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "Die nachfolgenden Einstellungen werden nur für den Benutzerimport benötigt"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAP Gruppen Objekt Filter"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAP Gruppen Name"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "LDAP Gruppen Mitglieds Feld"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAP Member User Filter Erkennung"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "Automatisch erkennen"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "Benutzerdefinierter Filter"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "LDAP Member User Filter"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "Erhalte %(provider)s OAuth Berechtigungen"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth Client Id"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth Client Secret"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "Externe Programme"

#: cps/templates/config_edit.html:325
msgid "Path to Calibre Binaries"
msgstr "Pfad zu Calibre Dateien"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Calibre E-Book Konverter Einstellungen"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Pfad zum Kepubify E-Book Konverter"

#: cps/templates/config_edit.html:344
msgid "Location of Unrar binary"
msgstr "Pfad zur UnRar-Binärdatei"

#: cps/templates/config_edit.html:360
msgid "Security Settings"
msgstr "Sicherheitseinstellungen"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr "Login Fehlversuche begrenzen"

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr "Backend für Limiter konfigurieren"

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr "Optionen für Limiter Backend"

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr "Überprüfe bei Upload ob Dateiinhalt zur Endung passt"

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr "Sessionschutz"

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr "Einfach"

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr "Stark"

#: cps/templates/config_edit.html:393
msgid "User Password policy"
msgstr "Passwortregeln"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr "Minimale Passwortlänge"

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr "Erzwinge Zahl"

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr "Erzwinge Kleinbuchstaben"

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr "Erzwinge Großbuchstaben"

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr "Zeichen erzwingen (erforderlich für chinesische/japanische/koreanische Zeichen)"

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr "Erzwinge Spezialzeichen"

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "Ansichtskonfiguration"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "Anzahl anzuzeigender zufälliger Bücher"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "Anzahl in Übersicht anzuzeigender Autoren (0=alle werden angezeigt)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "Design"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "Standard-Design"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "caliBlur! dunkles Design"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "Regulärer Ausdruck, um Spalten zu ignorien"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "Verknüpfe Gelesen/Ungelesen-Status mit Calibre-Spalte"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "Sichtbarkeitsbeschränkung basierend auf Calibre Spalte"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "Regulärer Ausdruck für Titelsortierung"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "Standard-Einstellungen für neue Benutzer"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "Administrator"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "Downloads erlauben"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "Anzeige von Büchern erlauben"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "Hochladen erlauben"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "Bearbeiten erlauben"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "Löschen von Büchern erlauben"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "Ändern des Passworts erlauben"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "Editieren öffentlicher Bücherregale erlauben"

#: cps/templates/config_view_edit.html:123
msgid "Default Language"
msgstr "Default Sprache"

#: cps/templates/config_view_edit.html:131
msgid "Default Visible Language of Books"
msgstr "Defaulteinstellung sichtbare Büchersprache"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "Standard-Sichtbarkeiten für neue Benutzer"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "Zeige zufällige Bücher in der Detailansicht"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "Erlaubte/Verbotene Tags hinzufügen"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "Erlaubte/Verbotene Calibre Spalten hinzufügen"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "Im Browser lesen"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "Im Browser anhören"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "Als ungelesen markieren"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "Als gelesen markieren"

#: cps/templates/detail.html:262
msgid "Mark Book as Read or Unread"
msgstr "Buch als gelesen oder ungelesen markieren"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "Gelesen"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "Vom Archiv wiederherstellen"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "Zum Archiv hinzufügen"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr "Buch als archiviert oder nicht markieren, um es in Calibre-Web auszublenden und vom Kobo Reader zu löschen"

#: cps/templates/detail.html:275
msgid "Archive"
msgstr "Archiviert"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "Zu Bücherregal hinzufügen"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(Öffentlich)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "Metadaten bearbeiten"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "Wähle Servertyp"

#: cps/templates/email_edit.html:15
msgid "Standard Email Account"
msgstr "Benutze Standard E-Mail Account"

#: cps/templates/email_edit.html:16
msgid "Gmail Account"
msgstr "G-Mail Konto"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr "G-Mail Konto einrichten"

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Gmail Zugriff entfernen"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "SMTP-Passwort"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "Anhangsgröße"

#: cps/templates/email_edit.html:66
msgid "Save and Send Test Email"
msgstr "Einstellungen speichern und Test E-Mail versenden"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "Zurück"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "Erlaubte Domains für die Registrierung"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "Domain hinzufügen"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "Hinzufügen"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "Domainnamen eingeben"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "Verbotene Domains für eine Registrierung"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "Öffne die .kobo/Kobo/Kobo eReader.conf Datei in einem Texteditor und füge hinzu (oder ersetze):"

#: cps/templates/generate_kobo_auth_url.html:11
msgid "Kobo Token:"
msgstr "Kobo Token:"

#: cps/templates/grid.html:21
msgid "List"
msgstr "Liste"

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "Calibre-Web Instanz ist nicht konfiguriert, bitte den Administrator kontaktieren"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Issue erzeugen"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "Datenbank-Konfiguration"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "Zurück zur Hauptseite"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "Benutzer ausloggen"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr "Aufsteigend nach Downloadzahlen sortieren"

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr "Absteigend nach Downloadzahlen sortieren"

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "Sortiere Autoren in alphabetischer Reihenfolge"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "Sortiere Autoren in umgekehrt alphabetischer Reihenfolge"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "Sortiere Serienindex aufsteigend"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "Sortiere Serienindex absteigend"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "Start"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "Bücher alphabetisch"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "Bücher alphabetisch sortiert"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "Beliebte Publikationen aus dieser Bibliothek basierend auf Anzahl der Downloads."

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "Beliebte Veröffentlichungen dieses Katalogs basierend auf Bewertung."

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "Kürzlich hinzugefügte Bücher"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "Die neuesten Bücher"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "Zufällige Bücher"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "Bücher nach Autor sortiert"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "Bücher nach Verleger sortiert"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "Bücher nach Kategorie sortiert"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "Bücher nach Serie sortiert"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "Bücher nach Sprache sortiert"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "Bücher nach Bewertung sortiert"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "Bücher nach Dateiformat sortiert"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "Bücherregale"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "Bücher in Bücherregalen organisiert"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "Nagivation umschalten"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "Einfach"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "Account"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "Einstellungen"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "Bitte die Seite nicht neu laden"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "Durchsuchen"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "Über"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "Buchdetails"

#: cps/templates/list.html:22
msgid "Grid"
msgstr "Raster"

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "Archiviert"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "Merken"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "Passwort vergessen?"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "Einloggen mit magischem Link"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Calibre-Web Logdatei anzeigen:  "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Calibre-Web Logdatei: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "Stream Ausgabe, kann nicht angezeigt werden"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "Zugriffslogbuch anzeigen: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Calibre-Web Logdatei herunterladen"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "Zugriffslogbuch herunterladen"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "Erlaubte/Verbotene Tags auswählen"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "Erlaubte/Verbotene Calibre Spalten auswählen"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "Erlaubte/Verbotene Tags des Benutzers auswählen"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "Erlaubte/Verbotene Calibre Spalten des Benutzers auswählen"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "Tag eingeben"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "Sichtbeschränkung hinzufügen"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "Dieses Buchformat wird permanent aus der Datenbank gelöscht"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "Das Buch wird endgültig aus der Datenbank"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "und von der Festplatte gelöscht"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Wichtiger Kobo Hinweis: Gelöschte Bücher bleiben auf allen verbundenen Kobo Geräten erhalten."

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "Bücher müssen zuerst archiviert werden und dann mit dem Gerät synchronisiert werden, bevor ein Buch sicher gelöscht werden kann."

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "Dateispeicherort auswählen"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "Typ"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "Name"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "Größe"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "Übergeordnetes Verzeichnis"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "Auswahl"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "Ok"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Calibre-Web E-Book-Katalog"

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr "EPUB-Leser"

#: cps/templates/read.html:80
msgid "Choose a theme below:"
msgstr "Wähle ein Design:"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "Hell"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "Dunkel"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr "Sepia"

#: cps/templates/read.html:90
msgid "Black"
msgstr "Schwarz"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "Text umbrechen, wenn Seitenleiste geöffnet ist."

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr "Schriftgröße"

#: cps/templates/read.html:105
msgid "Font"
msgstr "Schriftart"

#: cps/templates/read.html:106
msgid "Default"
msgstr "Standard"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr "Yahei"

#: cps/templates/read.html:108
msgid "SimSun"
msgstr "SimSun"

#: cps/templates/read.html:109
msgid "KaiTi"
msgstr "KaiTi"

#: cps/templates/read.html:110
msgid "Arial"
msgstr "Arial"

#: cps/templates/read.html:113
msgid "Spread"
msgstr "Aufteilung"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr "Zwei Spalten"

#: cps/templates/read.html:115
msgid "One column"
msgstr "Eine Spalte"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr "Comic-Leser"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "Tastaturkürzel"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "Vorherige Seite"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "Nächste Seite"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr "Einfach Seitendarstellung"

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr "Fortlaufende Seitendarstellung"

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "Optimale Skalierung"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "Skaliere auf Breite"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "Skaliere auf Höhe"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "Skaliere 1:1"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "Rechts rotieren"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "Links rotieren"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "Bild umdrehen"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr "Darstellung"

#: cps/templates/readcbr.html:113
msgid "Single Page"
msgstr "Einzelne Seite"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr "Fortlaufend"

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "Skalierung"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "Beste"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "Breite"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "Höhe"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "1:1"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "Rotieren"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "Umdrehen"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "Horizontal"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "Vertikal"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "Leserichtung"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "Links nach rechts"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "Rechts nach links"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr "Auf Anfang zurücksetzen"

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr "Position speichern"

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "Scrollleiste"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "Zeige"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "Verstecke"

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr "DJVU-Leser"

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr "PDF-Reader"

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr "txt-Leser"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "Neues Benutzerkonto registrieren"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "Wähle einen Benutzernamen"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "Deine E-Mail-Adresse"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "Magischer Link - Neues Gerät autorisieren"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "Benutze dein anderes Gerät, logge dich ein und besuche:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "Danach wirst du automatisch auf diesem Gerät eingeloggt sein."

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "Dieser Link wird in 10 Minuten ablaufen."

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr "Thumbnails für Serien Cover erzeugen"

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "Suchbegriff:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "Ergebnisse für:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "Herausgabedatum von"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "Herausgabedatum bis"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr "Beliebig"

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr "Leer"

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "Tags ausschließen"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "Serien ausschließen"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "Bücherregale ausschliessen"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "Sprachen ausschließen"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "Dateierweiterungen"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "Dateierweiterungen ausschliessen"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "Bewertungen größer als"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "Bewertungen kleiner als"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "Von:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "Bis:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "Lösche dieses Bücherregal"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "Eigenschaften bearbeiten"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "Bücher manuell sortieren"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "Manuelles Sortieren deaktivieren"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "Manuelles Sortieren aktivieren"

#: cps/templates/shelf.html:28
msgid "Sort according to book added to shelf, newest first"
msgstr "Sortiere nach Buch zu Bücherregal hinzugefügt, neuestes zuerst"

#: cps/templates/shelf.html:29
msgid "Sort according to book added to shelf, oldest first"
msgstr "Sortiere nach Buch zu Bücherregal hinzugefügt, ältestes zuerst"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "Bücherregal mit anderen Benutzern teilen"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "Dieses Bücherregal mit Kobo synchronisieren"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "Ziehen und ablegen um Reihenfolge zu ändern"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "Verstecktes Buch"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "Bibliotheksstatistiken"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "Bücher in dieser Bibliothek"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "Autoren in dieser Bibliothek"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "Kategorien in dieser Bibliothek"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "Serien in dieser Bibliothek"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "System Statistiken"

#: cps/templates/stats.html:33
msgid "Program"
msgstr "Programm"

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "Installierte Version"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "Benutzer"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "Aufgabe"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "Status"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "Fortschritt"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "Laufzeit"

#: cps/templates/tasks.html:19
msgid "Message"
msgstr "Meldung"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr "Aktionen"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr "Diese Aufgabe wird abgebrochen. Der Fortschritt wird gespeichert."

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr "Dies ist eine geplante Aufgabe, sie wird zur geplanten Zeit erneut ausgeführt."

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "Benutzerpasswort zurücksetzen"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr "An die E-Mail-Adresse des E-Readers senden. Kommas verwenden, um E-Mails für mehrere E-Reader zu trennen"

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "Zeige nur Bücher mit dieser Sprache"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "OAuth Einstellungen"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "Verknüpfung herstellen"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "Verknüpfung entfernen"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Kobo Sync Token"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "Erzeugen/Ansehen"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr "Kobo Komplettsynchronisation erzwingen"

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "Erlaubte/Verbotene Calibre Spalten hinzufügen"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "Nur Bücher aus ausgewählten Bücherregalen mit Kobo synchronisieren"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "Benutzer löschen"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Kobo Auth URL erzeugen"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "Auswahl..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "Benutzer bearbeiten"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "Benutzernamen eingeben"

#: cps/templates/user_table.html:135
msgid "Enter Email"
msgstr "E-Mailadresse eingeben"

#: cps/templates/user_table.html:136
msgid "Enter eReader Email"
msgstr "E-Reader E-Mail Adresse eingeben"

#: cps/templates/user_table.html:136
msgid "eReader Email"
msgstr "E-Reader E-Mail"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "Anzeigesprache"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "Sichtbare Büchersprachen"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "Erlaubte Tags bearbeiten"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "Erlaubte Tags"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "Verbotene Tags bearbeiten"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "Verbotene Tags"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "Erlaubte/Verbotene Calibre Spalten bearbeiten"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "Erlaubte Calibre Spalten hinzufügen"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "Erlaubte/Verbotene Calibre Spalten bearbeiten"

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr "Verbotene Spaltennamen"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "Passwort ändern"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "Ansicht"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "Öffentliche Bücherregale bearbeiten"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "Ausgesuchte Bücherregale mit Kobo synchronisieren"

#: cps/templates/user_table.html:156
msgid "Show Read/Unread Section"
msgstr "Zeige Gelesen/Ungelesen"

