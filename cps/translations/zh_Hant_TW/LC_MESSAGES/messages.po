# Chinese (Simplified, China) translations for Calibre-Web.
# Copyright (C) 2017 Calibre-Web
# This file is distributed under the same license as the Calibre-Web
# project.
# <AUTHOR> <EMAIL>, 2017.
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2020-09-27 22:18+0800\n"
"Last-Translator: xlivevil <<EMAIL>>\n"
"Language: zh_TW\n"
"Language-Team: zh_Hans_CN <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "統計"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "服務器已重啟，請刷新頁面"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "正在關閉服務器，請關閉窗口"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr ""

#: cps/admin.py:164
msgid "Unknown command"
msgstr "未知命令"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "發送給%(email)s的測試郵件已進入隊列。請檢查任務結果"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "未知"

#: cps/admin.py:233
msgid "Admin page"
msgstr "管理頁"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "基本配置"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "界面配置"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, fuzzy, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "自定義列號：%(column)d在Calibre數據庫中不存在"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "管理用戶"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "全部"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "找不到用戶"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "成功刪除 {} 個用戶"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "顯示全部"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "格式錯誤的請求"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "訪客名稱無法更改"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "遊客無法擁有此角色"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "管理員賬戶不存在，無法刪除管理員角色"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "值必須是 true 或 false"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "無效角色"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "遊客無法擁有此視圖"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "無效視圖"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "訪客的本地化是自動偵測而無法設置的"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "無可用本地化"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "無有效書籍語言"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "參數未找到"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "無效的閱讀列"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "無效的限制列"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Calibre-Web配置已更新"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "您確定刪除Kobo Token嗎？"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "您確定要刪除此網域嗎？"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "您確定要刪除此用戶嗎？"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "您確定要刪除此書架嗎？"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "您確定要修改選定用戶的本地化設置嗎？"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "您確定要修改選定用戶的可見書籍語言嗎？"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "您確定要修改選定用戶的選定角色嗎？"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "您確定要修改選定用戶的選定限制嗎？"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "您確定要修改選定用戶的選定可視化限制嗎？"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "您確定要更改所選用戶的書架同步行為嗎？"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "您確定要更改 Calibre 庫位置嗎？"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr ""

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr ""

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "拒絕"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "允許"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr ""

#: cps/admin.py:987
msgid "Tag not found"
msgstr "標籤未找到"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "無效的動作"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.json 未為 Web 應用程序配置"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "日誌文件路徑無效，請輸入正確的路徑"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "訪問日誌路徑無效，請輸入正確的路徑"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "請輸入LDAP主機、端口、DN和用戶對象標識符"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "請輸入一個LDAP服務賬號和密碼 "

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "請輸入一個LDAP服務賬號"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAP群組對象過濾器需要一個具有“%s”格式標識符號"

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAP群組對象過濾器的括號不匹配"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP用戶對象過濾器需要一個具有“%s”格式標識符"

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAP用戶對象過濾器的括號不匹配"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAP成員用戶過濾器需要有一個“%s”格式標識符號"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "LDAP成員用戶過濾器中有不匹配的括號"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAP CA證書、證書或密鑰位置無效，請輸入正確的路徑"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "添加新用戶"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "編輯郵件服務器設置"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr ""

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "數據庫錯誤：%(error)s。"

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "發送給%(email)s的測試郵件已進入隊列。請檢查任務結果"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "發送測試郵件時出錯：%(res)s"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "請先配置您的郵箱地址..."

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "郵件服務器設置已更新"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr ""

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr ""

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr ""

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr ""

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "發生一個未知錯誤，請稍後再試。"

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr ""

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "編輯用戶 %(nick)s"

#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "用戶 %(user)s 的密碼已重置"

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "請先配置SMTP郵箱設置..."

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "日誌文件查看器"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "正在請求更新包"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "正在下載更新包"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "正在解壓更新包"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "正在替換文件"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "數據庫連接已關閉"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "正在停止服務器"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "更新完成，請點擊確定並刷新頁面"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "更新失敗："

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTP錯誤"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "連接錯誤"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "建立連接超時"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "一般錯誤"

#: cps/admin.py:1551
#, fuzzy
msgid "Update file could not be saved in temp dir"
msgstr "更新文件無法保存在臨時目錄中"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "更新時檔案無法替換變更"

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "未能提取至少一個LDAP用戶"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "未能創建至少一個LDAP用戶"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "錯誤：%(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "錯誤：在LDAP服務器的響應中沒有返回用戶"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "數據庫中沒有找到至少一個LDAP用戶"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{} 用戶被成功導入"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "數據庫路徑無效，請輸入正確的路徑"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "數據庫不可寫入"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "密鑰文件路徑無效，請輸入正確的路徑"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "證書文件路徑無效，請輸入正確的路徑"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr ""

#: cps/admin.py:1917
#, fuzzy
msgid "Database Settings updated"
msgstr "郵件服務器設置已更新"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "數據庫配置"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "請填寫所有欄位！"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "郵箱不在有效網域中"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "添加新用戶"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "用戶“%(user)s”已創建"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "使用此郵箱或用戶名的賬號已經存在。"

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "用戶“%(nick)s”已刪除"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "無法刪除訪客用戶"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "管理員賬戶不存在，無法刪除用戶"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr ""

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "用戶“%(nick)s”已更新"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "搜尋"

#: cps/converter.py:31
msgid "not installed"
msgstr "未安裝"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "缺少執行權限"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "無"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "文件 %(file)s 已上傳"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "轉換的來源或目的格式不存在"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "書籍已經被成功加入到 %(book_format)s 格式轉換隊列"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "轉換此書籍時出現錯誤： %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "糟糕！選擇書名無法打開。文件不存在或者文件不可訪問"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr ""

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "標識符不區分大小寫，覆蓋舊標識符"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, fuzzy, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "%(langname)s 不是一種有效語言"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "已成功更新元數據"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr ""

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "上傳的書籍可能已經存在，建議修改後重新上傳： "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "不能上傳文件附檔名為“%(ext)s”的文件到此服務器"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "不能上傳文件附檔名為“%(ext)s”的文件到此服務器"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "要上傳的文件必須具有附檔名"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "文件 %(filename)s 無法保存到臨時目錄"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "移動封面文件失敗 %(file)s：%(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "書籍格式已成功刪除"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "書籍已成功刪除"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr ""

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "編輯元數據"

#: cps/editbooks.py:1016
#, fuzzy, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "%(seriesindex)s 不是一個有效的數值，忽略"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr ""

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "創建路徑 %(path)s 失敗(權限拒絕)。"

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "保存文件 %(file)s 失敗。"

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "已添加 %(ext)s 格式到 %(book)s"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Google Drive 設置未完成，請嘗試停用並再次激活Google雲端硬碟"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "回調網域名稱尚未被驗證，請在google開發者控制台按步驟驗證網域名稱"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "找不到id為 %(book)d 的書籍的 %(format)s 格式"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "Google Drive %(fn)s 上找不到 %(format)s"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "找不到 %(format)s：%(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
#, fuzzy
msgid "Send to eReader"
msgstr "發送到Kindle"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "此郵件已經通過Calibre-Web發送。"

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web測試郵件"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "測試郵件"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "開啟Calibre-Web之旅"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "用戶註冊電子郵件：%(name)s"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "轉換 %(orig)s 到 %(format)s 並發送到Kindle"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "發送 %(format)s 到Kindle"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s發送到Kindle"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "無法讀取請求的文件。可能有錯誤的權限設置？"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr ""

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "刪除書的文件夾%(id)s失敗，路徑有子文件夾：%(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "刪除書籍 %(id)s失敗：%(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "僅從數據庫中刪除書籍 %(id)s，數據庫中的書籍路徑無效： %(path)s"

#: cps/helper.py:439
#, fuzzy, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "將標題從“%(src)s”改為“%(dest)s”時失敗，錯誤錯信息：%(error)s"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "Google Drive上找不到文件 %(file)s"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "將標題從“%(src)s”改為“%(dest)s”時失敗，錯誤錯信息：%(error)s"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "Google Drive上找不到書籍路徑 %(path)s"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr ""

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "此用戶名已被使用"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "無效的郵件地址格式"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr ""

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr ""

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "下載封面時出錯"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "封面格式出錯"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr ""

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "創建封面路徑失敗"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "封面文件不是有效的圖片文件，或者無法儲存"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "封面文件只支持jpg/jpeg/png/webp/bmp格式文件"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr ""

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "僅將jpg、jpeg文件作為封面文件"

#: cps/helper.py:989 cps/helper.py:1149
#, fuzzy
msgid "Cover"
msgstr "發現"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "找不到UnRar執行文件"

#: cps/helper.py:1017
#, fuzzy
msgid "Error executing UnRar"
msgstr "執行UnRar時出錯"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "數據庫不可寫入"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "缺少執行權限"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "執行UnRar時出錯"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr ""

#: cps/kobo_auth.py:92
#, fuzzy
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "請不要使用localhost訪問Calibre-Web，以便Kobo設備能獲取有效的api_endpoint"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo 設置"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "使用 %(provider)s 註冊"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "您現在已以“%(nickname)s”身份登入"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "連接到%(oauth)s成功"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "登入失敗，沒有用戶與OAuth帳戶關聯"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "取消連接到%(oauth)s成功"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "取消連接到%(oauth)s失敗"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "為連接到%(oauth)s"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "使用Github登入失敗。"

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "從Github獲取用戶信息失敗。"

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "使用Google登入失敗。"

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "從Google獲取用戶信息失敗。"

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub Oauth 錯誤，請重試。"

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "GitHub Oauth 錯誤: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google Oauth 錯誤，請重試。"

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google Oauth 錯誤: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "{} 星"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "登入"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "找不到Token"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "Token已過期"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "成功！請返回您的設備"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "書籍"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "顯示最近書籍"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "熱門書籍"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "顯示熱門書籍"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "已下載書籍"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "顯示下載過的書籍"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "最高評分書籍"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "顯示最高評分書籍"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "已讀書籍"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "顯示閱讀狀態"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "未讀書籍"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "顯示未讀"

#: cps/render_template.py:68
msgid "Discover"
msgstr "發現"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "隨機顯示書籍"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "分類"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "顯示分類選擇"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "叢書"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "顯示叢書選擇"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "作者"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "顯示作者選擇"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "出版社"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "顯示出版社選擇"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "語言"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "顯示語言選擇"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "評分"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "顯示評分選擇"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "文件格式"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "顯示文件格式選擇"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "歸檔書籍"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "顯示歸檔書籍"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "書籍列表"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "顯示書籍列表"

#: cps/search.py:201
msgid "Published after "
msgstr "出版時間晚於 "

#: cps/search.py:208
msgid "Published before "
msgstr "出版時間早於 "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "評分 <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "評分 >= %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "閱讀狀態 = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "搜詢自定義欄位時出錯，請重啟 Calibre-Web"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "進階搜尋"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "指定的書架無效"

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "對不起，您沒有添加書籍到這個書架的權限"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "此書籍已經是書架 %(shelfname)s 的一部分"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "此書籍已被添加到書架：%(sname)s"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr ""

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "書籍已經在書架 %(name)s 中了"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "書籍已經被添加到書架 %(sname)s 中"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "無法添加書籍到書架：%(sname)s"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "此書已從書架 %(sname)s 中刪除"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr ""

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "創建書架"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "對不起，您沒有編輯這個書架的權限"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "編輯書架"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr ""

#: cps/shelf.py:239
#, fuzzy
msgid "Shelf successfully deleted"
msgstr "書籍已成功刪除"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "修改書架 %(name)s 順序"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr ""

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "書架 %(title)s 已創建"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "書架 %(title)s 已修改"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "發生錯誤"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "公共書架：%(title)s已經存在已經存在。"

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "私有書架：%(title)s已經存在。"

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "書架：%(name)s"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "打開書架出錯。書架不存在或不可訪問"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "任務列表"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "等待中"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "失敗"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "已開始"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "已完成"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr ""

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr ""

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "未知狀態"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "讀取更新信息時出現未預期數據"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "無可用更新。您已經安裝了最新版本"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "有新的更新。單擊下面的按鈕以更新到最新版本。"

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "無法獲取更新信息"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "點擊下面按鈕更新到最新穩定版本。"

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "有新的更新。單擊下面的按鈕以更新到版本: %(version)s"

#: cps/updater.py:538
msgid "No release information available"
msgstr "無可用發佈信息"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "發現(隨機書籍)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "熱門書籍（最多下載）"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "%(user)s 下載過的書籍"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "作者：%(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "出版社：%(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "叢書：%(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr ""

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "評分：%(rating)s 星"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "文件格式：%(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "分類：%(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "語言：%(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "下載次數"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "評分列表"

#: cps/web.py:1100
msgid "File formats list"
msgstr "文件格式列表"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "請先配置SMTP郵箱設置..."

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "書籍已經成功加入 %(eReadermail)s 的發送隊列"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "糟糕！發送這本書籍的時候出現錯誤：%(res)s"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "請先設置您的kindle郵箱。"

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr ""

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "註冊"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "郵件服務未配置，請聯繫網站管理員！"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "郵件服務未配置，請聯繫網站管理員！"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "您的電子郵件不允許註冊"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "確認郵件已經發送到您的郵箱。"

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "無法激活LDAP認證"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr ""

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "您現在已以“%(nickname)s”身份登入"

#: cps/web.py:1415
#, fuzzy, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "備援登入“%(nickname)s”：無法訪問LDAP伺服器，或用戶未知"

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "無法登入：%(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "用戶名或密碼錯誤"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "新密碼已發送到您的郵箱"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "發生一個未知錯誤，請稍後再試。"

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "請輸入有效的用戶名進行密碼重置"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "您現在已以“%(nickname)s”身份登入"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "%(name)s 的用戶配置"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "資料已更新"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "使用此郵箱的賬號已經存在。"

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "找不到包含 OAuth 信息的有效 gmail.json 文件"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, fuzzy, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s發送到Kindle"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "沒有發現Calibre 電子書轉換器%(tool)s"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "硬碟上找不到 %(format)s 格式"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "發生未知錯誤，書籍轉換失敗"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify 轉換失敗：%(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "找不到轉換後的文件或文件夾%(folder)s中有多個文件"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre 運行失敗，錯誤信息：%(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "電子書轉換器失敗： %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr ""

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr ""

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr ""

#: cps/tasks/metadata_backup.py:34
#, fuzzy
msgid "Backing up Metadata"
msgstr "編輯元數據"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr ""

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr ""

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "上傳書籍"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "用戶列表"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "用戶名"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "郵箱地址"

#: cps/templates/admin.html:15
#, fuzzy
msgid "Send to eReader Email"
msgstr "接收書籍的Kindle郵箱地址"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "管理權限"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "密碼"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "下載書籍"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "查看書籍"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "編輯書籍"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "刪除數據"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "公共書架"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "導入LDAP用戶"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "SMTP郵件服務器設置"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTP主機名"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTP端口"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "加密"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTP用戶名"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "發信人郵箱"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "電子郵件服務"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "通過Oauth2的Gmail"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "配置"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Calibre 數據庫路徑"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "日誌級別"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "端口"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "擴展端口"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "每頁書籍數"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "上傳"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "匿名瀏覽"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "開放註冊"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "魔法連接遠程登錄"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "反向代理登入"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "反向代理標頭名稱"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "編輯Calibre數據庫配置"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "編輯基本配置"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "編輯界面配置"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr ""

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr ""

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr ""

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr ""

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr ""

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr ""

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr ""

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr ""

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "管理"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "下載除錯包"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "查看日誌文件"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "重啟"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "停止"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr ""

#: cps/templates/admin.html:225
msgid "Version"
msgstr "版本"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "詳情"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "當前版本"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "檢查更新"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "執行更新"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "您確定要重啟嗎？"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "確定"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "取消"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "您確定要關閉嗎？"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "正在更新，請不要刷新頁面"

#: cps/templates/author.html:15
msgid "via"
msgstr "通過"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "在書庫"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "按圖書日期排序，最新優先"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "按圖書日期排序，最舊優先"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "按標題按字母順序排序"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "按標題逆字母順序排序"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "按出版日期排序，最新優先"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "按出版日期排序，最舊優先"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "減少"

#: cps/templates/author.html:97
msgid "More by"
msgstr "更多"

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, python-format
msgid "Book %(index)s of %(range)s"
msgstr "%(range)s 第%(index)s冊"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "語言"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "出版社"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "出版日期"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "簡介:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "上一個"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "下一個"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "無搜索結果"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "首頁"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "搜索書庫"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "登出"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "刪除書籍"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "刪除格式:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "轉換書籍格式:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "轉換從："

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "選擇一個選項"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "轉換到："

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "轉換書籍"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "正在上傳..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "關閉"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "錯誤"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "上傳完成，正在處理中，請稍候..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "上傳格式"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "書名"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "作者"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "標籤"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "叢書編號"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "出版日期"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "評分"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "簡介"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "書號"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "書號類型"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "書號編號"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "移除"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "添加書號"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "從URL獲取封面（JPEG - 圖片將下載並存儲在數據庫中）"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "從本地硬碟上傳封面"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "確認"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "沒有"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "查看保存書籍"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "獲取元數據"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "儲存"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "關鍵字"

#: cps/templates/book_edit.html:240
#, fuzzy
msgid "Search keyword"
msgstr " 搜索關鍵字 "

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "單擊封面將元數據加載到表單"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "加載中..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "來源"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "搜索錯誤！"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "無搜索結果！請嘗試另一個關鍵字。"

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "此欄必須填寫"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "合併選中的書籍"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "刪除選取選項"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "交換作者和標題"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "自動更新書名排序"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "自動更新作者排序"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "輸入書名"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "標題"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "輸入書名排序"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "書名排序"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "輸入作者排序"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "作者排序"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "輸入作者"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "輸入分類"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "輸入叢書"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "叢書編號"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "輸入語言"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "出版日期"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "輸入出版社"

#: cps/templates/book_table.html:73
#, fuzzy
msgid "Enter comments"
msgstr "輸入網域名"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr ""

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr ""

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "閱讀狀態"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
#, fuzzy
msgid "Enter "
msgstr "書號"

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "您真的確認？"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "這本書籍將被合併："

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "合併到這本書籍："

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "合併"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Calibre 數據庫路徑"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "是否使用Google Drive?"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "驗證 Google Drive"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Google Drive Calibre 目錄路徑"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "元數據監視通道ID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "撤回"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "新數據庫路徑無效，請輸入有效的路徑"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "伺服器配置"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "伺服器端口"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "SSL 證書文件路徑(非SSL服務器請留空)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "SSL 密鑰文件路徑(非SSL服務器請留空)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "更新通道"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "穩定版"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "每日夜間版"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr ""

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "日誌文件配置"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "日誌文件路徑和名稱(默認為calibre-web.log)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "啟用訪問日誌"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "訪問日誌路徑和名稱(默認為access.log)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "功能配置"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "儲存到硬碟時同步轉換書名與作者中的非英語字元"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "啟用上傳"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr ""

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "允許上傳的文件格式"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "允許匿名瀏覽"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "啟用註冊"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "使用郵箱或用戶名"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "啟用魔法連接遠程登入"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "啟用Kobo同步"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "代理未知請求到Kobo商店"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "伺服器擴展端口(用於轉發的API調用的端口)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "使用Goodreads"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "Goodreads API Key"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "允許反向代理認證方式"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "登入類型"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "使用標準認證"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "使用LDAP認證"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "使用OAuth認證"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAP服務器主機名或IP地址"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAP伺服器端口"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAP 加密"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS協議"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL協議"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP CA證書路徑(僅用於客戶端證書認證)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP 證書路徑(僅用於客戶端證書認證)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP密鑰文件路徑(僅用於客戶端證書認證)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP 驗證方式"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "匿名"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "無驗證"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "簡單"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP管理員用戶名"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP管理員密碼"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAP專有名稱（DN）"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAP用戶對象過濾器"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "LDAP伺服器是 OpenLDAP？"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "用戶導入需要以下設置"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAP群組對象過濾器"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAP群組名"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "LDAP群組成員欄位"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAP成員用戶過濾器檢測"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "自動檢測"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "自定義過濾器"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "LDAP成員用戶過濾器"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "獲取 %(provider)s OAuth憑證"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth 客戶端Id"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth 客戶端密鑰"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "擴展程序配置"

#: cps/templates/config_edit.html:325
#, fuzzy
msgid "Path to Calibre Binaries"
msgstr "Calibre 電子書轉換器路徑"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Calibre 電子書轉換器設置"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Kepubify 電子書轉換器路徑"

#: cps/templates/config_edit.html:344
#, fuzzy
msgid "Location of Unrar binary"
msgstr "UnRar程序路徑"

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "OAuth設置"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr ""

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr ""

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr ""

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr ""

#: cps/templates/config_edit.html:393
#, fuzzy
msgid "User Password policy"
msgstr "重置用戶密碼"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr ""

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr ""

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr ""

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr ""

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr ""

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "查看配置"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "隨機書籍顯示數量"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "主頁中書籍作者的最大顯示數量（0=不隱藏）"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "主題"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "標準主題"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "黑暗主題"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "可忽略的自定義欄位（正則表達式）"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "選擇自定義欄位作為閱讀狀態"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "選擇自定義欄位作為書籍可見性"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "按規則提取書名後排序（正則表達式）"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "新用戶默認權限設置"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "管理員用戶"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "允許下載書籍"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "允許在線閱讀"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "允許上傳書籍"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "允許編輯書籍"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "允許刪除書籍"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "允許修改密碼"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "允許編輯公共書架"

#: cps/templates/config_view_edit.html:123
#, fuzzy
msgid "Default Language"
msgstr "預設語言"

#: cps/templates/config_view_edit.html:131
#, fuzzy
msgid "Default Visible Language of Books"
msgstr "按預設語言顯示書籍"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "新用戶默認顯示權限"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "在主頁顯示隨機書籍"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "添加顯示或隱藏書籍的標籤值"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "添加顯示或隱藏書籍的自定義欄位值"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "在線閱讀"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "在線聽書"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "標為未讀"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "標為已讀"

#: cps/templates/detail.html:262
#, fuzzy
msgid "Mark Book as Read or Unread"
msgstr "標為未讀"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "已讀"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "從歸檔檔案還原"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "添加到歸檔"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "歸檔"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "添加到書架"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(公共)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "編輯元數據"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "選擇伺服器類型"

#: cps/templates/email_edit.html:15
#, fuzzy
msgid "Standard Email Account"
msgstr "使用標準電子郵件賬號"

#: cps/templates/email_edit.html:16
#, fuzzy
msgid "Gmail Account"
msgstr "選擇伺服器類型"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "撤消 Gmail 訪問權限"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS協議"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS協議"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "SMTP密碼"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "附件大小限制"

#: cps/templates/email_edit.html:66
#, fuzzy
msgid "Save and Send Test Email"
msgstr "保存設置並發送測試郵件"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "後退"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "允許註冊的網域名（白名單)"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "添加網域名"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "新增"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "輸入網域名"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "禁止註冊的網域名（黑名單）"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr "在文本編輯器中打開.kobo/Kobo/Kobo eReader.conf，添加（或編輯）:"

#: cps/templates/generate_kobo_auth_url.html:11
#, fuzzy
msgid "Kobo Token:"
msgstr "Kobo 同步 Token"

#: cps/templates/grid.html:21
msgid "List"
msgstr ""

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "Calibre-Web 實例未配置，請聯繫您的系統管理員！"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "創建問題"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "數據庫配置"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "回到首頁"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "登出賬號"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr ""

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr ""

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "按作者字母順序排序"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "按作者逆字母順序排序"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "按叢書編號排序"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "按叢書編號逆排序"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "開始"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "字母排序書籍"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "按字母排序的書籍"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "基於下載數的熱門書籍。"

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "基於評分的熱門書籍。"

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "最近添加的書籍"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "最新書籍"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "隨機書籍"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "書籍按作者排序"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "書籍按出版社排序"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "書籍按分類排序"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "書籍按叢書排序"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "書籍按語言排序"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "書籍按評分排序"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "書籍按文件格式排序"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "書架列表"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "書架上的書"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "切換導航"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "簡單"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "賬號"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "設置"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "請不要刷新頁面"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "瀏覽"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "關於"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "書籍詳情"

#: cps/templates/list.html:22
msgid "Grid"
msgstr ""

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "歸檔"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "記住我"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "忘記密碼？"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "通過魔法連接登入"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "顯示Calibre-Web日誌： "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Calibre-Web 日誌: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "輸出流，無法顯示"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "顯示訪問日誌： "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "下載Calibre-Web日誌"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "下載訪問日誌"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "選擇標籤值顯示或隱藏書籍"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "選擇自定義欄目值顯示或隱藏本用戶書籍"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "選擇標籤值顯示或隱藏書籍"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "選擇自定義欄目值顯示或隱藏本用戶書籍"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "輸入標籤"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "添加顯示或隱藏書籍的值"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "此書籍格式將從數據庫中永久刪除"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "此書籍將從數據庫中永久刪除"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "，包括從硬碟中"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Kobo 重要說明：被刪除的書籍將保留在任何配對的 Kobo 設備上。"

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "必須先將書籍存檔並同步設備，然後才能安全地刪除書籍。"

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "選擇文件位置"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "類型"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "名稱"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "大小"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "父目錄"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "選擇"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "完成"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Caliebre-Web電子書路徑"

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr "epub閱讀器"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "選擇一個用戶名"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "淺色"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "深色"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr ""

#: cps/templates/read.html:90
#, fuzzy
msgid "Black"
msgstr "後退"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "打開側欄時重排文本。"

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr ""

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "刪除數據"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "等待中"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "垂直"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "已讀"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "無效的閱讀列"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr "Comic閱讀器"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "快捷鍵"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "上一頁"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "下一頁"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr ""

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "縮放到最佳"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "按寬度縮放"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "按高度縮放"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "縮放到原始大小"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "向右旋轉"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "向左旋轉"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "翻轉圖片"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr ""

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "管理頁"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "縮放"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "最佳"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "寬度"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "高度"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "原始"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "旋轉"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "翻轉"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "水平"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "垂直"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "方向"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "從左到右"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "從右到左"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr ""

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr ""

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "工具欄"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "顯示"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "隱藏"

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr "DJVU閱讀器"

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr "PDF閱讀器"

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr "txt閱讀器"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "註冊新賬號"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "選擇一個用戶名"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "您的郵箱地址"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "魔法連接 - 授權新設備"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "在另一個設備上，登入並訪問："

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "驗證後，您將自動在新設備上登入。"

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "此驗證連接將在10分鐘後失效。"

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr ""

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "搜索項："

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "結果："

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "出版日期從"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "出版日期到"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "排除標籤"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "排除叢書"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "排除書架"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "排除語言"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "擴展名"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "排除擴展名"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "評分大於"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "評分小於"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "從："

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "到："

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "刪除此書架"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "編輯書架屬性"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "手動排列書籍排列順序"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "禁止改變順序"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "允許改變順序"

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "按圖書日期排序，最新優先"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "按圖書日期排序，最舊優先"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "書架將被公開"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "同步這個書架到 Kobo device"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "拖拽以重新排序"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "隱藏書籍"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "書庫統計"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "本書籍在此書庫中"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "作者在此書庫中"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "分類在此書庫中"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "叢書在此書庫中"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "系統統計"

#: cps/templates/stats.html:33
msgid "Program"
msgstr ""

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "已安裝版本"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "用戶名稱"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "任務信息"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "任務狀態"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "任務進度"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "運行時間"

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "合併"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr ""

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr ""

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr ""

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "重置用戶密碼"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "按語言顯示書籍"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "OAuth設置"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "連接"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "取消連接"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Kobo 同步 Token"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "新建或查看"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr ""

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "添加顯示或隱藏書籍的自定義欄位值"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "僅同步所選書架中的書籍到 Kobo"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "刪除此用戶"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "生成Kobo Auth 地址"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "選擇..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "編輯用戶"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "輸入用戶名"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "測試郵件"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "Enter eReader Email"
msgstr "接收書籍的Kindle郵箱地址"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "eReader Email"
msgstr "測試郵件"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "本地化"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "可見數據語言"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "編輯允許標籤"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "允許標籤"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "編輯拒絕標籤"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "拒絕標籤"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "編輯顯示欄目值"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "顯示欄目值"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "編輯隱藏欄目值"

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr "隱藏欄目值"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "修改密碼"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "查看書籍"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "編輯公共書架"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "同步所選書架到 Kobo"

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "顯示已讀/未讀選擇"

