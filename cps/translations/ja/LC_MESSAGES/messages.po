# Japanese translations for Calibre-Web.
# Copyright (C) 2017 Calibre-Web
# This file is distributed under the same license as the Calibre-Web
# project.
# FIRST AUTHOR white<<EMAIL>>, 2017.
msgid ""
msgstr ""
"Project-Id-Version:  Calibre-Web\n"
"Report-Msgid-Bugs-To: https://github.com/janeczku/Calibre-Web\n"
"POT-Creation-Date: 2025-06-29 09:44+0200\n"
"PO-Revision-Date: 2018-02-07 02:20-0500\n"
"Last-Translator: subdiox <<EMAIL>>\n"
"Language: ja\n"
"Language-Team: ja <<EMAIL>>\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.15.0\n"

#: cps/about.py:85
msgid "Statistics"
msgstr "統計"

#: cps/admin.py:151
#, fuzzy
msgid "Server restarted, please reload page."
msgstr "サーバーを再起動しました。ページを再読み込みしてください"

#: cps/admin.py:153
#, fuzzy
msgid "Performing Server shutdown, please close window."
msgstr "サーバーをシャットダウンしています。ページを閉じてください"

#: cps/admin.py:161
msgid "Success! Database Reconnected"
msgstr ""

#: cps/admin.py:164
msgid "Unknown command"
msgstr "不明なコマンド"

#: cps/admin.py:175
#, fuzzy
msgid "Success! Books queued for Metadata Backup, please check Tasks for result"
msgstr "%(email)s へのテストメール送信がキューに追加されました。結果を見るにはタスクを確認してください"

#: cps/admin.py:208 cps/editbooks.py:614 cps/editbooks.py:657
#: cps/editbooks.py:1302 cps/updater.py:615 cps/uploader.py:108
#: cps/uploader.py:117
msgid "Unknown"
msgstr "不明"

#: cps/admin.py:233
msgid "Admin page"
msgstr "管理者ページ"

#: cps/admin.py:253
msgid "Basic Configuration"
msgstr "基本設定"

#: cps/admin.py:291
msgid "UI Configuration"
msgstr "UI設定"

#: cps/admin.py:315 cps/admin.py:996 cps/db.py:801 cps/search.py:150
#: cps/web.py:753
#, python-format
msgid "Custom Column No.%(column)d does not exist in calibre database"
msgstr "カスタムカラムの%(column)d列目がcalibreのDBに存在しません"

#: cps/admin.py:333 cps/templates/admin.html:51
msgid "Edit Users"
msgstr "ユーザーを編集"

#: cps/admin.py:377 cps/opds.py:540 cps/templates/grid.html:14
#: cps/templates/list.html:13
msgid "All"
msgstr "全て"

#: cps/admin.py:401 cps/admin.py:1426
msgid "User not found"
msgstr "ユーザーが見つかりません"

#: cps/admin.py:415
msgid "{} users deleted successfully"
msgstr "{}人のユーザーが削除されました"

#: cps/admin.py:438 cps/templates/config_view_edit.html:133
#: cps/templates/user_edit.html:45 cps/templates/user_table.html:81
msgid "Show All"
msgstr "全て表示"

#: cps/admin.py:459 cps/admin.py:465
msgid "Malformed request"
msgstr "不正なリクエスト"

#: cps/admin.py:477 cps/admin.py:2069
msgid "Guest Name can't be changed"
msgstr "ゲストユーザーの名前は変更できません"

#: cps/admin.py:489
msgid "Guest can't have this role"
msgstr "ゲストユーザーはこのロールを持つことができません"

#: cps/admin.py:501 cps/admin.py:2023
msgid "No admin user remaining, can't remove admin role"
msgstr "管理者ユーザーが残っておらず、管理者ロールを削除できません"

#: cps/admin.py:505 cps/admin.py:519
msgid "Value has to be true or false"
msgstr "値はtrueかfalseのどちらかでなければなりません"

#: cps/admin.py:507
msgid "Invalid role"
msgstr "無効なロール"

#: cps/admin.py:511
msgid "Guest can't have this view"
msgstr "ゲストユーザーはこの画面を表示できません"

#: cps/admin.py:521
msgid "Invalid view"
msgstr "無効な表示"

#: cps/admin.py:524
msgid "Guest's Locale is determined automatically and can't be set"
msgstr "ゲストユーザーの言語設定は自動的に決定されるため、固定することはできません"

#: cps/admin.py:528
msgid "No Valid Locale Given"
msgstr "有効な言語設定がありません"

#: cps/admin.py:539
msgid "No Valid Book Language Given"
msgstr "有効な本の言語がありません"

#: cps/admin.py:541 cps/editbooks.py:292
msgid "Parameter not found"
msgstr "パラメータが見つかりません"

#: cps/admin.py:578
msgid "Invalid Read Column"
msgstr "無効な読み取り列"

#: cps/admin.py:584
msgid "Invalid Restricted Column"
msgstr "無効な制限列"

#: cps/admin.py:604 cps/admin.py:1894
msgid "Calibre-Web configuration updated"
msgstr "Calibre-Webの設定を更新しました"

#: cps/admin.py:616
msgid "Do you really want to delete the Kobo Token?"
msgstr "Koboのトークンを削除してもよろしいですか？"

#: cps/admin.py:618
msgid "Do you really want to delete this domain?"
msgstr "このドメインを削除してもよろしいですか？"

#: cps/admin.py:620
msgid "Do you really want to delete this user?"
msgstr "このユーザーを削除してもよろしいですか？"

#: cps/admin.py:622
msgid "Are you sure you want to delete this shelf?"
msgstr "この本棚を削除してもよろしいですか？"

#: cps/admin.py:624
msgid "Are you sure you want to change locales of selected user(s)?"
msgstr "選択したユーザーの言語設定を変更してもよろしいですか？"

#: cps/admin.py:626
msgid "Are you sure you want to change visible book languages for selected user(s)?"
msgstr "選択したユーザーが表示できる本の言語を変更してもよろしいですか？"

#: cps/admin.py:628
msgid "Are you sure you want to change the selected role for the selected user(s)?"
msgstr "選択したユーザーの選択したロールを変更してもよろしいですか？"

#: cps/admin.py:630
msgid "Are you sure you want to change the selected restrictions for the selected user(s)?"
msgstr "選択したユーザーの選択した制限を変更してもよろしいですか？"

#: cps/admin.py:632
msgid "Are you sure you want to change the selected visibility restrictions for the selected user(s)?"
msgstr "選択したユーザーの選択した表示制限を変更してもよろしいですか？"

#: cps/admin.py:635
msgid "Are you sure you want to change shelf sync behavior for the selected user(s)?"
msgstr "選択したユーザーの本棚同期の動作を変更してもよろしいですか？"

#: cps/admin.py:637
msgid "Are you sure you want to change Calibre library location?"
msgstr "Calibreライブラリのパスを変更してもよろしいですか？"

#: cps/admin.py:639
msgid "Calibre-Web will search for updated Covers and update Cover Thumbnails, this may take a while?"
msgstr "Calibre-Webは新しい表紙を検索してそのサムネイルを更新しますが、これにはしばらく時間がかかるかもしれません"

#: cps/admin.py:642
msgid "Are you sure you want delete Calibre-Web's sync database to force a full sync with your Kobo Reader?"
msgstr "Calibre-Webの同期DBを削除して強制的にKoboリーダーと同期してもよろしいですか？"

#: cps/admin.py:885 cps/admin.py:891 cps/admin.py:901 cps/admin.py:911
#: cps/templates/modal_dialogs.html:29 cps/templates/user_table.html:41
#: cps/templates/user_table.html:58
msgid "Deny"
msgstr "拒否"

#: cps/admin.py:887 cps/admin.py:893 cps/admin.py:903 cps/admin.py:913
#: cps/templates/modal_dialogs.html:28 cps/templates/user_table.html:44
#: cps/templates/user_table.html:61
msgid "Allow"
msgstr "許可"

#: cps/admin.py:946
msgid "{} sync entries deleted"
msgstr "{}件の同期項目を削除しました"

#: cps/admin.py:987
msgid "Tag not found"
msgstr "タグが見つかりません"

#: cps/admin.py:1005
msgid "Invalid Action"
msgstr "無効なアクションです"

#: cps/admin.py:1132
msgid "client_secrets.json Is Not Configured For Web Application"
msgstr "client_secrets.jsonがWebアプリケーション用に設定されていません"

#: cps/admin.py:1177
msgid "Logfile Location is not Valid, Please Enter Correct Path"
msgstr "ログファイルの場所が無効です。正しいパスを入力してください"

#: cps/admin.py:1183
msgid "Access Logfile Location is not Valid, Please Enter Correct Path"
msgstr "アクセスログファイルの場所が無効です。正しいパスを入力してください"

#: cps/admin.py:1217
msgid "Please Enter a LDAP Provider, Port, DN and User Object Identifier"
msgstr "LDAPのプロバイダ、ポート番号、DN、ユーザーIDを入力してください"

#: cps/admin.py:1223
msgid "Please Enter a LDAP Service Account and Password"
msgstr "LDAPのサービスアカウント名とパスワードを入力してください"

#: cps/admin.py:1226
msgid "Please Enter a LDAP Service Account"
msgstr "LDAPのサービスアカウント名を入力してください"

#: cps/admin.py:1231
#, python-format
msgid "LDAP Group Object Filter Needs to Have One \"%s\" Format Identifier"
msgstr "LDAPのグループフィルタには \"%s\" というフォーマットのIDが一つ必要です"

#: cps/admin.py:1233
msgid "LDAP Group Object Filter Has Unmatched Parenthesis"
msgstr "LDAPのグループフィルタ内の括弧が一致しません"

#: cps/admin.py:1237
#, python-format
msgid "LDAP User Object Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAPのユーザーフィルタには \"%s\" というフォーマットのIDが一つ必要です"

#: cps/admin.py:1239
msgid "LDAP User Object Filter Has Unmatched Parenthesis"
msgstr "LDAPのユーザーフィルタ内の括弧が一致しません"

#: cps/admin.py:1246
#, python-format
msgid "LDAP Member User Filter needs to Have One \"%s\" Format Identifier"
msgstr "LDAPのメンバーフィルタには \"%s\" というフォーマットのIDが一つ必要です"

#: cps/admin.py:1248
msgid "LDAP Member User Filter Has Unmatched Parenthesis"
msgstr "LDAPのメンバーフィルタ内の括弧が一致しません"

#: cps/admin.py:1255
msgid "LDAP CACertificate, Certificate or Key Location is not Valid, Please Enter Correct Path"
msgstr "LDAPのCA証明書、証明書、キーの場所が無効です。正しいパスを入力してください"

#: cps/admin.py:1286 cps/templates/admin.html:53
msgid "Add New User"
msgstr "新規ユーザーを追加"

#: cps/admin.py:1295 cps/templates/admin.html:100
msgid "Edit Email Server Settings"
msgstr "メールサーバー設定を編集"

#: cps/admin.py:1314
msgid "Success! Gmail Account Verified."
msgstr ""

#: cps/admin.py:1334 cps/admin.py:1337 cps/admin.py:1722 cps/admin.py:1878
#: cps/admin.py:1976 cps/admin.py:2097 cps/editbooks.py:168
#: cps/editbooks.py:561 cps/editbooks.py:1256 cps/shelf.py:90 cps/shelf.py:150
#: cps/shelf.py:193 cps/shelf.py:243 cps/shelf.py:280 cps/shelf.py:354
#: cps/shelf.py:476 cps/tasks/convert.py:160 cps/web.py:1535
#, python-format
msgid "Oops! Database Error: %(error)s."
msgstr "DBエラー: %(error)s"

#: cps/admin.py:1344
#, python-format
msgid "Test e-mail queued for sending to %(email)s, please check Tasks for result"
msgstr "%(email)s へのテストメール送信がキューに追加されました。結果を見るにはタスクを確認してください"

#: cps/admin.py:1347
#, python-format
msgid "There was an error sending the Test e-mail: %(res)s"
msgstr "%(res)s へのテストメール送信中にエラーが発生しました"

#: cps/admin.py:1349
msgid "Please configure your e-mail address first..."
msgstr "初めにメールアドレスを設定してください"

#: cps/admin.py:1351
msgid "Email Server Settings updated"
msgstr "メールサーバーの設定を更新しました"

#: cps/admin.py:1374 cps/templates/admin.html:195
msgid "Edit Scheduled Tasks Settings"
msgstr "スケジュールタスク設定を編集"

#: cps/admin.py:1386
msgid "Invalid start time for task specified"
msgstr "指定したタスクの開始時刻が無効です"

#: cps/admin.py:1391
msgid "Invalid duration for task specified"
msgstr "指定したタスクの期間が無効です"

#: cps/admin.py:1401
msgid "Scheduled tasks settings updated"
msgstr "スケジュールタスクの設定を更新しました"

#: cps/admin.py:1411 cps/admin.py:1460 cps/admin.py:2093 cps/web.py:1325
msgid "Oops! An unknown error occurred. Please try again later."
msgstr "不明なエラーが発生しました。あとで再試行してください。"

#: cps/admin.py:1415
msgid "Settings DB is not Writeable"
msgstr "設定DBが書き込みできません"

#: cps/admin.py:1445 cps/admin.py:2085
#, python-format
msgid "Edit User %(nick)s"
msgstr "ユーザー %(nick)s を編集"

#: cps/admin.py:1457
#, fuzzy, python-format
msgid "Success! Password for user %(user)s reset"
msgstr "ユーザー %(user)s のパスワードをリセット"

#: cps/admin.py:1463
#, fuzzy
msgid "Oops! Please configure the SMTP mail settings."
msgstr "初めにSMTPメールの設定をしてください"

#: cps/admin.py:1474
msgid "Logfile viewer"
msgstr "ログファイルビューア"

#: cps/admin.py:1540
msgid "Requesting update package"
msgstr "更新データを要求中"

#: cps/admin.py:1541
msgid "Downloading update package"
msgstr "更新データをダウンロード中"

#: cps/admin.py:1542
msgid "Unzipping update package"
msgstr "更新データを展開中"

#: cps/admin.py:1543
msgid "Replacing files"
msgstr "ファイルを置換中"

#: cps/admin.py:1544
msgid "Database connections are closed"
msgstr "DB接続を切断"

#: cps/admin.py:1545
msgid "Stopping server"
msgstr "サーバー停止中"

#: cps/admin.py:1546
msgid "Update finished, please press okay and reload page"
msgstr "アップデート完了、OKを押してページを再読み込みしてください"

#: cps/admin.py:1547 cps/admin.py:1548 cps/admin.py:1549 cps/admin.py:1550
#: cps/admin.py:1551 cps/admin.py:1552
msgid "Update failed:"
msgstr "アップデート失敗:"

#: cps/admin.py:1547 cps/updater.py:391 cps/updater.py:626 cps/updater.py:628
msgid "HTTP Error"
msgstr "HTTPエラー"

#: cps/admin.py:1548 cps/updater.py:393 cps/updater.py:630
msgid "Connection error"
msgstr "接続エラー"

#: cps/admin.py:1549 cps/updater.py:395 cps/updater.py:632
msgid "Timeout while establishing connection"
msgstr "接続確立中にタイムアウトしました"

#: cps/admin.py:1550 cps/updater.py:397 cps/updater.py:634
msgid "General error"
msgstr "エラー発生"

#: cps/admin.py:1551
msgid "Update file could not be saved in temp dir"
msgstr "更新データを一時フォルダに保存できませんでした"

#: cps/admin.py:1552
msgid "Files could not be replaced during update"
msgstr "更新中にファイルを置換できませんでした"

#: cps/admin.py:1576
msgid "Failed to extract at least One LDAP User"
msgstr "少なくとも1人のLDAPユーザーの抽出に失敗しました"

#: cps/admin.py:1621
msgid "Failed to Create at Least One LDAP User"
msgstr "少なくとも1人のLDAPユーザーの作成に失敗しました"

#: cps/admin.py:1634
#, python-format
msgid "Error: %(ldaperror)s"
msgstr "エラー: %(ldaperror)s"

#: cps/admin.py:1638
msgid "Error: No user returned in response of LDAP server"
msgstr "エラー: LDAPサーバーのレスポンスでユーザーが返されません"

#: cps/admin.py:1674
msgid "At Least One LDAP User Not Found in Database"
msgstr "DB内にLDAPユーザーが1人も見つかりません"

#: cps/admin.py:1676
msgid "{} User Successfully Imported"
msgstr "{}人のユーザーをインポートしました"

#: cps/admin.py:1734
msgid "Books path not valid"
msgstr ""

#: cps/admin.py:1740
msgid "DB Location is not Valid, Please Enter Correct Path"
msgstr "DBの場所が無効です。正しいパスを入力してください"

#: cps/admin.py:1768
msgid "DB is not Writeable"
msgstr "DBへの書き込みができません"

#: cps/admin.py:1782
msgid "Keyfile Location is not Valid, Please Enter Correct Path"
msgstr "キーファイルの場所が無効です。正しいパスを入力してください"

#: cps/admin.py:1786
msgid "Certfile Location is not Valid, Please Enter Correct Path"
msgstr "証明書ファイルの場所が無効です。正しいパスを入力してください"

#: cps/admin.py:1863
msgid "Password length has to be between 1 and 40"
msgstr ""

#: cps/admin.py:1917
msgid "Database Settings updated"
msgstr "DB設定を更新しました"

#: cps/admin.py:1925
msgid "Database Configuration"
msgstr "DB設定"

#: cps/admin.py:1940 cps/web.py:1299
msgid "Oops! Please complete all fields."
msgstr "全ての項目を入力してください"

#: cps/admin.py:1949
msgid "E-mail is not from valid domain"
msgstr "このメールは有効なドメインからのものではありません"

#: cps/admin.py:1955
msgid "Add new user"
msgstr "新規ユーザー追加"

#: cps/admin.py:1966
#, python-format
msgid "User '%(user)s' created"
msgstr "ユーザー '%(user)s' を作成しました"

#: cps/admin.py:1972
msgid "Oops! An account already exists for this Email. or name."
msgstr "このメールアドレスかニックネームで登録されたアカウントがすでに存在します。"

#: cps/admin.py:2002
#, python-format
msgid "User '%(nick)s' deleted"
msgstr "ユーザー '%(nick)s' を削除しました"

#: cps/admin.py:2005
msgid "Can't delete Guest User"
msgstr "ゲストユーザーは削除できません"

#: cps/admin.py:2008
msgid "No admin user remaining, can't delete user"
msgstr "管理者ユーザーが残っておらず、ユーザーを削除できません"

#: cps/admin.py:2063 cps/web.py:1484
msgid "Email can't be empty and has to be a valid Email"
msgstr ""

#: cps/admin.py:2089
#, python-format
msgid "User '%(nick)s' updated"
msgstr "ユーザー '%(nick)s' を更新しました"

#: cps/basic.py:67 cps/search.py:50 cps/search.py:426
#: cps/templates/basic_layout.html:23 cps/templates/book_edit.html:242
#: cps/templates/feed.xml:34 cps/templates/index.xml:12
#: cps/templates/layout.html:47 cps/templates/layout.html:50
#: cps/templates/search_form.html:247
msgid "Search"
msgstr "検索"

#: cps/converter.py:31
msgid "not installed"
msgstr "インストールされていません"

#: cps/converter.py:32
msgid "Execution permissions missing"
msgstr "実行権限がありません"

#: cps/db.py:1046 cps/templates/config_edit.html:203
#: cps/templates/config_view_edit.html:62 cps/templates/email_edit.html:41
#: cps/web.py:568 cps/web.py:602 cps/web.py:647 cps/web.py:687 cps/web.py:714
#: cps/web.py:995 cps/web.py:1025 cps/web.py:1070 cps/web.py:1098
#: cps/web.py:1137
msgid "None"
msgstr "なし"

#: cps/editbooks.py:154
#, python-format
msgid "File %(file)s uploaded"
msgstr "ファイル %(file)s をアップロードしました"

#: cps/editbooks.py:183
msgid "Source or destination format for conversion missing"
msgstr "変換元の形式または変換後の形式が指定されていません"

#: cps/editbooks.py:191
#, python-format
msgid "Book successfully queued for converting to %(book_format)s"
msgstr "本の %(book_format)s への変換がキューに追加されました"

#: cps/editbooks.py:195
#, python-format
msgid "There was an error converting this book: %(res)s"
msgstr "この本の変換中にエラーが発生しました: %(res)s"

#: cps/editbooks.py:433 cps/editbooks.py:928 cps/web.py:535 cps/web.py:1576
#: cps/web.py:1622 cps/web.py:1672
msgid "Oops! Selected book is unavailable. File does not exist or is not accessible"
msgstr "選択した本は利用できません。ファイルが存在しないか、アクセスできません"

#: cps/editbooks.py:479 cps/editbooks.py:1285
msgid "User has no rights to upload cover"
msgstr "ユーザーは表紙をアップロードする権限がありません"

#: cps/editbooks.py:500 cps/editbooks.py:743
msgid "Identifiers are not Case Sensitive, Overwriting Old Identifier"
msgstr "IDは大文字小文字を区別しません。元のIDを上書きします"

#: cps/editbooks.py:515 cps/editbooks.py:717 cps/editbooks.py:1055
#, python-format
msgid "'%(langname)s' is not a valid language"
msgstr "'%(langname)s' は有効な言語ではありません"

#: cps/editbooks.py:543
msgid "Metadata successfully updated"
msgstr "メタデータを更新しました"

#: cps/editbooks.py:566
msgid "Error editing book: {}"
msgstr "本編集中のエラー: {}"

#: cps/editbooks.py:661
msgid "Uploaded book probably exists in the library, consider to change before upload new: "
msgstr "アップロードした本はすでにライブラリに存在します。新しくアップロードする前に変更を加えてください: "

#: cps/editbooks.py:755 cps/editbooks.py:1202
#, fuzzy
msgid "File type isn't allowed to be uploaded to this server"
msgstr "ファイル拡張子 '%(ext)s' をこのサーバーにアップロードすることは許可されていません"

#: cps/editbooks.py:761 cps/editbooks.py:1213
#, python-format
msgid "File extension '%(ext)s' is not allowed to be uploaded to this server"
msgstr "ファイル拡張子 '%(ext)s' をこのサーバーにアップロードすることは許可されていません"

#: cps/editbooks.py:765 cps/editbooks.py:1218
msgid "File to be uploaded must have an extension"
msgstr "アップロードするファイルには拡張子が必要です"

#: cps/editbooks.py:773
#, python-format
msgid "File %(filename)s could not saved to temp dir"
msgstr "ファイル %(filename)s は一時フォルダに保存できませんでした"

#: cps/editbooks.py:793
#, python-format
msgid "Failed to Move Cover File %(file)s: %(error)s"
msgstr "表紙ファイル %(file)s の移動に失敗しました: %(error)s"

#: cps/editbooks.py:850 cps/editbooks.py:852
msgid "Book Format Successfully Deleted"
msgstr "本の形式を削除しました"

#: cps/editbooks.py:859 cps/editbooks.py:861
msgid "Book Successfully Deleted"
msgstr "本を削除しました"

#: cps/editbooks.py:913
msgid "You are missing permissions to delete books"
msgstr "本を削除する権限がありません"

#: cps/editbooks.py:963
msgid "edit metadata"
msgstr "メタデータを編集"

#: cps/editbooks.py:1016
#, fuzzy, python-format
msgid "Seriesindex: %(seriesindex)s is not a valid number, skipping"
msgstr "%(seriesindex)s は有効な数字ではありません。スキップします"

#: cps/editbooks.py:1207
msgid "User has no rights to upload additional file formats"
msgstr "新たなファイル形式をアップロードする権限がありません"

#: cps/editbooks.py:1231
#, python-format
msgid "Failed to create path %(path)s (Permission denied)."
msgstr "%(path)s の作成に失敗しました (Permission denied)。"

#: cps/editbooks.py:1238
#, python-format
msgid "Failed to store file %(file)s."
msgstr "ファイル %(file)s を保存できません。"

#: cps/editbooks.py:1263
#, python-format
msgid "File format %(ext)s added to %(book)s"
msgstr "ファイル形式 %(ext)s が %(book)s に追加されました"

#: cps/gdrive.py:58
msgid "Google Drive setup not completed, try to deactivate and activate Google Drive again"
msgstr "Googleドライブの設定が完了していません。Googleドライブを無効にしてから再度有効にしてみてください"

#: cps/gdrive.py:96
msgid "Callback domain is not verified, please follow steps to verify domain in google developer console"
msgstr "コールバックドメインが認証されていません。Google Developer Consoleでドメインを認証してください"

#: cps/helper.py:87
#, python-format
msgid "%(format)s format not found for book id: %(book)d"
msgstr "ID: %(book)d の本に %(format)s フォーマットはありません"

#: cps/helper.py:94 cps/tasks/convert.py:93
#, python-format
msgid "%(format)s not found on Google Drive: %(fn)s"
msgstr "Googleドライブ: %(fn)s に %(format)s はありません"

#: cps/helper.py:99
#, python-format
msgid "%(format)s not found: %(fn)s"
msgstr "%(format)s がありません: %(fn)s"

#: cps/helper.py:104 cps/helper.py:233 cps/templates/detail.html:66
msgid "Send to eReader"
msgstr "E-Readerに送信"

#: cps/helper.py:105 cps/helper.py:125 cps/helper.py:235
#, fuzzy
msgid "This Email has been sent via Calibre-Web."
msgstr "このメールはCalibre-Web経由で送信されました。"

#: cps/helper.py:123
#, fuzzy
msgid "Calibre-Web Test Email"
msgstr "Calibre-Web テストメール"

#: cps/helper.py:124
#, fuzzy
msgid "Test Email"
msgstr "テストメール"

#: cps/helper.py:141
msgid "Get Started with Calibre-Web"
msgstr "Calibre-Webを始める"

#: cps/helper.py:146
#, fuzzy, python-format
msgid "Registration Email for user: %(name)s"
msgstr "ユーザー: %(name)s 用の登録メール"

#: cps/helper.py:157 cps/helper.py:163
#, fuzzy, python-format
msgid "Convert %(orig)s to %(format)s and send to eReader"
msgstr "%(orig)s を %(format)s に変換してからE-Readerに送信"

#: cps/helper.py:182 cps/helper.py:186 cps/helper.py:190
#, fuzzy, python-format
msgid "Send %(format)s to eReader"
msgstr "E-Readerに %(format)s を送信"

#: cps/helper.py:230
#, fuzzy, python-format
msgid "%(book)s send to eReader"
msgstr "%(book)s をE-Readerに送信"

#: cps/helper.py:237
msgid "The requested file could not be read. Maybe wrong permissions?"
msgstr "要求されたファイルを読み込めませんでした。権限設定が正しいか確認してください。"

#: cps/helper.py:352
msgid "Read status could not set: {}"
msgstr "読み込みステータスを設定できません: {}"

#: cps/helper.py:375
#, python-format
msgid "Deleting bookfolder for book %(id)s failed, path has subfolders: %(path)s"
msgstr "%(id)s の本フォルダの削除に失敗しました。そこにはサブフォルダがあります: %(path)s"

#: cps/helper.py:381
#, python-format
msgid "Deleting book %(id)s failed: %(message)s"
msgstr "本 %(id)s の削除に失敗しました: %(message)s"

#: cps/helper.py:392
#, python-format
msgid "Deleting book %(id)s from database only, book path in database not valid: %(path)s"
msgstr "本 %(id)s はDBのみから削除されます。DB内の本のパスが有効ではありません: %(path)s"

#: cps/helper.py:439
#, python-format
msgid "Rename author from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "エラー: %(error)s により、著者名を %(src)s から %(dest)s に変更できませんでした"

#: cps/helper.py:507 cps/helper.py:516
#, python-format
msgid "File %(file)s not found on Google Drive"
msgstr "ファイル %(file)s はGoogleドライブ上にありません"

#: cps/helper.py:559
#, python-format
msgid "Rename title from: '%(src)s' to '%(dest)s' failed with error: %(error)s"
msgstr "エラー: %(error)s により、タイトルを %(src)s から %(dest)s に変更できませんでした"

#: cps/helper.py:597
#, python-format
msgid "Book path %(path)s not found on Google Drive"
msgstr "本のパス %(path)s はGoogleドライブ上にありません"

#: cps/helper.py:657
msgid "Found an existing account for this Email address"
msgstr ""

#: cps/helper.py:665
msgid "This username is already taken"
msgstr "このユーザー名はすでに使われています"

#: cps/helper.py:679
#, fuzzy
msgid "Invalid Email address format"
msgstr "メールアドレスの形式が無効"

#: cps/helper.py:701
msgid "Password doesn't comply with password validation rules"
msgstr ""

#: cps/helper.py:847
msgid "Python module 'advocate' is not installed but is needed for cover uploads"
msgstr "表紙のアップロードに必要なPythonモジュール 'advocate' がインストールされていません"

#: cps/helper.py:857
msgid "Error Downloading Cover"
msgstr "表紙のダウンロードに失敗しました"

#: cps/helper.py:860
msgid "Cover Format Error"
msgstr "表紙形式エラー"

#: cps/helper.py:863
msgid "You are not allowed to access localhost or the local network for cover uploads"
msgstr "表紙アップロードのためにlocalhostやローカルネットワークにアクセスすることは許可されていません"

#: cps/helper.py:873
msgid "Failed to create path for cover"
msgstr "表紙ファイルの作成に失敗しました"

#: cps/helper.py:889
msgid "Cover-file is not a valid image file, or could not be stored"
msgstr "表紙ファイルが有効な画像ファイルでないか、または保存できませんでした"

#: cps/helper.py:900
msgid "Only jpg/jpeg/png/webp/bmp files are supported as coverfile"
msgstr "表紙ファイルは jpg/jpeg/png/webp/bmp のみ対応しています"

#: cps/helper.py:912
msgid "Invalid cover file content"
msgstr "表紙ファイルの内容が無効です"

#: cps/helper.py:916
msgid "Only jpg/jpeg files are supported as coverfile"
msgstr "表紙ファイルは jpg/jpeg のみ対応しています"

#: cps/helper.py:989 cps/helper.py:1149
#, fuzzy
msgid "Cover"
msgstr "見つける"

#: cps/helper.py:1006
msgid "UnRar binary file not found"
msgstr "UnRarのバイナリファイルが見つかりません"

#: cps/helper.py:1017
#, fuzzy
msgid "Error executing UnRar"
msgstr "UnRarの実行中にエラーが発生しました"

#: cps/helper.py:1025
msgid "Could not find the specified directory"
msgstr ""

#: cps/helper.py:1028
msgid "Please specify a directory, not a file"
msgstr ""

#: cps/helper.py:1042
#, fuzzy
msgid "Calibre binaries not viable"
msgstr "DBへの書き込みができません"

#: cps/helper.py:1051
#, python-format
msgid "Missing calibre binaries: %(missing)s"
msgstr ""

#: cps/helper.py:1053
#, fuzzy, python-format
msgid "Missing executable permissions: %(missing)s"
msgstr "実行権限がありません"

#: cps/helper.py:1058
#, fuzzy
msgid "Error executing Calibre"
msgstr "UnRarの実行中にエラーが発生しました"

#: cps/helper.py:1151 cps/templates/admin.html:216
msgid "Queue all books for metadata backup"
msgstr ""

#: cps/kobo_auth.py:92
msgid "Please access Calibre-Web from non localhost to get valid api_endpoint for kobo device"
msgstr "localhost以外からCalibre-Webにアクセスし、有効なKobo端末用APIエンドポイントを取得してください"

#: cps/kobo_auth.py:118
msgid "Kobo Setup"
msgstr "Kobo設定"

#: cps/oauth_bb.py:78
#, python-format
msgid "Register with %(provider)s"
msgstr "%(provider)s で登録"

#: cps/oauth_bb.py:139 cps/remotelogin.py:131
#, python-format
msgid "Success! You are now logged in as: %(nickname)s"
msgstr "%(nickname)s としてログイン中"

#: cps/oauth_bb.py:149
#, python-format
msgid "Link to %(oauth)s Succeeded"
msgstr "%(oauth)s との連携に成功しました"

#: cps/oauth_bb.py:156
msgid "Login failed, No User Linked With OAuth Account"
msgstr "ログイン失敗、OAuthアカウントと連携しているユーザーが存在しません"

#: cps/oauth_bb.py:198
#, python-format
msgid "Unlink to %(oauth)s Succeeded"
msgstr "%(oauth)s との連携解除に成功しました"

#: cps/oauth_bb.py:203
#, python-format
msgid "Unlink to %(oauth)s Failed"
msgstr "%(oauth)s との連携解除に失敗しました"

#: cps/oauth_bb.py:206
#, python-format
msgid "Not Linked to %(oauth)s"
msgstr "%(oauth)s と連携していません"

#: cps/oauth_bb.py:263
msgid "Failed to log in with GitHub."
msgstr "GitHubアカウントでのログインに失敗しました。"

#: cps/oauth_bb.py:269
msgid "Failed to fetch user info from GitHub."
msgstr "GitHubからのユーザー情報取得に失敗しました。"

#: cps/oauth_bb.py:281
msgid "Failed to log in with Google."
msgstr "Googleアカウントでのログインに失敗しました。"

#: cps/oauth_bb.py:287
msgid "Failed to fetch user info from Google."
msgstr "Googleからのユーザー情報取得に失敗しました。"

#: cps/oauth_bb.py:335
msgid "GitHub Oauth error, please retry later."
msgstr "GitHub OAuth エラー、再度お試しください。"

#: cps/oauth_bb.py:338
msgid "GitHub Oauth error: {}"
msgstr "GitHub OAuth エラー: {}"

#: cps/oauth_bb.py:359
msgid "Google Oauth error, please retry later."
msgstr "Google OAuth エラー、再度お試しください。"

#: cps/oauth_bb.py:362
msgid "Google Oauth error: {}"
msgstr "Google OAuth エラー: {}"

#: cps/opds.py:299
msgid "{} Stars"
msgstr "星{}"

#: cps/remotelogin.py:63 cps/templates/layout.html:69
#: cps/templates/layout.html:104 cps/templates/login.html:4
#: cps/templates/login.html:21 cps/web.py:1361
msgid "Login"
msgstr "ログイン"

#: cps/remotelogin.py:75 cps/remotelogin.py:109
msgid "Token not found"
msgstr "トークンが見つかりません"

#: cps/remotelogin.py:84 cps/remotelogin.py:117
msgid "Token has expired"
msgstr "トークンが無効です"

#: cps/remotelogin.py:93
msgid "Success! Please return to your device"
msgstr "成功です！端末に戻ってください"

#: cps/render_template.py:41 cps/web.py:424
msgid "Books"
msgstr "本"

#: cps/render_template.py:43
msgid "Show recent books"
msgstr "最近追加された本を表示"

#: cps/render_template.py:44 cps/templates/index.xml:27
msgid "Hot Books"
msgstr "人気の本"

#: cps/render_template.py:46
msgid "Show Hot Books"
msgstr "人気の本を表示"

#: cps/render_template.py:48 cps/render_template.py:53
msgid "Downloaded Books"
msgstr "ダウンロードされた本"

#: cps/render_template.py:50 cps/render_template.py:55
#: cps/templates/user_table.html:167
msgid "Show Downloaded Books"
msgstr "ダウンロードされた本を表示"

#: cps/render_template.py:58 cps/templates/index.xml:36 cps/web.py:439
msgid "Top Rated Books"
msgstr "高評価の本"

#: cps/render_template.py:60 cps/templates/user_table.html:161
msgid "Show Top Rated Books"
msgstr "高評価の本を表示"

#: cps/render_template.py:61 cps/templates/index.xml:63
#: cps/templates/index.xml:67 cps/web.py:772
msgid "Read Books"
msgstr "既読の本"

#: cps/render_template.py:63
#, fuzzy
msgid "Show Read and Unread"
msgstr "既読の本と未読の本を表示"

#: cps/render_template.py:65 cps/templates/index.xml:70
#: cps/templates/index.xml:74 cps/web.py:775
msgid "Unread Books"
msgstr "未読の本"

#: cps/render_template.py:67
msgid "Show unread"
msgstr "未読の本を表示"

#: cps/render_template.py:68
msgid "Discover"
msgstr "見つける"

#: cps/render_template.py:70 cps/templates/index.xml:58
#: cps/templates/user_table.html:159 cps/templates/user_table.html:162
msgid "Show Random Books"
msgstr "ランダムに本を表示"

#: cps/render_template.py:71 cps/templates/book_table.html:67
#: cps/templates/index.xml:97 cps/web.py:1141
msgid "Categories"
msgstr "カテゴリ"

#: cps/render_template.py:73 cps/templates/user_table.html:158
#, fuzzy
msgid "Show Category Section"
msgstr "カテゴリ選択を表示"

#: cps/render_template.py:74 cps/templates/book_edit.html:86
#: cps/templates/book_table.html:68 cps/templates/index.xml:106
#: cps/templates/search_form.html:70 cps/web.py:1031 cps/web.py:1043
msgid "Series"
msgstr "シリーズ"

#: cps/render_template.py:76 cps/templates/user_table.html:157
#, fuzzy
msgid "Show Series Section"
msgstr "シリーズ選択を表示"

#: cps/render_template.py:77 cps/templates/book_table.html:66
#: cps/templates/index.xml:79
msgid "Authors"
msgstr "著者"

#: cps/render_template.py:79 cps/templates/user_table.html:160
#, fuzzy
msgid "Show Author Section"
msgstr "著者選択を表示"

#: cps/render_template.py:81 cps/templates/book_table.html:72
#: cps/templates/index.xml:88 cps/web.py:999
msgid "Publishers"
msgstr "出版社"

#: cps/render_template.py:83 cps/templates/user_table.html:163
#, fuzzy
msgid "Show Publisher Section"
msgstr "出版社選択を表示"

#: cps/render_template.py:84 cps/templates/book_table.html:70
#: cps/templates/index.xml:115 cps/templates/search_form.html:108
#: cps/web.py:1113
msgid "Languages"
msgstr "言語"

#: cps/render_template.py:87 cps/templates/user_table.html:155
#, fuzzy
msgid "Show Language Section"
msgstr "言語選択を表示"

#: cps/render_template.py:88 cps/templates/index.xml:124
msgid "Ratings"
msgstr "評価"

#: cps/render_template.py:90 cps/templates/user_table.html:164
#, fuzzy
msgid "Show Ratings Section"
msgstr "評価選択を表示"

#: cps/render_template.py:91 cps/templates/index.xml:133
msgid "File formats"
msgstr "ファイル形式"

#: cps/render_template.py:93 cps/templates/user_table.html:165
#, fuzzy
msgid "Show File Formats Section"
msgstr "ファイル形式選択を表示"

#: cps/render_template.py:95 cps/web.py:798
msgid "Archived Books"
msgstr "アーカイブされた本"

#: cps/render_template.py:97 cps/templates/user_table.html:166
#, fuzzy
msgid "Show Archived Books"
msgstr "アーカイブされた本を表示"

#: cps/render_template.py:100 cps/web.py:829
msgid "Books List"
msgstr "本の一覧"

#: cps/render_template.py:102 cps/templates/user_table.html:168
msgid "Show Books List"
msgstr "本の一覧を表示"

#: cps/search.py:201
msgid "Published after "
msgstr "これ以降に出版 "

#: cps/search.py:208
msgid "Published before "
msgstr "これ以前に出版 "

#: cps/search.py:230
#, python-format
msgid "Rating <= %(rating)s"
msgstr "評価 <= %(rating)s"

#: cps/search.py:232
#, python-format
msgid "Rating >= %(rating)s"
msgstr "評価 >= %(rating)s"

#: cps/search.py:234
#, fuzzy, python-format
msgid "Read Status = '%(status)s'"
msgstr "既読/未読状況 = %(status)s"

#: cps/search.py:351
msgid "Error on search for custom columns, please restart Calibre-Web"
msgstr "カスタムカラムの検索でエラーが発生しました。Calibre-Webを再起動してください"

#: cps/search.py:370 cps/search.py:402 cps/templates/layout.html:58
msgid "Advanced Search"
msgstr "詳細検索"

#: cps/shelf.py:49 cps/shelf.py:111
msgid "Invalid shelf specified"
msgstr "指定された本棚は無効です"

#: cps/shelf.py:55
msgid "Sorry you are not allowed to add a book to that shelf"
msgstr "申し訳ありませんが、あなたはこの本棚に本を追加することが許可されていません"

#: cps/shelf.py:64
#, python-format
msgid "Book is already part of the shelf: %(shelfname)s"
msgstr "この本は %(shelfname)s にすでに追加されています"

#: cps/shelf.py:77
#, python-format
msgid "%(book_id)s is a invalid Book Id. Could not be added to Shelf"
msgstr ""

#: cps/shelf.py:97
#, python-format
msgid "Book has been added to shelf: %(sname)s"
msgstr "本を %(sname)s に追加しました"

#: cps/shelf.py:116
msgid "You are not allowed to add a book to the shelf"
msgstr "あなたはこの本棚に本を追加することが許可されていません"

#: cps/shelf.py:134
#, python-format
msgid "Books are already part of the shelf: %(name)s"
msgstr "これらの本は %(name)s にすでに追加されています"

#: cps/shelf.py:146
#, python-format
msgid "Books have been added to shelf: %(sname)s"
msgstr "本が %(sname)s に追加されました"

#: cps/shelf.py:153
#, python-format
msgid "Could not add books to shelf: %(sname)s"
msgstr "%(sname)s に本を追加できません"

#: cps/shelf.py:199
#, python-format
msgid "Book has been removed from shelf: %(sname)s"
msgstr "本が %(sname)s から削除されました"

#: cps/shelf.py:208
msgid "Sorry you are not allowed to remove a book from this shelf"
msgstr "申し訳ありませんが、あなたはこの本棚から本を削除することが許可されていません"

#: cps/shelf.py:218 cps/templates/layout.html:160
msgid "Create a Shelf"
msgstr "本棚を作成する"

#: cps/shelf.py:226
msgid "Sorry you are not allowed to edit this shelf"
msgstr "申し訳ありませんが、あなたはこの本棚を編集することが許可されていません"

#: cps/shelf.py:228
msgid "Edit a shelf"
msgstr "本棚を編集する"

#: cps/shelf.py:237
msgid "Error deleting Shelf"
msgstr "本棚の削除中にエラーが発生しました"

#: cps/shelf.py:239
msgid "Shelf successfully deleted"
msgstr "本棚を削除しました"

#: cps/shelf.py:289
#, python-format
msgid "Change order of Shelf: '%(name)s'"
msgstr "'%(name)s' 内の本の順番を変更する"

#: cps/shelf.py:324
msgid "Sorry you are not allowed to create a public shelf"
msgstr "申し訳ありませんが、あなたはみんなの本棚を作成することが許可されていません"

#: cps/shelf.py:341
#, python-format
msgid "Shelf %(title)s created"
msgstr "%(title)s を作成しました"

#: cps/shelf.py:344
#, python-format
msgid "Shelf %(title)s changed"
msgstr "%(title)s を変更しました"

#: cps/shelf.py:358
msgid "There was an error"
msgstr "エラーが発生しました"

#: cps/shelf.py:380
#, python-format
msgid "A public shelf with the name '%(title)s' already exists."
msgstr "'%(title)s' という名前のみんなの本棚はすでに存在します。"

#: cps/shelf.py:391
#, python-format
msgid "A private shelf with the name '%(title)s' already exists."
msgstr "'%(title)s' という名前の本棚はすでに存在します。"

#: cps/shelf.py:481
#, python-format
msgid "Shelf: '%(name)s'"
msgstr "本棚: '%(name)s'"

#: cps/shelf.py:487
msgid "Error opening shelf. Shelf does not exist or is not accessible"
msgstr "本棚を開けません。この本棚は存在しないかアクセスできません"

#: cps/tasks_status.py:47 cps/templates/layout.html:91
#: cps/templates/tasks.html:7
msgid "Tasks"
msgstr "タスク"

#: cps/tasks_status.py:63
msgid "Waiting"
msgstr "待機中"

#: cps/tasks_status.py:65
msgid "Failed"
msgstr "失敗"

#: cps/tasks_status.py:67
msgid "Started"
msgstr "開始"

#: cps/tasks_status.py:69
msgid "Finished"
msgstr "終了"

#: cps/tasks_status.py:71
msgid "Ended"
msgstr "終了"

#: cps/tasks_status.py:73
msgid "Cancelled"
msgstr "キャンセル"

#: cps/tasks_status.py:75
msgid "Unknown Status"
msgstr "不明"

#: cps/updater.py:433 cps/updater.py:444 cps/updater.py:545 cps/updater.py:560
msgid "Unexpected data while reading update information"
msgstr "アップデート情報の読み込み中に予期しないデータが見つかりました"

#: cps/updater.py:440 cps/updater.py:552
msgid "No update available. You already have the latest version installed"
msgstr "アップデートはありません。すでに最新バージョンがインストールされています"

#: cps/updater.py:458
msgid "A new update is available. Click on the button below to update to the latest version."
msgstr "アップデートが利用可能です。下のボタンをクリックして最新バージョンにアップデートしてください。"

#: cps/updater.py:476
msgid "Could not fetch update information"
msgstr "アップデート情報を取得できません"

#: cps/updater.py:486
msgid "Click on the button below to update to the latest stable version."
msgstr "下のボタンをクリックして最新の安定バージョンにアップデートしてください。"

#: cps/updater.py:495 cps/updater.py:509 cps/updater.py:520
#, python-format
msgid "A new update is available. Click on the button below to update to version: %(version)s"
msgstr "アップデートが利用可能です。下のボタンをクリックしてバージョン: %(version)s にアップデートしてください。"

#: cps/updater.py:538
msgid "No release information available"
msgstr "リリース情報がありません"

#: cps/templates/index.html:6 cps/web.py:451
msgid "Discover (Random Books)"
msgstr "本を見つける (ランダムに表示)"

#: cps/web.py:487
msgid "Hot Books (Most Downloaded)"
msgstr "人気の本 (最もダウンロードされた本)"

#: cps/web.py:518
#, python-format
msgid "Downloaded books by %(user)s"
msgstr "%(user)s がダウンロードした本"

#: cps/web.py:551
#, python-format
msgid "Author: %(name)s"
msgstr "著者: %(name)s"

#: cps/web.py:587
#, python-format
msgid "Publisher: %(name)s"
msgstr "出版社: %(name)s"

#: cps/web.py:615
#, python-format
msgid "Series: %(serie)s"
msgstr "シリーズ: %(serie)s"

#: cps/web.py:629
msgid "Rating: None"
msgstr "評価: なし"

#: cps/web.py:638
#, python-format
msgid "Rating: %(rating)s stars"
msgstr "評価: 星%(rating)s"

#: cps/web.py:669
#, python-format
msgid "File format: %(format)s"
msgstr "ファイル形式: %(format)s"

#: cps/web.py:704
#, python-format
msgid "Category: %(name)s"
msgstr "カテゴリ: %(name)s"

#: cps/web.py:733
#, python-format
msgid "Language: %(name)s"
msgstr "言語: %(name)s"

#: cps/templates/admin.html:16 cps/web.py:971
msgid "Downloads"
msgstr "ダウンロード数"

#: cps/web.py:1073
msgid "Ratings list"
msgstr "評価一覧"

#: cps/web.py:1100
msgid "File formats list"
msgstr "ファイル形式一覧"

#: cps/web.py:1259
#, fuzzy
msgid "Please configure the SMTP mail settings first..."
msgstr "初めにSMTPメールの設定をしてください"

#: cps/web.py:1265
#, python-format
msgid "Success! Book queued for sending to %(eReadermail)s"
msgstr "本の %(eReadermail)s への送信がキューに追加されました"

#: cps/web.py:1268
#, python-format
msgid "Oops! There was an error sending book: %(res)s"
msgstr "%(res)s を送信中にエラーが発生しました"

#: cps/web.py:1270
#, fuzzy
msgid "Oops! Please update your profile with a valid eReader Email."
msgstr "初めにKindleのメールアドレスを設定してください"

#: cps/web.py:1286
msgid "Please wait one minute to register next user"
msgstr ""

#: cps/templates/layout.html:70 cps/templates/layout.html:105
#: cps/templates/login.html:27 cps/templates/register.html:17 cps/web.py:1287
#: cps/web.py:1291 cps/web.py:1296 cps/web.py:1300 cps/web.py:1306
#: cps/web.py:1326 cps/web.py:1330 cps/web.py:1343 cps/web.py:1346
msgid "Register"
msgstr "登録"

#: cps/web.py:1290 cps/web.py:1393
#, fuzzy
msgid "Connection error to limiter backend, please contact your administrator"
msgstr "メールサーバーが設定されていません。管理者に連絡してください"

#: cps/web.py:1295 cps/web.py:1342
msgid "Oops! Email server is not configured, please contact your administrator."
msgstr "メールサーバーが設定されていません。管理者に連絡してください"

#: cps/web.py:1328
msgid "Oops! Your Email is not allowed."
msgstr "このメールアドレスは登録が許可されていません"

#: cps/web.py:1331
msgid "Success! Confirmation Email has been sent."
msgstr "確認メールがこのメールアドレスに送信されました。"

#: cps/web.py:1376 cps/web.py:1399
#, fuzzy
msgid "Cannot activate LDAP authentication"
msgstr "LDAP認証を有効化できません"

#: cps/web.py:1389
msgid "Please wait one minute before next login"
msgstr ""

#: cps/web.py:1408
#, fuzzy, python-format
msgid "you are now logged in as: '%(nickname)s'"
msgstr "%(nickname)s としてログイン中"

#: cps/web.py:1415
#, fuzzy, python-format
msgid "Fallback Login as: '%(nickname)s', LDAP Server not reachable, or user not known"
msgstr "代わりに '%(nickname)s' としてログインします。LDAPサーバーにアクセスできないか、ユーザーが存在しません"

#: cps/web.py:1420
#, fuzzy, python-format
msgid "Could not login: %(message)s"
msgstr "ログインできません: %(message)s"

#: cps/web.py:1424 cps/web.py:1449
#, fuzzy
msgid "Wrong Username or Password"
msgstr "ユーザー名またはパスワードが違います"

#: cps/web.py:1431
#, fuzzy
msgid "New Password was sent to your email address"
msgstr "新しいパスワードがあなたのメールアドレスに送信されました"

#: cps/web.py:1435
#, fuzzy
msgid "An unknown error occurred. Please try again later."
msgstr "不明なエラーが発生しました。あとで再試行してください。"

#: cps/web.py:1437
#, fuzzy
msgid "Please enter valid username to reset password"
msgstr "パスワードをリセットするには、有効なユーザー名を入力してください"

#: cps/web.py:1445
#, fuzzy, python-format
msgid "You are now logged in as: '%(nickname)s'"
msgstr "%(nickname)s としてログイン中"

#: cps/web.py:1510 cps/web.py:1560
#, python-format
msgid "%(name)s's Profile"
msgstr "%(name)s のプロフィール"

#: cps/web.py:1526
#, fuzzy
msgid "Success! Profile Updated"
msgstr "プロフィールを更新しました"

#: cps/web.py:1530
msgid "Oops! An account already exists for this Email."
msgstr "このメールアドレスで登録されたアカウントがすでに存在します"

#: cps/services/gmail.py:59
msgid "Found no valid gmail.json file with OAuth information"
msgstr "OAuth情報を含んだ有効なgmail.jsonファイルが見つかりません"

#: cps/tasks/clean.py:29
msgid "Delete temp folder contents"
msgstr ""

#: cps/tasks/convert.py:112
#, python-format
msgid "%(book)s send to E-Reader"
msgstr "%(book)s をE-Readerに送信"

#: cps/tasks/convert.py:177
#, python-format
msgid "Calibre ebook-convert %(tool)s not found"
msgstr "Calibre ebook-convert %(tool)s が見つかりません"

#: cps/tasks/convert.py:211
#, python-format
msgid "%(format)s format not found on disk"
msgstr "%(format)s 形式は存在しません"

#: cps/tasks/convert.py:215
msgid "Ebook converter failed with unknown error"
msgstr "Ebook converter が不明なエラーで失敗しました"

#: cps/tasks/convert.py:234
#, python-format
msgid "Kepubify-converter failed: %(error)s"
msgstr "Kepubify-converter が失敗しました: %(error)s"

#: cps/tasks/convert.py:255
#, python-format
msgid "Converted file not found or more than one file in folder %(folder)s"
msgstr "変換されたファイルが見つからないか、またはフォルダー %(folder)s 内に複数存在します"

#: cps/tasks/convert.py:291 cps/tasks/convert.py:342
#, python-format
msgid "Calibre failed with error: %(error)s"
msgstr "Calibre が失敗しました: %(error)s"

#: cps/tasks/convert.py:319
#, python-format
msgid "Ebook-converter failed: %(error)s"
msgstr "Ebook-converter が失敗しました: %(error)s"

#: cps/tasks/convert.py:347
msgid "Convert"
msgstr "変換"

#: cps/tasks/database.py:26
msgid "Reconnecting Calibre database"
msgstr "Calibre DBと再接続中"

#: cps/tasks/mail.py:283
msgid "E-mail"
msgstr "メール"

#: cps/tasks/metadata_backup.py:34
#, fuzzy
msgid "Backing up Metadata"
msgstr "メタデータを編集"

#: cps/tasks/thumbnail.py:97
#, python-format
msgid "Generated %(count)s cover thumbnails"
msgstr "表紙サムネイルを%(count)s個生成しました"

#: cps/tasks/thumbnail.py:233 cps/tasks/thumbnail.py:448
#: cps/tasks/thumbnail.py:518
msgid "Cover Thumbnails"
msgstr "表紙サムネイル"

#: cps/tasks/thumbnail.py:294
msgid "Generated {0} series thumbnails"
msgstr "シリーズのサムネイルを{0}個生成しました"

#: cps/tasks/thumbnail.py:459
msgid "Clearing cover thumbnail cache"
msgstr "表紙サムネイルのキャッシュを消去中"

#: cps/tasks/upload.py:39 cps/templates/admin.html:20
#: cps/templates/layout.html:83 cps/templates/user_table.html:145
msgid "Upload"
msgstr "アップロード"

#: cps/templates/admin.html:9
msgid "Users"
msgstr "ユーザー"

#: cps/templates/admin.html:13 cps/templates/login.html:9
#: cps/templates/login.html:10 cps/templates/register.html:9
#: cps/templates/user_edit.html:10 cps/templates/user_table.html:134
msgid "Username"
msgstr "ユーザー名"

#: cps/templates/admin.html:14 cps/templates/register.html:14
#: cps/templates/user_edit.html:15 cps/templates/user_table.html:135
msgid "Email"
msgstr "メールアドレス"

#: cps/templates/admin.html:15
msgid "Send to eReader Email"
msgstr "E-Readerメールアドレス"

#: cps/templates/admin.html:17 cps/templates/layout.html:94
#: cps/templates/user_table.html:143
msgid "Admin"
msgstr "管理者"

#: cps/templates/admin.html:18 cps/templates/login.html:13
#: cps/templates/login.html:14 cps/templates/user_edit.html:23
msgid "Password"
msgstr "パスワード"

#: cps/templates/admin.html:22 cps/templates/detail.html:28
#: cps/templates/detail.html:41 cps/templates/shelf.html:8
#: cps/templates/user_table.html:146
msgid "Download"
msgstr "ダウンロード"

#: cps/templates/admin.html:23
msgid "View Books"
msgstr "閲覧"

#: cps/templates/admin.html:24 cps/templates/user_table.html:131
#: cps/templates/user_table.html:148
msgid "Edit"
msgstr "編集"

#: cps/templates/admin.html:25 cps/templates/book_edit.html:17
#: cps/templates/book_table.html:100 cps/templates/modal_dialogs.html:63
#: cps/templates/modal_dialogs.html:116 cps/templates/user_edit.html:67
#: cps/templates/user_table.html:149
msgid "Delete"
msgstr "削除"

#: cps/templates/admin.html:26
msgid "Public Shelf"
msgstr "みんなの本棚"

#: cps/templates/admin.html:55
msgid "Import LDAP Users"
msgstr "LDAPユーザーをインポート"

#: cps/templates/admin.html:62
msgid "Email Server Settings"
msgstr "メールサーバー設定"

#: cps/templates/admin.html:67 cps/templates/email_edit.html:31
msgid "SMTP Hostname"
msgstr "SMTPホスト名"

#: cps/templates/admin.html:71 cps/templates/email_edit.html:35
msgid "SMTP Port"
msgstr "SMTPポート番号"

#: cps/templates/admin.html:75 cps/templates/email_edit.html:39
msgid "Encryption"
msgstr "暗号化"

#: cps/templates/admin.html:79 cps/templates/email_edit.html:47
msgid "SMTP Login"
msgstr "SMTPログイン"

#: cps/templates/admin.html:83 cps/templates/admin.html:94
#: cps/templates/email_edit.html:55
msgid "From Email"
msgstr "Fromメールアドレス"

#: cps/templates/admin.html:90
#, fuzzy
msgid "Email Service"
msgstr "メールサービス"

#: cps/templates/admin.html:91
msgid "Gmail via Oauth2"
msgstr "OAuth2経由のGmail"

#: cps/templates/admin.html:106
msgid "Configuration"
msgstr "設定"

#: cps/templates/admin.html:109
msgid "Calibre Database Directory"
msgstr "Calibre DBのディレクトリ"

#: cps/templates/admin.html:113 cps/templates/config_edit.html:68
msgid "Log Level"
msgstr "ログレベル"

#: cps/templates/admin.html:117
msgid "Port"
msgstr "ポート番号"

#: cps/templates/admin.html:122
msgid "External Port"
msgstr "外部ポート"

#: cps/templates/admin.html:129 cps/templates/config_view_edit.html:28
msgid "Books per Page"
msgstr "1ページに表示する冊数"

#: cps/templates/admin.html:133
msgid "Uploads"
msgstr "アップロード"

#: cps/templates/admin.html:137
msgid "Anonymous Browsing"
msgstr "匿名での閲覧"

#: cps/templates/admin.html:141
msgid "Public Registration"
msgstr "誰でも登録可能"

#: cps/templates/admin.html:145
msgid "Magic Link Remote Login"
msgstr "マジックリンクによるリモートログイン"

#: cps/templates/admin.html:149
msgid "Reverse Proxy Login"
msgstr "リバースプロキシによるログイン"

#: cps/templates/admin.html:154 cps/templates/config_edit.html:172
msgid "Reverse Proxy Header Name"
msgstr "リバースプロキシのヘッダー名"

#: cps/templates/admin.html:159
msgid "Edit Calibre Database Configuration"
msgstr "Calibre DBの設定を編集"

#: cps/templates/admin.html:160
msgid "Edit Basic Configuration"
msgstr "基本設定を編集"

#: cps/templates/admin.html:161
msgid "Edit UI Configuration"
msgstr "UI設定を編集"

#: cps/templates/admin.html:167
msgid "Scheduled Tasks"
msgstr "スケジュールタスク"

#: cps/templates/admin.html:170 cps/templates/schedule_edit.html:12
#: cps/templates/tasks.html:18
msgid "Start Time"
msgstr "タスクを開始する時間"

#: cps/templates/admin.html:174 cps/templates/schedule_edit.html:20
msgid "Maximum Duration"
msgstr "最大タスク継続時間"

#: cps/templates/admin.html:178 cps/templates/schedule_edit.html:29
msgid "Generate Thumbnails"
msgstr "本の表紙サムネイルを生成"

#: cps/templates/admin.html:182
msgid "Generate series cover thumbnails"
msgstr "シリーズの表紙サムネイルを生成"

#: cps/templates/admin.html:186 cps/templates/admin.html:208
#: cps/templates/schedule_edit.html:37
msgid "Reconnect Calibre Database"
msgstr "Calibreライブラリに再接続"

#: cps/templates/admin.html:190 cps/templates/schedule_edit.html:41
msgid "Generate Metadata Backup Files"
msgstr ""

#: cps/templates/admin.html:197
msgid "Refresh Thumbnail Cache"
msgstr "表紙サムネイルのキャッシュを更新"

#: cps/templates/admin.html:203
msgid "Administration"
msgstr "管理"

#: cps/templates/admin.html:204
msgid "Download Debug Package"
msgstr "デバッグパッケージをダウンロード"

#: cps/templates/admin.html:205
msgid "View Logs"
msgstr "ログを表示"

#: cps/templates/admin.html:211
msgid "Restart"
msgstr "再起動"

#: cps/templates/admin.html:212
msgid "Shutdown"
msgstr "シャットダウン"

#: cps/templates/admin.html:221
msgid "Version Information"
msgstr "バージョン情報"

#: cps/templates/admin.html:225
msgid "Version"
msgstr "バージョン"

#: cps/templates/admin.html:226
msgid "Details"
msgstr "詳細"

#: cps/templates/admin.html:232
msgid "Current Version"
msgstr "現在のバージョン"

#: cps/templates/admin.html:239
msgid "Check for Update"
msgstr "アップデートを確認"

#: cps/templates/admin.html:240
msgid "Perform Update"
msgstr "アップデートを実行"

#: cps/templates/admin.html:253
msgid "Are you sure you want to restart?"
msgstr "再起動してもよろしいですか？"

#: cps/templates/admin.html:258 cps/templates/admin.html:272
#: cps/templates/admin.html:292 cps/templates/config_db.html:82
msgid "OK"
msgstr "OK"

#: cps/templates/admin.html:259 cps/templates/admin.html:273
#: cps/templates/book_edit.html:220 cps/templates/book_table.html:127
#: cps/templates/config_db.html:66 cps/templates/config_edit.html:427
#: cps/templates/config_view_edit.html:175 cps/templates/detail.html:350
#: cps/templates/modal_dialogs.html:64 cps/templates/modal_dialogs.html:99
#: cps/templates/modal_dialogs.html:117 cps/templates/modal_dialogs.html:135
#: cps/templates/schedule_edit.html:45 cps/templates/shelf_edit.html:27
#: cps/templates/tasks.html:47 cps/templates/user_edit.html:144
msgid "Cancel"
msgstr "キャンセル"

#: cps/templates/admin.html:271
msgid "Are you sure you want to shutdown?"
msgstr "シャットダウンしてもよろしいですか？"

#: cps/templates/admin.html:283
msgid "Updating, please do not reload this page"
msgstr "アップデート中です。このページをリロードしないでください"

#: cps/templates/author.html:15
msgid "via"
msgstr "経由"

#: cps/templates/author.html:23
msgid "In Library"
msgstr "ライブラリ内"

#: cps/templates/author.html:26 cps/templates/index.html:74
#: cps/templates/search.html:31 cps/templates/shelf.html:20
msgid "Sort according to book date, newest first"
msgstr "追加日が新しい順にソート"

#: cps/templates/author.html:27 cps/templates/index.html:75
#: cps/templates/search.html:32 cps/templates/shelf.html:21
msgid "Sort according to book date, oldest first"
msgstr "追加日が古い順にソート"

#: cps/templates/author.html:28 cps/templates/index.html:76
#: cps/templates/search.html:33 cps/templates/shelf.html:22
msgid "Sort title in alphabetical order"
msgstr "五十音順にソート"

#: cps/templates/author.html:29 cps/templates/index.html:77
#: cps/templates/search.html:34 cps/templates/shelf.html:23
msgid "Sort title in reverse alphabetical order"
msgstr "逆五十音順にソート"

#: cps/templates/author.html:30 cps/templates/index.html:80
#: cps/templates/search.html:37 cps/templates/shelf.html:26
msgid "Sort according to publishing date, newest first"
msgstr "発売日が新しい順にソート"

#: cps/templates/author.html:31 cps/templates/index.html:81
#: cps/templates/search.html:38 cps/templates/shelf.html:27
msgid "Sort according to publishing date, oldest first"
msgstr "発売日が古い順にソート"

#: cps/templates/author.html:56 cps/templates/author.html:113
#: cps/templates/index.html:30 cps/templates/index.html:113
#: cps/templates/search.html:67 cps/templates/shelf.html:58
msgid "reduce"
msgstr "減らす"

#: cps/templates/author.html:97
msgid "More by"
msgstr "同じ著者が書いた本: "

#: cps/templates/basic_detail.html:34 cps/templates/detail.html:158
#: cps/templates/listenmp3.html:62
#, python-format
msgid "Book %(index)s of %(range)s"
msgstr "%(range)s 第%(index)s巻"

#: cps/templates/basic_detail.html:41 cps/templates/book_edit.html:106
#: cps/templates/detail.html:165 cps/templates/listenmp3.html:69
#: cps/templates/user_edit.html:33
msgid "Language"
msgstr "言語"

#: cps/templates/basic_detail.html:61 cps/templates/book_edit.html:102
#: cps/templates/book_edit.html:279 cps/templates/book_edit.html:296
#: cps/templates/detail.html:200 cps/templates/listenmp3.html:102
#: cps/templates/search_form.html:16
msgid "Publisher"
msgstr "出版社"

#: cps/templates/basic_detail.html:70 cps/templates/detail.html:209
#: cps/templates/listenmp3.html:111
msgid "Published"
msgstr "発売日"

#: cps/templates/basic_detail.html:76 cps/templates/detail.html:286
#: cps/templates/listenmp3.html:177
msgid "Description:"
msgstr "詳細:"

#: cps/templates/basic_index.html:7 cps/templates/layout.html:175
msgid "Previous"
msgstr "前"

#: cps/templates/basic_index.html:12 cps/templates/feed.xml:22
#: cps/templates/layout.html:190
msgid "Next"
msgstr "次"

#: cps/templates/basic_index.html:18 cps/templates/search.html:6
msgid "No Results Found"
msgstr "結果が見つかりません"

#: cps/templates/basic_layout.html:17 cps/templates/layout.html:26
#: cps/templates/login.html:30
msgid "Home"
msgstr "ホーム"

#: cps/templates/basic_layout.html:21 cps/templates/layout.html:48
msgid "Search Library"
msgstr "ライブラリ内を検索"

#: cps/templates/basic_layout.html:29 cps/templates/layout.html:73
#: cps/templates/layout.html:99
msgid "Logout"
msgstr "ログアウト"

#: cps/templates/basic_layout.html:35
msgid "Normal Theme"
msgstr ""

#: cps/templates/book_edit.html:11
msgid "Delete Book"
msgstr "本を削除"

#: cps/templates/book_edit.html:14
msgid "Delete formats:"
msgstr "削除する形式:"

#: cps/templates/book_edit.html:25
msgid "Convert book format:"
msgstr "変換する形式:"

#: cps/templates/book_edit.html:30
msgid "Convert from:"
msgstr "変換元:"

#: cps/templates/book_edit.html:32 cps/templates/book_edit.html:39
msgid "select an option"
msgstr "選択肢から選ぶ"

#: cps/templates/book_edit.html:37
msgid "Convert to:"
msgstr "変換先:"

#: cps/templates/book_edit.html:46
msgid "Convert book"
msgstr "本を変換"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
#: cps/templates/layout.html:137
msgid "Uploading..."
msgstr "アップロード中..."

#: cps/templates/book_edit.html:53 cps/templates/book_edit.html:257
#: cps/templates/layout.html:80 cps/templates/layout.html:206
#: cps/templates/modal_dialogs.html:34 cps/templates/user_edit.html:163
msgid "Close"
msgstr "閉じる"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Error"
msgstr "エラー"

#: cps/templates/book_edit.html:53 cps/templates/layout.html:80
msgid "Upload done, processing, please wait..."
msgstr "アップロード完了。現在処理中ですのでお待ち下さい..."

#: cps/templates/book_edit.html:58
msgid "Upload Format"
msgstr "アップロード形式"

#: cps/templates/book_edit.html:71 cps/templates/search_form.html:8
msgid "Book Title"
msgstr "本のタイトル"

#: cps/templates/book_edit.html:78 cps/templates/book_edit.html:277
#: cps/templates/book_edit.html:295 cps/templates/search_form.html:12
msgid "Author"
msgstr "著者"

#: cps/templates/book_edit.html:82 cps/templates/search_form.html:52
msgid "Tags"
msgstr "タグ"

#: cps/templates/book_edit.html:90
msgid "Series ID"
msgstr "巻数"

#: cps/templates/book_edit.html:93
msgid "Published Date"
msgstr "発売日"

#: cps/templates/book_edit.html:110
msgid "Rating"
msgstr "評価"

#: cps/templates/book_edit.html:114 cps/templates/book_edit.html:282
#: cps/templates/book_edit.html:297 cps/templates/search_form.html:154
msgid "Description"
msgstr "詳細"

#: cps/templates/book_edit.html:118
msgid "Identifiers"
msgstr "識別子"

#: cps/templates/book_edit.html:122 cps/templates/book_edit.html:306
msgid "Identifier Type"
msgstr "識別子タイプ"

#: cps/templates/book_edit.html:123 cps/templates/book_edit.html:307
msgid "Identifier Value"
msgstr "識別子の値"

#: cps/templates/book_edit.html:124 cps/templates/book_edit.html:308
#: cps/templates/user_table.html:24
msgid "Remove"
msgstr "削除"

#: cps/templates/book_edit.html:129
msgid "Add Identifier"
msgstr "識別子を追加"

#: cps/templates/book_edit.html:133
msgid "Fetch Cover from URL (JPEG - Image will be downloaded and stored in database)"
msgstr "表紙をURLから取得 (JPEG画像がダウンロードされDBに保存されます)"

#: cps/templates/book_edit.html:137
msgid "Upload Cover from Local Disk"
msgstr "表紙をアップロード"

#: cps/templates/book_edit.html:149 cps/templates/search_form.html:46
#: cps/templates/search_form.html:167
msgid "Yes"
msgstr "既読"

#: cps/templates/book_edit.html:150 cps/templates/search_form.html:47
#: cps/templates/search_form.html:168
msgid "No"
msgstr "未読"

#: cps/templates/book_edit.html:215
msgid "View Book on Save"
msgstr "保存後に本を表示"

#: cps/templates/book_edit.html:218 cps/templates/book_edit.html:236
msgid "Fetch Metadata"
msgstr "メタデータを取得"

#: cps/templates/book_edit.html:219 cps/templates/config_db.html:65
#: cps/templates/config_edit.html:426 cps/templates/config_view_edit.html:174
#: cps/templates/email_edit.html:65 cps/templates/schedule_edit.html:44
#: cps/templates/shelf_edit.html:25 cps/templates/shelf_order.html:41
#: cps/templates/user_edit.html:142
msgid "Save"
msgstr "保存"

#: cps/templates/book_edit.html:239
msgid "Keyword"
msgstr "キーワード"

#: cps/templates/book_edit.html:240
msgid "Search keyword"
msgstr "キーワード検索"

#: cps/templates/book_edit.html:246
msgid "Click the cover to load metadata to the form"
msgstr "カバー画像をクリックしてメタデータをフォームに読み込んでください"

#: cps/templates/book_edit.html:253 cps/templates/book_edit.html:292
msgid "Loading..."
msgstr "読み込み中..."

#: cps/templates/book_edit.html:284 cps/templates/book_edit.html:298
msgid "Source"
msgstr "ソース"

#: cps/templates/book_edit.html:293
msgid "Search error!"
msgstr "検索エラー"

#: cps/templates/book_edit.html:294
msgid "No Result(s) found! Please try another keyword."
msgstr "検索結果が見つかりません。別のキーワードで検索してみてください。"

#: cps/templates/book_table.html:12 cps/templates/book_table.html:69
#: cps/templates/user_table.html:14 cps/templates/user_table.html:77
#: cps/templates/user_table.html:100
msgid "This Field is Required"
msgstr "この項目は必須です"

#: cps/templates/book_table.html:37
msgid "Merge selected books"
msgstr "選択した本を統合"

#: cps/templates/book_table.html:38 cps/templates/user_table.html:124
msgid "Remove Selections"
msgstr "選択した本を削除"

#: cps/templates/book_table.html:41
msgid "Exchange author and title"
msgstr "著者とタイトルを交換"

#: cps/templates/book_table.html:47
msgid "Update Title Sort automatically"
msgstr "タイトルのよみがなを自動で更新"

#: cps/templates/book_table.html:51
msgid "Update Author Sort automatically"
msgstr "著者のよみがなを自動で更新"

#: cps/templates/book_table.html:63 cps/templates/book_table.html:69
msgid "Enter Title"
msgstr "タイトルを入力"

#: cps/templates/book_table.html:63 cps/templates/config_view_edit.html:24
#: cps/templates/shelf_edit.html:8
msgid "Title"
msgstr "タイトル"

#: cps/templates/book_table.html:64
msgid "Enter Title Sort"
msgstr "タイトルのよみがなを入力"

#: cps/templates/book_table.html:64
msgid "Title Sort"
msgstr "タイトルのよみがな"

#: cps/templates/book_table.html:65
msgid "Enter Author Sort"
msgstr "著者名のよみがなを入力"

#: cps/templates/book_table.html:65
msgid "Author Sort"
msgstr "著者名のよみがな"

#: cps/templates/book_table.html:66
msgid "Enter Authors"
msgstr "著者名を入力"

#: cps/templates/book_table.html:67
msgid "Enter Categories"
msgstr "カテゴリ名を入力"

#: cps/templates/book_table.html:68
msgid "Enter Series"
msgstr "シリーズ名を入力"

#: cps/templates/book_table.html:69
msgid "Series Index"
msgstr "巻数"

#: cps/templates/book_table.html:70
msgid "Enter Languages"
msgstr "言語を入力"

#: cps/templates/book_table.html:71
msgid "Publishing Date"
msgstr "発売日"

#: cps/templates/book_table.html:72
msgid "Enter Publishers"
msgstr "出版社を入力"

#: cps/templates/book_table.html:73
msgid "Enter comments"
msgstr "コメントを入力"

#: cps/templates/book_table.html:73
msgid "Comments"
msgstr "コメント"

#: cps/templates/book_table.html:75
msgid "Archive Status"
msgstr "アーカイブ状況"

#: cps/templates/book_table.html:77 cps/templates/search_form.html:42
msgid "Read Status"
msgstr "既読/未読"

#: cps/templates/book_table.html:80 cps/templates/book_table.html:82
#: cps/templates/book_table.html:84 cps/templates/book_table.html:86
#: cps/templates/book_table.html:90 cps/templates/book_table.html:92
#: cps/templates/book_table.html:96
msgid "Enter "
msgstr "入力: "

#: cps/templates/book_table.html:113 cps/templates/modal_dialogs.html:46
#: cps/templates/tasks.html:37
msgid "Are you really sure?"
msgstr "よろしいですか?"

#: cps/templates/book_table.html:117
msgid "Books with Title will be merged from:"
msgstr "このタイトルの本から"

#: cps/templates/book_table.html:121
msgid "Into Book with Title:"
msgstr "このタイトルの本に統合します"

#: cps/templates/book_table.html:126
msgid "Merge"
msgstr "統合"

#: cps/templates/config_db.html:12
msgid "Location of Calibre Database"
msgstr "Calibre DBの場所"

#: cps/templates/config_db.html:21
msgid "Separate Book Files from Library"
msgstr ""

#: cps/templates/config_db.html:34
msgid "Use Google Drive?"
msgstr "Googleドライブを利用しますか？"

#: cps/templates/config_db.html:39
msgid "Authenticate Google Drive"
msgstr "Googleドライブを認証"

#: cps/templates/config_db.html:44
msgid "Google Drive Calibre folder"
msgstr "Googleドライブ上のCalibreフォルダ"

#: cps/templates/config_db.html:52
msgid "Metadata Watch Channel ID"
msgstr "メタデータ監視用チャンネルID"

#: cps/templates/config_db.html:55
msgid "Revoke"
msgstr "取り消す"

#: cps/templates/config_db.html:80
msgid "New db location is invalid, please enter valid path"
msgstr "入力したDBの場所が無効です。有効なパスを入力してください"

#: cps/templates/config_edit.html:18
msgid "Server Configuration"
msgstr "サーバー設定"

#: cps/templates/config_edit.html:25
msgid "Server Port"
msgstr "ポート番号"

#: cps/templates/config_edit.html:28
msgid "SSL certfile location (leave it empty for non-SSL Servers)"
msgstr "SSL証明書の場所 (非SSLサーバーでは空欄にしてください)"

#: cps/templates/config_edit.html:35
msgid "SSL Keyfile location (leave it empty for non-SSL Servers)"
msgstr "SSL鍵ファイルの場所 (非SSLサーバーでは空欄にしてください)"

#: cps/templates/config_edit.html:43
msgid "Update Channel"
msgstr "アップデートチャンネル"

#: cps/templates/config_edit.html:45
msgid "Stable"
msgstr "安定"

#: cps/templates/config_edit.html:46
msgid "Nightly"
msgstr "最新"

#: cps/templates/config_edit.html:50
msgid "Trusted Hosts (Comma Separated)"
msgstr "信頼するホスト名 (カンマ区切り)"

#: cps/templates/config_edit.html:61
msgid "Logfile Configuration"
msgstr "ログファイル設定"

#: cps/templates/config_edit.html:77
msgid "Location and name of logfile (calibre-web.log for no entry)"
msgstr "ログファイル名 (空欄の場合はcalibre-web.log)"

#: cps/templates/config_edit.html:82
msgid "Enable Access Log"
msgstr "アクセスログを保存する"

#: cps/templates/config_edit.html:85
msgid "Location and name of access logfile (access.log for no entry)"
msgstr "アクセスログのファイル名 (空欄の場合はaccess.log)"

#: cps/templates/config_edit.html:96
msgid "Feature Configuration"
msgstr "機能設定"

#: cps/templates/config_edit.html:104
msgid "Convert non-English characters in title and author while saving to disk"
msgstr "ダウンロード時にタイトルと著者名の中にある2バイト文字を変換する"

#: cps/templates/config_edit.html:108
msgid "Embed Metadata to Ebook File on Download/Conversion/e-mail (needs Calibre/Kepubify binaries)"
msgstr ""

#: cps/templates/config_edit.html:112
msgid "Enable Uploads"
msgstr "アップロード機能を有効にする"

#: cps/templates/config_edit.html:112
msgid "(Please ensure that users also have upload permissions)"
msgstr "(ユーザーにアップロード権限を与えることも忘れないでください)"

#: cps/templates/config_edit.html:116
msgid "Allowed Upload Fileformats"
msgstr "アップロードを許可するファイル形式"

#: cps/templates/config_edit.html:122
msgid "Enable Anonymous Browsing"
msgstr "匿名での閲覧を許可"

#: cps/templates/config_edit.html:126
msgid "Enable Public Registration"
msgstr "誰でも登録可能にする"

#: cps/templates/config_edit.html:131
msgid "Use Email as Username"
msgstr "メールアドレスをユーザー名として使う"

#: cps/templates/config_edit.html:136
msgid "Enable Magic Link Remote Login"
msgstr "マジックリンクによるリモートログインを有効にする"

#: cps/templates/config_edit.html:141
msgid "Enable Kobo sync"
msgstr "Koboの同期を有効にする"

#: cps/templates/config_edit.html:146
msgid "Proxy unknown requests to Kobo Store"
msgstr "Koboストアへの不明なリクエストをプロキシ"

#: cps/templates/config_edit.html:149
msgid "Server External Port (for port forwarded API calls)"
msgstr "サーバーの外部ポート番号 (APIの呼び出しをポートフォワーディングするため)"

#: cps/templates/config_edit.html:157
msgid "Use Goodreads"
msgstr "Goodreadsを利用"

#: cps/templates/config_edit.html:161
msgid "Goodreads API Key"
msgstr "GoodreadsのAPIキー"

#: cps/templates/config_edit.html:168
msgid "Allow Reverse Proxy Authentication"
msgstr "リバースプロキシの認証を許可"

#: cps/templates/config_edit.html:179
msgid "Login type"
msgstr "ログインの種類"

#: cps/templates/config_edit.html:181
msgid "Use Standard Authentication"
msgstr "通常の認証を利用"

#: cps/templates/config_edit.html:183
msgid "Use LDAP Authentication"
msgstr "LDAP認証を利用"

#: cps/templates/config_edit.html:186
msgid "Use OAuth"
msgstr "OAuthを利用"

#: cps/templates/config_edit.html:193
msgid "LDAP Server Host Name or IP Address"
msgstr "LDAPサーバーのホスト名またはIPアドレス"

#: cps/templates/config_edit.html:197
msgid "LDAP Server Port"
msgstr "LDAPサーバーのポート番号"

#: cps/templates/config_edit.html:201
msgid "LDAP Encryption"
msgstr "LDAPの暗号化方式"

#: cps/templates/config_edit.html:204
msgid "TLS"
msgstr "TLS"

#: cps/templates/config_edit.html:205
msgid "SSL"
msgstr "SSL"

#: cps/templates/config_edit.html:209
msgid "LDAP CACertificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAPのCA証明書のパス (クライアント証明書による認証の場合のみ必要)"

#: cps/templates/config_edit.html:216
msgid "LDAP Certificate Path (Only needed for Client Certificate Authentication)"
msgstr "LDAP証明書のパス (クライアント証明書による認証の場合のみ必要)"

#: cps/templates/config_edit.html:223
msgid "LDAP Keyfile Path (Only needed for Client Certificate Authentication)"
msgstr "LDAPのキーファイルのパス (クライアント証明書による認証の場合のみ必要)"

#: cps/templates/config_edit.html:232
msgid "LDAP Authentication"
msgstr "LDAP認証"

#: cps/templates/config_edit.html:234
msgid "Anonymous"
msgstr "匿名"

#: cps/templates/config_edit.html:235
msgid "Unauthenticated"
msgstr "認証失敗"

#: cps/templates/config_edit.html:236
msgid "Simple"
msgstr "簡単"

#: cps/templates/config_edit.html:241
msgid "LDAP Administrator Username"
msgstr "LDAP管理者のユーザー名"

#: cps/templates/config_edit.html:247
msgid "LDAP Administrator Password"
msgstr "LDAP管理者のパスワード"

#: cps/templates/config_edit.html:252
msgid "LDAP Distinguished Name (DN)"
msgstr "LDAPの識別名 (DN)"

#: cps/templates/config_edit.html:256
msgid "LDAP User Object Filter"
msgstr "LDAPユーザーフィルタ"

#: cps/templates/config_edit.html:261
msgid "LDAP Server is OpenLDAP?"
msgstr "LDAPサーバーはOpenLDAPですか？"

#: cps/templates/config_edit.html:263
msgid "Following Settings are Needed For User Import"
msgstr "以下の設定はユーザーのインポートのために必要です"

#: cps/templates/config_edit.html:265
msgid "LDAP Group Object Filter"
msgstr "LDAPグループフィルタ"

#: cps/templates/config_edit.html:269
msgid "LDAP Group Name"
msgstr "LDAPグループ名"

#: cps/templates/config_edit.html:273
msgid "LDAP Group Members Field"
msgstr "LDAPグループメンバーフィールド"

#: cps/templates/config_edit.html:277
msgid "LDAP Member User Filter Detection"
msgstr "LDAPメンバーユーザーフィルタ検出"

#: cps/templates/config_edit.html:279
msgid "Autodetect"
msgstr "自動検出"

#: cps/templates/config_edit.html:280
msgid "Custom Filter"
msgstr "カスタムフィルタ"

#: cps/templates/config_edit.html:285
msgid "LDAP Member User Filter"
msgstr "LDAPメンバーユーザーフィルタ"

#: cps/templates/config_edit.html:296
#, python-format
msgid "Obtain %(provider)s OAuth Credential"
msgstr "%(provider)s OAuth 認証情報を入手"

#: cps/templates/config_edit.html:299
#, python-format
msgid "%(provider)s OAuth Client Id"
msgstr "%(provider)s OAuth クライアントID"

#: cps/templates/config_edit.html:303
#, python-format
msgid "%(provider)s OAuth Client Secret"
msgstr "%(provider)s OAuth クライアントシークレット"

#: cps/templates/config_edit.html:319
msgid "External binaries"
msgstr "外部バイナリ"

#: cps/templates/config_edit.html:325
#, fuzzy
msgid "Path to Calibre Binaries"
msgstr "Calibre E-Book Converterのパス"

#: cps/templates/config_edit.html:333
msgid "Calibre E-Book Converter Settings"
msgstr "Calibre E-Book Converterの設定"

#: cps/templates/config_edit.html:336
msgid "Path to Kepubify E-Book Converter"
msgstr "Kepubify E-Book Converterのパス"

#: cps/templates/config_edit.html:344
#, fuzzy
msgid "Location of Unrar binary"
msgstr "UnRarバイナリのパス"

#: cps/templates/config_edit.html:360
#, fuzzy
msgid "Security Settings"
msgstr "OAuth設定"

#: cps/templates/config_edit.html:368
msgid "Limit failed login attempts"
msgstr ""

#: cps/templates/config_edit.html:372
msgid "Configure Backend for Limiter"
msgstr ""

#: cps/templates/config_edit.html:376
msgid "Options for Limiter Backend"
msgstr ""

#: cps/templates/config_edit.html:382
msgid "Check if file extensions matches file content on upload"
msgstr ""

#: cps/templates/config_edit.html:385
msgid "Session protection"
msgstr ""

#: cps/templates/config_edit.html:387
msgid "Basic"
msgstr ""

#: cps/templates/config_edit.html:388
msgid "Strong"
msgstr ""

#: cps/templates/config_edit.html:393
#, fuzzy
msgid "User Password policy"
msgstr "パスワードをリセット"

#: cps/templates/config_edit.html:397
msgid "Minimum password length"
msgstr ""

#: cps/templates/config_edit.html:402
msgid "Enforce number"
msgstr ""

#: cps/templates/config_edit.html:406
msgid "Enforce lowercase characters"
msgstr ""

#: cps/templates/config_edit.html:410
msgid "Enforce uppercase characters"
msgstr ""

#: cps/templates/config_edit.html:414
msgid "Enforce characters (needed For Chinese/Japanese/Korean Characters)"
msgstr ""

#: cps/templates/config_edit.html:418
msgid "Enforce special characters"
msgstr ""

#: cps/templates/config_view_edit.html:17
msgid "View Configuration"
msgstr "表示設定"

#: cps/templates/config_view_edit.html:32
msgid "No. of Random Books to Display"
msgstr "ランダムに表示する本の冊数"

#: cps/templates/config_view_edit.html:36
msgid "No. of Authors to Display Before Hiding (0=Disable Hiding)"
msgstr "非表示にする前に表示する著者数 (0=非表示にしない)"

#: cps/templates/config_view_edit.html:40 cps/templates/readcbr.html:101
msgid "Theme"
msgstr "テーマ"

#: cps/templates/config_view_edit.html:42
msgid "Standard Theme"
msgstr "通常テーマ"

#: cps/templates/config_view_edit.html:43
msgid "caliBlur! Dark Theme"
msgstr "caliBlur! ダークテーマ"

#: cps/templates/config_view_edit.html:47
msgid "Regular Expression for Ignoring Columns"
msgstr "無視するカラムの正規表現"

#: cps/templates/config_view_edit.html:51
msgid "Link Read/Unread Status to Calibre Column"
msgstr "既読/未読をリンクするCalibreのカラム"

#: cps/templates/config_view_edit.html:60
msgid "View Restrictions based on Calibre column"
msgstr "表示制限をリンクするCalibreのカラム"

#: cps/templates/config_view_edit.html:69
msgid "Regular Expression for Title Sorting"
msgstr "タイトルをソートするための正規表現"

#: cps/templates/config_view_edit.html:80
msgid "Default Settings for New Users"
msgstr "新規ユーザーのデフォルト設定"

#: cps/templates/config_view_edit.html:88 cps/templates/user_edit.html:96
msgid "Admin User"
msgstr "管理者ユーザー"

#: cps/templates/config_view_edit.html:92 cps/templates/user_edit.html:101
msgid "Allow Downloads"
msgstr "ダウンロードを許可"

#: cps/templates/config_view_edit.html:96 cps/templates/user_edit.html:105
msgid "Allow eBook Viewer"
msgstr "本のビューアを許可"

#: cps/templates/config_view_edit.html:101 cps/templates/user_edit.html:110
msgid "Allow Uploads"
msgstr "アップロードを許可"

#: cps/templates/config_view_edit.html:106 cps/templates/user_edit.html:115
msgid "Allow Edit"
msgstr "編集を許可"

#: cps/templates/config_view_edit.html:111 cps/templates/user_edit.html:120
msgid "Allow Delete Books"
msgstr "本の削除を許可"

#: cps/templates/config_view_edit.html:116 cps/templates/user_edit.html:126
msgid "Allow Changing Password"
msgstr "パスワード変更を許可"

#: cps/templates/config_view_edit.html:120 cps/templates/user_edit.html:130
msgid "Allow Editing Public Shelves"
msgstr "みんなの本棚の編集を許可"

#: cps/templates/config_view_edit.html:123
msgid "Default Language"
msgstr "言語設定のデフォルト"

#: cps/templates/config_view_edit.html:131
msgid "Default Visible Language of Books"
msgstr "表示する本の言語設定のデフォルト"

#: cps/templates/config_view_edit.html:147
msgid "Default Visibilities for New Users"
msgstr "新規ユーザーのデフォルト表示設定"

#: cps/templates/config_view_edit.html:163 cps/templates/user_edit.html:84
#: cps/templates/user_table.html:154
msgid "Show Random Books in Detail View"
msgstr "詳細画面でランダムに本を表示"

#: cps/templates/config_view_edit.html:166 cps/templates/user_edit.html:87
msgid "Add Allowed/Denied Tags"
msgstr "許可/拒否タグを追加"

#: cps/templates/config_view_edit.html:167
msgid "Add Allowed/Denied custom column values"
msgstr "許可/拒否カスタムカラムを追加"

#: cps/templates/detail.html:85 cps/templates/detail.html:99
msgid "Read in Browser"
msgstr "ブラウザで読む"

#: cps/templates/detail.html:108 cps/templates/detail.html:128
msgid "Listen in Browser"
msgstr "ブラウザで聞く"

#: cps/templates/detail.html:259 cps/templates/listenmp3.html:158
msgid "Mark As Unread"
msgstr "未読に設定"

#: cps/templates/detail.html:260 cps/templates/listenmp3.html:158
msgid "Mark As Read"
msgstr "既読に設定"

#: cps/templates/detail.html:262
#, fuzzy
msgid "Mark Book as Read or Unread"
msgstr "未読に設定"

#: cps/templates/detail.html:262 cps/templates/listenmp3.html:159
msgid "Read"
msgstr "既読"

#: cps/templates/detail.html:272 cps/templates/listenmp3.html:166
msgid "Restore from archive"
msgstr "アーカイブから復元"

#: cps/templates/detail.html:273 cps/templates/listenmp3.html:166
msgid "Add to archive"
msgstr "アーカイブに追加"

#: cps/templates/detail.html:275
msgid "Mark Book as archived or not, to hide it in Calibre-Web and delete it from Kobo Reader"
msgstr ""

#: cps/templates/detail.html:275
#, fuzzy
msgid "Archive"
msgstr "アーカイブ済み"

#: cps/templates/detail.html:301 cps/templates/listenmp3.html:190
#: cps/templates/search.html:16
msgid "Add to shelf"
msgstr "本棚に追加"

#: cps/templates/detail.html:313 cps/templates/detail.html:332
#: cps/templates/feed.xml:81 cps/templates/layout.html:157
#: cps/templates/listenmp3.html:201 cps/templates/listenmp3.html:218
#: cps/templates/search.html:22
msgid "(Public)"
msgstr "(公開)"

#: cps/templates/detail.html:348
msgid "Edit Metadata"
msgstr "メタデータを編集"

#: cps/templates/email_edit.html:13
msgid "Email Account Type"
msgstr "サーバーの種類を選択"

#: cps/templates/email_edit.html:15
#, fuzzy
msgid "Standard Email Account"
msgstr "標準のメールアカウントを使用"

#: cps/templates/email_edit.html:16
#, fuzzy
msgid "Gmail Account"
msgstr "サーバーの種類を選択"

#: cps/templates/email_edit.html:22
msgid "Setup Gmail Account"
msgstr ""

#: cps/templates/email_edit.html:24
msgid "Revoke Gmail Access"
msgstr "Gmailへのアクセスを失効"

#: cps/templates/email_edit.html:42
msgid "STARTTLS"
msgstr "STARTTLS"

#: cps/templates/email_edit.html:43
msgid "SSL/TLS"
msgstr "SSL/TLS"

#: cps/templates/email_edit.html:51
msgid "SMTP Password"
msgstr "SMTPのパスワード"

#: cps/templates/email_edit.html:58
msgid "Attachment Size Limit"
msgstr "添付ファイルのサイズ制限"

#: cps/templates/email_edit.html:66
#, fuzzy
msgid "Save and Send Test Email"
msgstr "保存してテストメールを送信"

#: cps/templates/email_edit.html:70 cps/templates/layout.html:26
#: cps/templates/shelf_order.html:42 cps/templates/user_table.html:174
msgid "Back"
msgstr "戻る"

#: cps/templates/email_edit.html:74
msgid "Allowed Domains (Whitelist)"
msgstr "許可するドメイン名 (ホワイトリスト)"

#: cps/templates/email_edit.html:78 cps/templates/email_edit.html:105
msgid "Add Domain"
msgstr "ドメインを追加"

#: cps/templates/email_edit.html:81 cps/templates/email_edit.html:108
#: cps/templates/user_table.html:27
msgid "Add"
msgstr "追加"

#: cps/templates/email_edit.html:86 cps/templates/email_edit.html:96
msgid "Enter domainname"
msgstr "ドメイン名を入力"

#: cps/templates/email_edit.html:92
msgid "Denied Domains (Blacklist)"
msgstr "拒否するドメイン名 (ブラックリスト)"

#: cps/templates/generate_kobo_auth_url.html:6
msgid "Open the .kobo/Kobo/Kobo eReader.conf file in a text editor and add (or edit):"
msgstr ".kobo/Kobo/Kobo eReader.confファイルをテキストエディタで開いて追加(編集)してください:"

#: cps/templates/generate_kobo_auth_url.html:11
msgid "Kobo Token:"
msgstr "Koboトークン:"

#: cps/templates/grid.html:21
msgid "List"
msgstr "一覧"

#: cps/templates/http_error.html:34
msgid "Calibre-Web Instance is unconfigured, please contact your administrator"
msgstr "Calibre-Webインスタンスが未設定です。管理者に連絡してください"

#: cps/templates/http_error.html:44
msgid "Create Issue"
msgstr "Issueを作成"

#: cps/templates/http_error.html:52
#, fuzzy
msgid "Return to Database config"
msgstr "DB設定"

#: cps/templates/http_error.html:54
msgid "Return to Home"
msgstr "ホームに戻る"

#: cps/templates/http_error.html:57
msgid "Logout User"
msgstr "ユーザーをログアウト"

#: cps/templates/index.html:71
msgid "Sort ascending according to download count"
msgstr "ダウンロード数が少ない順にソート"

#: cps/templates/index.html:72
msgid "Sort descending according to download count"
msgstr "ダウンロード数が多い順にソート"

#: cps/templates/index.html:78 cps/templates/search.html:35
#: cps/templates/shelf.html:24
msgid "Sort authors in alphabetical order"
msgstr "著者名を五十音順にソート"

#: cps/templates/index.html:79 cps/templates/search.html:36
#: cps/templates/shelf.html:25
msgid "Sort authors in reverse alphabetical order"
msgstr "著者名を逆五十音順にソート"

#: cps/templates/index.html:83
msgid "Sort ascending according to series index"
msgstr "巻数が小さい順にソート"

#: cps/templates/index.html:84
msgid "Sort descending according to series index"
msgstr "巻数が大きい順にソート"

#: cps/templates/index.xml:7
msgid "Start"
msgstr "開始"

#: cps/templates/index.xml:19
msgid "Alphabetical Books"
msgstr "五十音順の本"

#: cps/templates/index.xml:23
msgid "Books sorted alphabetically"
msgstr "五十音順にソートされた本"

#: cps/templates/index.xml:31
msgid "Popular publications from this catalog based on Downloads."
msgstr "ダウンロード数に基づいた、この出版社が出している有名な本"

#: cps/templates/index.xml:40
msgid "Popular publications from this catalog based on Rating."
msgstr "評価に基づいた、この出版社が出している有名な本"

#: cps/templates/index.xml:45
msgid "Recently added Books"
msgstr "最近追加された本"

#: cps/templates/index.xml:49
msgid "The latest Books"
msgstr "最新の本"

#: cps/templates/index.xml:54
msgid "Random Books"
msgstr "ランダム"

#: cps/templates/index.xml:83
msgid "Books ordered by Author"
msgstr "著者名順"

#: cps/templates/index.xml:92
msgid "Books ordered by publisher"
msgstr "出版社順"

#: cps/templates/index.xml:101
msgid "Books ordered by category"
msgstr "カテゴリ順"

#: cps/templates/index.xml:110
msgid "Books ordered by series"
msgstr "シリーズ順"

#: cps/templates/index.xml:119
msgid "Books ordered by Languages"
msgstr "言語順"

#: cps/templates/index.xml:128
msgid "Books ordered by Rating"
msgstr "評価順"

#: cps/templates/index.xml:137
msgid "Books ordered by file formats"
msgstr "ファイル形式順"

#: cps/templates/index.xml:142 cps/templates/layout.html:155
#: cps/templates/search_form.html:88
msgid "Shelves"
msgstr "本棚"

#: cps/templates/index.xml:146
msgid "Books organized in shelves"
msgstr "本棚に整理された本"

#: cps/templates/layout.html:32
msgid "Toggle Navigation"
msgstr "ナビゲーションを実行"

#: cps/templates/layout.html:59
#, fuzzy
msgid "Simple Theme"
msgstr "簡単"

#: cps/templates/layout.html:67 cps/templates/layout.html:97
msgid "Account"
msgstr "アカウント"

#: cps/templates/layout.html:94 cps/templates/read.html:78
#: cps/templates/readcbr.html:70 cps/templates/readcbr.html:96
msgid "Settings"
msgstr "設定"

#: cps/templates/layout.html:138
msgid "Please do not refresh the page"
msgstr "ページを更新しないでください"

#: cps/templates/layout.html:148
msgid "Browse"
msgstr "閲覧"

#: cps/templates/layout.html:161 cps/templates/stats.html:3
msgid "About"
msgstr "このサイトについて"

#: cps/templates/layout.html:202
msgid "Book Details"
msgstr "本の詳細"

#: cps/templates/list.html:22
msgid "Grid"
msgstr "表"

#: cps/templates/listenmp3.html:167
msgid "Archived"
msgstr "アーカイブ済み"

#: cps/templates/login.html:18
msgid "Remember Me"
msgstr "アカウント情報を記憶する"

#: cps/templates/login.html:23
msgid "Forgot Password?"
msgstr "パスワードを忘れた場合"

#: cps/templates/login.html:34
msgid "Log in with Magic Link"
msgstr "マジックリンクでログイン"

#: cps/templates/logviewer.html:6
msgid "Show Calibre-Web Log: "
msgstr "Calibre-Webのログを表示: "

#: cps/templates/logviewer.html:8
msgid "Calibre-Web Log: "
msgstr "Calibre-Webのログ: "

#: cps/templates/logviewer.html:8
msgid "Stream output, can't be displayed"
msgstr "ストリームの出力を表示できません"

#: cps/templates/logviewer.html:12
msgid "Show Access Log: "
msgstr "アクセスログを表示: "

#: cps/templates/logviewer.html:18
msgid "Download Calibre-Web Log"
msgstr "Calibre-Webのログをダウンロード"

#: cps/templates/logviewer.html:21
msgid "Download Access Log"
msgstr "アクセスログをダウンロード"

#: cps/templates/modal_dialogs.html:6
msgid "Select Allowed/Denied Tags"
msgstr "許可/拒否タグを選択"

#: cps/templates/modal_dialogs.html:7
msgid "Select Allowed/Denied Custom Column Values"
msgstr "許可/拒否カスタムカラムを選択"

#: cps/templates/modal_dialogs.html:8
msgid "Select Allowed/Denied Tags of User"
msgstr "ユーザーの許可/拒否タグを選択"

#: cps/templates/modal_dialogs.html:9
msgid "Select Allowed/Denied Custom Column Values of User"
msgstr "ユーザーの許可/拒否カスタムカラムを選択"

#: cps/templates/modal_dialogs.html:15
msgid "Enter Tag"
msgstr "タグを入力"

#: cps/templates/modal_dialogs.html:24
msgid "Add View Restriction"
msgstr "表示制限を追加"

#: cps/templates/modal_dialogs.html:50
msgid "This book format will be permanently erased from database"
msgstr "このファイル形式はDBから完全に削除されます"

#: cps/templates/modal_dialogs.html:51
msgid "This book will be permanently erased from database"
msgstr "この本はDBと"

#: cps/templates/modal_dialogs.html:52
msgid "and hard disk"
msgstr "ローカルから完全に削除されます"

#: cps/templates/modal_dialogs.html:56
msgid "Important Kobo Note: deleted books will remain on any paired Kobo device."
msgstr "Koboに関する重要な注意: 削除された本はどの連携されたKobo端末にも残ります。"

#: cps/templates/modal_dialogs.html:57
msgid "Books must first be archived and the device synced before a book can safely be deleted."
msgstr "本を安全に削除するためには、初めに本をアーカイブした上で端末と同期する必要があります"

#: cps/templates/modal_dialogs.html:76
msgid "Choose File Location"
msgstr "ファイルの場所を選択"

#: cps/templates/modal_dialogs.html:82
msgid "type"
msgstr "種類"

#: cps/templates/modal_dialogs.html:83
msgid "name"
msgstr "名前"

#: cps/templates/modal_dialogs.html:84
msgid "size"
msgstr "サイズ"

#: cps/templates/modal_dialogs.html:90
msgid "Parent Directory"
msgstr "親ディレクトリ"

#: cps/templates/modal_dialogs.html:98
msgid "Select"
msgstr "選択"

#: cps/templates/modal_dialogs.html:134 cps/templates/tasks.html:46
msgid "Ok"
msgstr "OK"

#: cps/templates/osd.xml:5
msgid "Calibre-Web eBook Catalog"
msgstr "Calibre-WebのeBookカタログ"

#: cps/templates/read.html:7
msgid "epub Reader"
msgstr "EPUBリーダー"

#: cps/templates/read.html:80
#, fuzzy
msgid "Choose a theme below:"
msgstr "ユーザー名を入力してください"

#: cps/templates/read.html:84 cps/templates/readcbr.html:104
msgid "Light"
msgstr "ライト"

#: cps/templates/read.html:86 cps/templates/readcbr.html:105
msgid "Dark"
msgstr "ダーク"

#: cps/templates/read.html:88
msgid "Sepia"
msgstr "セピア"

#: cps/templates/read.html:90
msgid "Black"
msgstr "ブラック"

#: cps/templates/read.html:95
msgid "Reflow text when sidebars are open."
msgstr "サイドバーが開いているとき、テキストを再度流し込みます。"

#: cps/templates/read.html:100
msgid "Font Sizes"
msgstr ""

#: cps/templates/read.html:105
msgid "Font"
msgstr ""

#: cps/templates/read.html:106
#, fuzzy
msgid "Default"
msgstr "削除"

#: cps/templates/read.html:107
msgid "Yahei"
msgstr ""

#: cps/templates/read.html:108
msgid "SimSun"
msgstr ""

#: cps/templates/read.html:109
#, fuzzy
msgid "KaiTi"
msgstr "待機中"

#: cps/templates/read.html:110
#, fuzzy
msgid "Arial"
msgstr "垂直方向"

#: cps/templates/read.html:113
#, fuzzy
msgid "Spread"
msgstr "既読"

#: cps/templates/read.html:114
msgid "Two columns"
msgstr ""

#: cps/templates/read.html:115
#, fuzzy
msgid "One column"
msgstr "無効な読み取り列"

#: cps/templates/readcbr.html:8
msgid "Comic Reader"
msgstr "コミックリーダー"

#: cps/templates/readcbr.html:75
msgid "Keyboard Shortcuts"
msgstr "キーボードショートカット"

#: cps/templates/readcbr.html:78
msgid "Previous Page"
msgstr "前のページ"

#: cps/templates/readcbr.html:79 cps/templates/readcbr.html:159
msgid "Next Page"
msgstr "次のページ"

#: cps/templates/readcbr.html:80
msgid "Single Page Display"
msgstr ""

#: cps/templates/readcbr.html:81
msgid "Long Strip Display"
msgstr ""

#: cps/templates/readcbr.html:82
msgid "Scale to Best"
msgstr "最適なサイズにする"

#: cps/templates/readcbr.html:83
msgid "Scale to Width"
msgstr "横に合わせる"

#: cps/templates/readcbr.html:84
msgid "Scale to Height"
msgstr "縦に合わせる"

#: cps/templates/readcbr.html:85
msgid "Scale to Native"
msgstr "オリジナルのサイズにする"

#: cps/templates/readcbr.html:86
msgid "Rotate Right"
msgstr "右に回転する"

#: cps/templates/readcbr.html:87
msgid "Rotate Left"
msgstr "左に回転する"

#: cps/templates/readcbr.html:88
msgid "Flip Image"
msgstr "画像を反転する"

#: cps/templates/readcbr.html:110
msgid "Display"
msgstr ""

#: cps/templates/readcbr.html:113
#, fuzzy
msgid "Single Page"
msgstr "管理者ページ"

#: cps/templates/readcbr.html:114
msgid "Long Strip"
msgstr ""

#: cps/templates/readcbr.html:119
msgid "Scale"
msgstr "スケール"

#: cps/templates/readcbr.html:122
msgid "Best"
msgstr "最適"

#: cps/templates/readcbr.html:123
msgid "Width"
msgstr "横に合わせる"

#: cps/templates/readcbr.html:124
msgid "Height"
msgstr "縦に合わせる"

#: cps/templates/readcbr.html:125
msgid "Native"
msgstr "オリジナル"

#: cps/templates/readcbr.html:130
msgid "Rotate"
msgstr "回転"

#: cps/templates/readcbr.html:141
msgid "Flip"
msgstr "反転"

#: cps/templates/readcbr.html:144
msgid "Horizontal"
msgstr "水平方向"

#: cps/templates/readcbr.html:145
msgid "Vertical"
msgstr "垂直方向"

#: cps/templates/readcbr.html:150
msgid "Direction"
msgstr "読む方向"

#: cps/templates/readcbr.html:153
msgid "Left to Right"
msgstr "左から右"

#: cps/templates/readcbr.html:154
msgid "Right to Left"
msgstr "右から左"

#: cps/templates/readcbr.html:162
msgid "Reset to Top"
msgstr ""

#: cps/templates/readcbr.html:163
msgid "Remember Position"
msgstr ""

#: cps/templates/readcbr.html:168
msgid "Scrollbar"
msgstr "スクロールバー"

#: cps/templates/readcbr.html:171
msgid "Show"
msgstr "表示"

#: cps/templates/readcbr.html:172
msgid "Hide"
msgstr "非表示"

#: cps/templates/readdjvu.html:5
msgid "DJVU Reader"
msgstr "DJVUリーダー"

#: cps/templates/readpdf.html:31
msgid "PDF Reader"
msgstr "PDFリーダー"

#: cps/templates/readtxt.html:6
msgid "txt Reader"
msgstr "テキストリーダー"

#: cps/templates/register.html:4
msgid "Register New Account"
msgstr "新規アカウントを登録"

#: cps/templates/register.html:10
msgid "Choose a username"
msgstr "ユーザー名を入力してください"

#: cps/templates/register.html:15
msgid "Your Email"
msgstr "メールアドレス"

#: cps/templates/remote_login.html:5
msgid "Magic Link - Authorise New Device"
msgstr "マジックリンク - 新規端末を認証する"

#: cps/templates/remote_login.html:7
msgid "On another device, login and visit:"
msgstr "他の端末でログインしてアクセスしてください:"

#: cps/templates/remote_login.html:11
msgid "Once verified, you will automatically be logged in on this device."
msgstr "一度承認されると、自動的にこの端末でログインされます。"

#: cps/templates/remote_login.html:14
msgid "This verification link will expire in 10 minutes."
msgstr "この確認リンクの有効期限は10分です。"

#: cps/templates/schedule_edit.html:33
msgid "Generate Series Cover Thumbnails"
msgstr "シリーズの表紙サムネイルを生成"

#: cps/templates/search.html:7
msgid "Search Term:"
msgstr "検索する単語:"

#: cps/templates/search.html:9
msgid "Results for:"
msgstr "結果:"

#: cps/templates/search_form.html:21
msgid "Published Date From"
msgstr "発売日(〜から)"

#: cps/templates/search_form.html:31
msgid "Published Date To"
msgstr "発売日(〜まで)"

#: cps/templates/search_form.html:44 cps/templates/search_form.html:165
msgid "Any"
msgstr ""

#: cps/templates/search_form.html:45 cps/templates/search_form.html:166
msgid "Empty"
msgstr ""

#: cps/templates/search_form.html:60
msgid "Exclude Tags"
msgstr "除外するタグ"

#: cps/templates/search_form.html:78
msgid "Exclude Series"
msgstr "除外するシリーズ"

#: cps/templates/search_form.html:96
msgid "Exclude Shelves"
msgstr "除外する本棚"

#: cps/templates/search_form.html:116
msgid "Exclude Languages"
msgstr "除外する言語"

#: cps/templates/search_form.html:127
msgid "Extensions"
msgstr "拡張子"

#: cps/templates/search_form.html:135
msgid "Exclude Extensions"
msgstr "除外する拡張子"

#: cps/templates/search_form.html:145
msgid "Rating Above"
msgstr "評価(〜以上)"

#: cps/templates/search_form.html:149
msgid "Rating Below"
msgstr "評価(〜以下)"

#: cps/templates/search_form.html:175 cps/templates/search_form.html:187
#: cps/templates/search_form.html:201
msgid "From:"
msgstr "〜から:"

#: cps/templates/search_form.html:179 cps/templates/search_form.html:191
#: cps/templates/search_form.html:211
msgid "To:"
msgstr "〜まで:"

#: cps/templates/shelf.html:13
msgid "Delete this Shelf"
msgstr "この本棚を削除"

#: cps/templates/shelf.html:14
msgid "Edit Shelf Properties"
msgstr "本棚のプロパティを編集"

#: cps/templates/shelf.html:17
msgid "Arrange books manually"
msgstr "手動で本を並び替える"

#: cps/templates/shelf.html:18
msgid "Disable Change order"
msgstr "並び順の変更を無効にする"

#: cps/templates/shelf.html:18
msgid "Enable Change order"
msgstr "並び順の変更を有効にする"

#: cps/templates/shelf.html:28
#, fuzzy
msgid "Sort according to book added to shelf, newest first"
msgstr "追加日が新しい順にソート"

#: cps/templates/shelf.html:29
#, fuzzy
msgid "Sort according to book added to shelf, oldest first"
msgstr "追加日が古い順にソート"

#: cps/templates/shelf_edit.html:14
msgid "Share with Everyone"
msgstr "みんなと共有"

#: cps/templates/shelf_edit.html:21
msgid "Sync this shelf with Kobo device"
msgstr "この本棚をKobo端末と同期"

#: cps/templates/shelf_order.html:5
msgid "Drag to Rearrange Order"
msgstr "ドラッグして並び順を変更"

#: cps/templates/shelf_order.html:33
msgid "Hidden Book"
msgstr "非表示の本"

#: cps/templates/stats.html:7
msgid "Library Statistics"
msgstr "ライブラリの統計"

#: cps/templates/stats.html:12
msgid "Books in this Library"
msgstr "このライブラリ内の本の冊数"

#: cps/templates/stats.html:16
msgid "Authors in this Library"
msgstr "このライブラリ内の著者数"

#: cps/templates/stats.html:20
msgid "Categories in this Library"
msgstr "このライブラリ内のカテゴリ数"

#: cps/templates/stats.html:24
msgid "Series in this Library"
msgstr "このライブラリ内のシリーズ数"

#: cps/templates/stats.html:29
msgid "System Statistics"
msgstr "システムの統計"

#: cps/templates/stats.html:33
msgid "Program"
msgstr "プログラム"

#: cps/templates/stats.html:34
msgid "Installed Version"
msgstr "インストールされたバージョン"

#: cps/templates/tasks.html:12
msgid "User"
msgstr "ユーザー"

#: cps/templates/tasks.html:14
msgid "Task"
msgstr "タスク"

#: cps/templates/tasks.html:15
msgid "Status"
msgstr "ステータス"

#: cps/templates/tasks.html:16
msgid "Progress"
msgstr "進捗"

#: cps/templates/tasks.html:17
msgid "Run Time"
msgstr "稼働時間"

#: cps/templates/tasks.html:19
#, fuzzy
msgid "Message"
msgstr "統合"

#: cps/templates/tasks.html:21
msgid "Actions"
msgstr "アクション"

#: cps/templates/tasks.html:41
msgid "This task will be cancelled. Any progress made by this task will be saved."
msgstr "このタスクはキャンセルされます。このタスクによる進捗はすべて保存されます。"

#: cps/templates/tasks.html:42
msgid "If this is a scheduled task, it will be re-ran during the next scheduled time."
msgstr "これがスケジュールタスクの場合、次のスケジュールの時刻にはもう一度実行されます。"

#: cps/templates/user_edit.html:20
msgid "Reset user Password"
msgstr "パスワードをリセット"

#: cps/templates/user_edit.html:28
msgid "Send to eReader Email Address. Use comma to separate emails for multiple eReaders"
msgstr ""

#: cps/templates/user_edit.html:43
msgid "Language of Books"
msgstr "本の言語"

#: cps/templates/user_edit.html:54
msgid "OAuth Settings"
msgstr "OAuth設定"

#: cps/templates/user_edit.html:56
msgid "Link"
msgstr "リンク"

#: cps/templates/user_edit.html:58
msgid "Unlink"
msgstr "リンク解除"

#: cps/templates/user_edit.html:64
msgid "Kobo Sync Token"
msgstr "Kobo同期トークン"

#: cps/templates/user_edit.html:66
msgid "Create/View"
msgstr "作成/表示"

#: cps/templates/user_edit.html:70
msgid "Force full kobo sync"
msgstr "強制的にKoboと完全同期"

#: cps/templates/user_edit.html:88
msgid "Add allowed/Denied Custom Column Values"
msgstr "許可/拒否カスタムカラムを追加"

#: cps/templates/user_edit.html:137
msgid "Sync only books in selected shelves with Kobo"
msgstr "選択した本棚内の本のみKoboと同期"

#: cps/templates/user_edit.html:147 cps/templates/user_table.html:169
msgid "Delete User"
msgstr "ユーザーを削除"

#: cps/templates/user_edit.html:159
msgid "Generate Kobo Auth URL"
msgstr "Koboの認証URLを生成"

#: cps/templates/user_table.html:80 cps/templates/user_table.html:103
msgid "Select..."
msgstr "選択..."

#: cps/templates/user_table.html:131
msgid "Edit User"
msgstr "ユーザーを編集"

#: cps/templates/user_table.html:134
msgid "Enter Username"
msgstr "ユーザー名を入力してください"

#: cps/templates/user_table.html:135
#, fuzzy
msgid "Enter Email"
msgstr "テストメール"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "Enter eReader Email"
msgstr "E-Readerメールアドレス"

#: cps/templates/user_table.html:136
#, fuzzy
msgid "eReader Email"
msgstr "E-Readerメール"

#: cps/templates/user_table.html:137
msgid "Locale"
msgstr "地域"

#: cps/templates/user_table.html:138
msgid "Visible Book Languages"
msgstr "表示する本の言語"

#: cps/templates/user_table.html:139
msgid "Edit Allowed Tags"
msgstr "許可タグを編集"

#: cps/templates/user_table.html:139
msgid "Allowed Tags"
msgstr "許可タグ"

#: cps/templates/user_table.html:140
msgid "Edit Denied Tags"
msgstr "拒否タグを編集"

#: cps/templates/user_table.html:140
msgid "Denied Tags"
msgstr "拒否タグ"

#: cps/templates/user_table.html:141
msgid "Edit Allowed Column Values"
msgstr "許可カスタムカラムを編集"

#: cps/templates/user_table.html:141
msgid "Allowed Column Values"
msgstr "許可カスタムカラム"

#: cps/templates/user_table.html:142
msgid "Edit Denied Column Values"
msgstr "拒否カスタムカラムを編集"

#: cps/templates/user_table.html:142
msgid "Denied Column Values"
msgstr "拒否カスタムカラム"

#: cps/templates/user_table.html:144
msgid "Change Password"
msgstr "パスワード変更"

#: cps/templates/user_table.html:147
msgid "View"
msgstr "閲覧"

#: cps/templates/user_table.html:150
msgid "Edit Public Shelves"
msgstr "みんなの本棚を編集"

#: cps/templates/user_table.html:152
msgid "Sync selected Shelves with Kobo"
msgstr "選択した本棚をKoboと同期"

#: cps/templates/user_table.html:156
#, fuzzy
msgid "Show Read/Unread Section"
msgstr "既読/未読の選択を表示"

