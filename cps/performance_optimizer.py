# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

import time
import functools
from flask import request, g
from . import logger, config

log = logger.create()


class PerformanceOptimizer:
    """Performance optimization utilities for Calibre-Web"""
    
    def __init__(self):
        self.query_cache = {}
        self.cache_ttl = 300  # 5 minutes default TTL
        
    def cache_query_result(self, cache_key, result, ttl=None):
        """Cache query result with TTL"""
        if ttl is None:
            ttl = self.cache_ttl
            
        self.query_cache[cache_key] = {
            'result': result,
            'timestamp': time.time(),
            'ttl': ttl
        }
    
    def get_cached_result(self, cache_key):
        """Get cached result if still valid"""
        if cache_key in self.query_cache:
            cached = self.query_cache[cache_key]
            if time.time() - cached['timestamp'] < cached['ttl']:
                return cached['result']
            else:
                # Remove expired cache entry
                del self.query_cache[cache_key]
        return None
    
    def clear_cache(self, pattern=None):
        """Clear cache entries, optionally matching a pattern"""
        if pattern is None:
            self.query_cache.clear()
        else:
            keys_to_remove = [key for key in self.query_cache.keys() if pattern in key]
            for key in keys_to_remove:
                del self.query_cache[key]


def timing_decorator(func):
    """Decorator to measure function execution time"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        if execution_time > 1.0:  # Log slow operations (>1 second)
            log.warning("Slow operation detected: %s took %.2f seconds", 
                       func.__name__, execution_time)
        
        return result
    return wrapper


def cache_result(cache_key_func=None, ttl=300):
    """Decorator to cache function results"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
            
            # Try to get cached result
            cached_result = performance_optimizer.get_cached_result(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            performance_optimizer.cache_query_result(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator


def optimize_database_queries():
    """Apply database query optimizations"""
    from . import calibre_db
    
    # Enable SQLite optimizations
    try:
        calibre_db.session.execute("PRAGMA cache_size = 20000")  # Increase cache size
        calibre_db.session.execute("PRAGMA temp_store = MEMORY")  # Use memory for temp storage
        calibre_db.session.execute("PRAGMA journal_mode = WAL")  # Use WAL mode for better concurrency
        calibre_db.session.execute("PRAGMA synchronous = NORMAL")  # Balance safety and performance
        calibre_db.session.execute("PRAGMA mmap_size = 268435456")  # 256MB memory mapping
        log.info("Applied database performance optimizations")
    except Exception as e:
        log.warning("Failed to apply some database optimizations: %s", e)


def add_performance_headers(response):
    """Add performance-related headers to responses"""
    if hasattr(g, 'start_time'):
        response_time = time.time() - g.start_time
        response.headers['X-Response-Time'] = f"{response_time:.3f}s"
    
    # Add caching headers for static content
    if request.endpoint and 'static' in request.endpoint:
        response.headers['Cache-Control'] = 'public, max-age=31536000'  # 1 year
    elif request.endpoint and any(x in request.endpoint for x in ['cover', 'thumbnail']):
        response.headers['Cache-Control'] = 'public, max-age=86400'  # 1 day
    
    return response


def preload_critical_data():
    """Preload frequently accessed data"""
    from . import calibre_db, db
    
    try:
        # Preload book count for dashboard
        book_count = calibre_db.session.query(db.Books).count()
        performance_optimizer.cache_query_result('book_count', book_count, ttl=600)
        
        # Preload author count
        author_count = calibre_db.session.query(db.Authors).count()
        performance_optimizer.cache_query_result('author_count', author_count, ttl=600)
        
        # Preload series count
        series_count = calibre_db.session.query(db.Series).count()
        performance_optimizer.cache_query_result('series_count', series_count, ttl=600)
        
        # Preload tag count
        tag_count = calibre_db.session.query(db.Tags).count()
        performance_optimizer.cache_query_result('tag_count', tag_count, ttl=600)
        
        log.debug("Preloaded critical data for performance")
        
    except Exception as e:
        log.warning("Failed to preload some critical data: %s", e)


def optimize_book_queries():
    """Optimize common book queries"""
    from . import calibre_db, db
    
    # Create indexes for common queries if they don't exist
    try:
        # Index on book title for faster searching
        calibre_db.session.execute("CREATE INDEX IF NOT EXISTS idx_books_title ON books(title)")
        
        # Index on book sort for faster sorting
        calibre_db.session.execute("CREATE INDEX IF NOT EXISTS idx_books_sort ON books(sort)")
        
        # Index on book timestamp for recent books
        calibre_db.session.execute("CREATE INDEX IF NOT EXISTS idx_books_timestamp ON books(timestamp)")
        
        # Index on book path for faster file operations
        calibre_db.session.execute("CREATE INDEX IF NOT EXISTS idx_books_path ON books(path)")
        
        # Composite index for common filters
        calibre_db.session.execute("CREATE INDEX IF NOT EXISTS idx_books_title_sort ON books(title, sort)")
        
        log.info("Created database indexes for performance optimization")
        
    except Exception as e:
        log.warning("Failed to create some database indexes: %s", e)


def get_optimized_book_list(offset=0, limit=50, order_by=None):
    """Get optimized book list with caching"""
    from . import calibre_db, db
    
    cache_key = f"book_list_{offset}_{limit}_{order_by}"
    cached_result = performance_optimizer.get_cached_result(cache_key)
    
    if cached_result is not None:
        return cached_result
    
    # Build optimized query
    query = calibre_db.session.query(db.Books)
    
    if order_by:
        if order_by == 'title':
            query = query.order_by(db.Books.title)
        elif order_by == 'timestamp':
            query = query.order_by(db.Books.timestamp.desc())
        elif order_by == 'author':
            query = query.join(db.books_authors_link).join(db.Authors).order_by(db.Authors.name)
    
    # Apply pagination
    books = query.offset(offset).limit(limit).all()
    
    # Cache result for 5 minutes
    performance_optimizer.cache_query_result(cache_key, books, ttl=300)
    
    return books


def optimize_search_performance():
    """Optimize search performance"""
    from . import calibre_db, db
    
    try:
        # Create full-text search indexes if SQLite supports it
        calibre_db.session.execute("""
            CREATE VIRTUAL TABLE IF NOT EXISTS books_fts USING fts5(
                title, 
                content='books', 
                content_rowid='id'
            )
        """)
        
        # Populate FTS table
        calibre_db.session.execute("""
            INSERT OR REPLACE INTO books_fts(rowid, title) 
            SELECT id, title FROM books
        """)
        
        log.info("Created full-text search indexes")
        
    except Exception as e:
        log.debug("FTS not available or failed to create: %s", e)


def monitor_performance():
    """Monitor application performance"""
    import psutil
    import os
    
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        cpu_percent = process.cpu_percent()
        
        performance_data = {
            'memory_rss': memory_info.rss,
            'memory_vms': memory_info.vms,
            'cpu_percent': cpu_percent,
            'timestamp': time.time()
        }
        
        # Log performance warnings
        if memory_info.rss > 500 * 1024 * 1024:  # 500MB
            log.warning("High memory usage detected: %.2f MB", memory_info.rss / 1024 / 1024)
        
        if cpu_percent > 80:
            log.warning("High CPU usage detected: %.2f%%", cpu_percent)
        
        return performance_data
        
    except ImportError:
        log.debug("psutil not available for performance monitoring")
        return None
    except Exception as e:
        log.warning("Performance monitoring failed: %s", e)
        return None


# Global instance
performance_optimizer = PerformanceOptimizer()


def init_performance_optimizations():
    """Initialize all performance optimizations"""
    log.info("Initializing performance optimizations...")
    
    optimize_database_queries()
    optimize_book_queries()
    optimize_search_performance()
    preload_critical_data()
    
    log.info("Performance optimizations initialized")
