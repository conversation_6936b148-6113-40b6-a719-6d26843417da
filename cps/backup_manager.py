# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

import os
import json
import shutil
import tarfile
import zipfile
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

from . import logger, config, calibre_db, db, ub
from .enhanced_book_manager import enhanced_book_manager

log = logger.create()


class BackupManager:
    """Automated backup and recovery system for Calibre-Web"""
    
    def __init__(self):
        self.backup_dir = os.path.join(config.CONFIG_DIR, 'backups')
        self.ensure_backup_directory()
        
    def ensure_backup_directory(self):
        """Ensure backup directory exists"""
        try:
            os.makedirs(self.backup_dir, exist_ok=True)
            log.debug("Backup directory ensured: %s", self.backup_dir)
        except Exception as e:
            log.error("Failed to create backup directory: %s", e)
    
    def create_full_backup(self, include_books: bool = False) -> Dict[str, Any]:
        """Create a full backup of the library"""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        backup_name = f"calibre_web_backup_{timestamp}"
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.tar.gz")
        
        try:
            with tarfile.open(backup_path, 'w:gz') as tar:
                # Backup configuration database
                config_db_path = ub.app_DB_path
                if os.path.exists(config_db_path):
                    tar.add(config_db_path, arcname='app.db')
                    log.debug("Added configuration database to backup")
                
                # Backup Calibre database
                calibre_db_path = os.path.join(config.config_calibre_dir, 'metadata.db')
                if os.path.exists(calibre_db_path):
                    tar.add(calibre_db_path, arcname='metadata.db')
                    log.debug("Added Calibre database to backup")
                
                # Backup configuration files
                config_files = ['app.db', 'gdrive.db']
                for config_file in config_files:
                    file_path = os.path.join(config.CONFIG_DIR, config_file)
                    if os.path.exists(file_path):
                        tar.add(file_path, arcname=config_file)
                
                # Export metadata as JSON
                metadata_export = self._export_all_metadata()
                metadata_path = os.path.join(self.backup_dir, f"{backup_name}_metadata.json")
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata_export, f, indent=2, default=str)
                tar.add(metadata_path, arcname='metadata_export.json')
                
                # Optionally include book files
                if include_books and config.config_calibre_dir:
                    book_dir = config.config_calibre_dir
                    if os.path.exists(book_dir):
                        tar.add(book_dir, arcname='books')
                        log.debug("Added book files to backup")
            
            # Clean up temporary metadata file
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            backup_info = {
                'name': backup_name,
                'path': backup_path,
                'timestamp': timestamp,
                'size': os.path.getsize(backup_path),
                'includes_books': include_books,
                'status': 'completed'
            }
            
            # Save backup info
            self._save_backup_info(backup_info)
            
            log.info("Full backup created: %s", backup_path)
            return backup_info
            
        except Exception as e:
            log.error("Failed to create full backup: %s", e)
            return {
                'name': backup_name,
                'timestamp': timestamp,
                'status': 'failed',
                'error': str(e)
            }
    
    def create_metadata_backup(self) -> Dict[str, Any]:
        """Create a metadata-only backup"""
        timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
        backup_name = f"metadata_backup_{timestamp}"
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.json")
        
        try:
            metadata_export = self._export_all_metadata()
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(metadata_export, f, indent=2, default=str)
            
            backup_info = {
                'name': backup_name,
                'path': backup_path,
                'timestamp': timestamp,
                'size': os.path.getsize(backup_path),
                'type': 'metadata_only',
                'status': 'completed'
            }
            
            self._save_backup_info(backup_info)
            
            log.info("Metadata backup created: %s", backup_path)
            return backup_info
            
        except Exception as e:
            log.error("Failed to create metadata backup: %s", e)
            return {
                'name': backup_name,
                'timestamp': timestamp,
                'type': 'metadata_only',
                'status': 'failed',
                'error': str(e)
            }
    
    def _export_all_metadata(self) -> Dict[str, Any]:
        """Export all book metadata"""
        try:
            books = calibre_db.session.query(db.Books).all()
            books_data = []
            
            for book in books:
                book_data = enhanced_book_manager.backup_book_data(book.id)
                books_data.append(book_data)
            
            # Export library statistics
            stats = enhanced_book_manager.get_book_statistics()
            
            export_data = {
                'export_timestamp': datetime.utcnow().isoformat(),
                'calibre_web_version': getattr(config, 'STABLE_VERSION', 'unknown'),
                'library_path': config.config_calibre_dir,
                'statistics': stats,
                'books': books_data,
                'total_books': len(books_data)
            }
            
            return export_data
            
        except Exception as e:
            log.error("Failed to export metadata: %s", e)
            return {}
    
    def restore_from_backup(self, backup_path: str, restore_books: bool = False) -> Dict[str, Any]:
        """Restore from a backup file"""
        try:
            if not os.path.exists(backup_path):
                return {'status': 'failed', 'error': 'Backup file not found'}
            
            # Create restore point before restoration
            restore_point = self.create_metadata_backup()
            
            if backup_path.endswith('.tar.gz'):
                return self._restore_from_tarball(backup_path, restore_books)
            elif backup_path.endswith('.json'):
                return self._restore_from_metadata(backup_path)
            else:
                return {'status': 'failed', 'error': 'Unsupported backup format'}
                
        except Exception as e:
            log.error("Failed to restore from backup: %s", e)
            return {'status': 'failed', 'error': str(e)}
    
    def _restore_from_tarball(self, backup_path: str, restore_books: bool = False) -> Dict[str, Any]:
        """Restore from tarball backup"""
        try:
            restore_dir = os.path.join(self.backup_dir, 'restore_temp')
            os.makedirs(restore_dir, exist_ok=True)
            
            with tarfile.open(backup_path, 'r:gz') as tar:
                tar.extractall(restore_dir)
            
            # Restore configuration database
            app_db_path = os.path.join(restore_dir, 'app.db')
            if os.path.exists(app_db_path):
                shutil.copy2(app_db_path, ub.app_DB_path)
                log.info("Restored configuration database")
            
            # Restore Calibre database
            metadata_db_path = os.path.join(restore_dir, 'metadata.db')
            if os.path.exists(metadata_db_path):
                calibre_db_target = os.path.join(config.config_calibre_dir, 'metadata.db')
                shutil.copy2(metadata_db_path, calibre_db_target)
                log.info("Restored Calibre database")
            
            # Restore book files if requested
            if restore_books:
                books_dir = os.path.join(restore_dir, 'books')
                if os.path.exists(books_dir):
                    # This is dangerous - should be done carefully
                    log.warning("Book file restoration not implemented for safety")
            
            # Clean up
            shutil.rmtree(restore_dir, ignore_errors=True)
            
            return {
                'status': 'completed',
                'message': 'Backup restored successfully',
                'restart_required': True
            }
            
        except Exception as e:
            log.error("Failed to restore from tarball: %s", e)
            return {'status': 'failed', 'error': str(e)}
    
    def _restore_from_metadata(self, backup_path: str) -> Dict[str, Any]:
        """Restore metadata from JSON backup"""
        try:
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            books_data = backup_data.get('books', [])
            restored_count = 0
            
            for book_backup in books_data:
                try:
                    # This would require implementing metadata restoration logic
                    # For now, just log what would be restored
                    book_id = book_backup.get('book_id')
                    if book_id:
                        log.debug("Would restore metadata for book %s", book_id)
                        restored_count += 1
                except Exception as e:
                    log.warning("Failed to restore book metadata: %s", e)
            
            return {
                'status': 'completed',
                'message': f'Metadata restoration completed for {restored_count} books',
                'restored_count': restored_count
            }
            
        except Exception as e:
            log.error("Failed to restore metadata: %s", e)
            return {'status': 'failed', 'error': str(e)}
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """List all available backups"""
        backups = []
        
        try:
            backup_info_path = os.path.join(self.backup_dir, 'backup_info.json')
            if os.path.exists(backup_info_path):
                with open(backup_info_path, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                    backups = backup_info.get('backups', [])
            
            # Also scan for backup files
            for file in os.listdir(self.backup_dir):
                if file.endswith(('.tar.gz', '.json')) and not file.endswith('backup_info.json'):
                    file_path = os.path.join(self.backup_dir, file)
                    file_stat = os.stat(file_path)
                    
                    # Check if this backup is already in our info
                    existing = next((b for b in backups if b.get('name') == file), None)
                    if not existing:
                        backups.append({
                            'name': file,
                            'path': file_path,
                            'size': file_stat.st_size,
                            'timestamp': datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y%m%d_%H%M%S'),
                            'status': 'unknown'
                        })
            
            # Sort by timestamp (newest first)
            backups.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
        except Exception as e:
            log.error("Failed to list backups: %s", e)
        
        return backups
    
    def cleanup_old_backups(self, keep_days: int = 30) -> Dict[str, Any]:
        """Clean up old backup files"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=keep_days)
            deleted_count = 0
            total_size_freed = 0
            
            for backup in self.list_backups():
                backup_path = backup.get('path')
                if not backup_path or not os.path.exists(backup_path):
                    continue
                
                file_stat = os.stat(backup_path)
                file_date = datetime.fromtimestamp(file_stat.st_mtime)
                
                if file_date < cutoff_date:
                    file_size = file_stat.st_size
                    os.remove(backup_path)
                    deleted_count += 1
                    total_size_freed += file_size
                    log.debug("Deleted old backup: %s", backup_path)
            
            return {
                'deleted_count': deleted_count,
                'size_freed': total_size_freed,
                'status': 'completed'
            }
            
        except Exception as e:
            log.error("Failed to cleanup old backups: %s", e)
            return {'status': 'failed', 'error': str(e)}
    
    def _save_backup_info(self, backup_info: Dict[str, Any]):
        """Save backup information"""
        try:
            backup_info_path = os.path.join(self.backup_dir, 'backup_info.json')
            
            # Load existing info
            all_backups = {'backups': []}
            if os.path.exists(backup_info_path):
                with open(backup_info_path, 'r', encoding='utf-8') as f:
                    all_backups = json.load(f)
            
            # Add new backup info
            all_backups['backups'].append(backup_info)
            
            # Save updated info
            with open(backup_info_path, 'w', encoding='utf-8') as f:
                json.dump(all_backups, f, indent=2, default=str)
                
        except Exception as e:
            log.error("Failed to save backup info: %s", e)
    
    def schedule_automatic_backup(self, interval_hours: int = 24):
        """Schedule automatic backups (placeholder for future implementation)"""
        log.info("Automatic backup scheduling not yet implemented")
        # This would integrate with a task scheduler
        pass


# Global instance
backup_manager = BackupManager()
