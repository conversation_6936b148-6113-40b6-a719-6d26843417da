{% macro restrict_modal() %}
<div class="modal fade" id="restrictModal" tabindex="-1" role="dialog" aria-labelledby="restrictModalLabel">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title hidden" id="h1">{{_('Select Allowed/Denied Tags')}}</h4>
        <h4 class="modal-title hidden" id="h2">{{_('Select Allowed/Denied Custom Column Values')}}</h4>
        <h4 class="modal-title hidden" id="h3">{{_('Select Allowed/Denied Tags of User')}}</h4>
        <h4 class="modal-title hidden" id="h4">{{_('Select Allowed/Denied Custom Column Values of User')}}</h4>
      </div>
      <div class="modal-body">
          <table class="table table-no-bordered" id="restrict-elements-table" data-id-field="id" data-show-header="false" data-editable-mode="inline">
            <thead>
              <tr>
                  <th data-field="Element" id="Element" data-escape="true" data-editable-type="text" data-editable="true" data-editable-title="{{_('Enter Tag')}}"></th>
                  <th data-field="type" id="type" data-visible="true"></th>
                  <th data-field="id" id="id" data-visible="false"></th>
                  <th data-align="right" data-formatter="RestrictionActions"></th>
              </tr>
            </thead>
          </table>
          <form id="add_restriction" action="" method="POST">
          <div class="form-group required">
            <label for="add_element">{{_('Add View Restriction')}}</label>
            <input type="text" class="form-control" name="add_element" id="add_element">
          </div>
          <div class="form-group required">
            <input type="button" class="btn btn-default" value="{{_('Allow')}}" name="submit_allow" id="submit_allow" data-dismiss="static">
            <input type="button" class="btn btn-default" value="{{_('Deny')}}" name="submit_deny" id="submit_restrict" data-dismiss="static">
          </div>
          </form>
      </div>
      <div class="modal-footer">
        <button type="button" id="restrict_close" class="btn btn-default" data-dismiss="modal">{{_('Close')}}</button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}
{% macro delete_book(allow) %}
{% if allow %}
<div class="modal fade" id="deleteModal" role="dialog" aria-labelledby="metaDeleteLabel">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-center">
          <span>{{_('Are you really sure?')}}</span>
      </div>
        <div class="modal-body text-center">
          <p>
          <span class="hidden" id="book_format">{{_('This book format will be permanently erased from database')}}</span>
          <span class="hidden" id="book_complete">{{_('This book will be permanently erased from database')}}</span>
          <span>{{_('and hard disk')}}</span>
		  </p>
          {% if config.config_kobo_sync %}
          <p>
            <span>{{_('Important Kobo Note: deleted books will remain on any paired Kobo device.')}}</span>
            <span>{{_('Books must first be archived and the device synced before a book can safely be deleted.')}}</span>
          </p>
          {% endif %}
        </div>

      <div class="modal-footer">
        <input type="button" class="btn btn-danger" value="{{_('Delete')}}" name="delete_confirm" id="delete_confirm" data-dismiss="modal">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>
{% endif %}
{% endmacro %}
{% macro filechooser_modal() %}
<div class="modal fade" id="fileModal" role="dialog" aria-labelledby="metafileLabel">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-info text-center">
          <span>{{_('Choose File Location')}}</span>
      </div>
        <div class="modal-body">
         <table id="file_table" class="table table-striped">
            <thead>
              <tr>
                  <th>{{_('type')}}</th>
                  <th>{{_('name')}}</th>
                  <th>{{_('size')}}</th>
              </tr>
            </thead>
            <tbody id="tbody">
              <tr class="tr-clickable hidden" id="parent" data-type="dir" data-path="..">
                <td><span class="glyphicon glyphicon-folder-close"></span></td>
                <td title="{{_('Parent Directory')}}"><span class="parentdir">..</span></td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
      <div class="modal-footer">
        <div class="text-left" id="element_selected"></div>
        <input type="button" class="btn btn-primary" data-path="" data-link=""  data-folderonly="" data-filefilter="" data-newfile="" value="{{_('Select')}}" name="file_confirm" id="file_confirm" data-dismiss="modal">
        <button type="button" id="file_abort" class="btn btn-default" data-dismiss="modal">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

{% macro delete_confirm_modal() %}
<div id="GeneralDeleteModal" class="modal fade" role="Dialog">
  <div class="modal-dialog modal-sm">
    <div class="modal-content">
      <div class="modal-header bg-danger text-center">
      <span id="header-GeneralDeleteModal"></span>
      </div>
      <div class="modal-body text-center">
        <span id="text-GeneralDeleteModal"></span>
          <p></p>
        <button id="btnConfirmYes-GeneralDeleteModal" type="button" class="btn btn btn-danger">{{_('Delete')}}</button>
        <button id="btnConfirmNo-GeneralDeleteModal" type="button" class="btn btn-default">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>
{% endmacro %}

{% macro change_confirm_modal() %}
<div id="GeneralChangeModal" class="modal fade" role="Dialog">
  <div class="modal-dialog modal-sm">
    <div class="modal-content">
      <div class="modal-header bg-info text-center">
      <span id="header-GeneralChangeModal"></span>
      </div>
      <div class="modal-body text-center">
        <span id="text-GeneralChangeModal"></span>
          <p></p>
        <button id="btnConfirmYes-GeneralChangeModal" type="button" class="btn btn btn-default">{{_('Ok')}}</button>
        <button id="btnConfirmNo-GeneralChangeModal" type="button" class="btn btn-default">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>

{% endmacro %}
