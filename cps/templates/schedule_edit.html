{% extends "layout.html" %}
{% block header %}
<link href="{{ url_for('static', filename='css/libs/bootstrap-table.min.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='css/libs/bootstrap-editable.css') }}" rel="stylesheet">
{% endblock %}
{% block body %}
<div class="discover">
  <h1>{{title}}</h1>
  <form role="form" class="col-md-10 col-lg-6" method="POST" autocomplete="off">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-group">
      <label for="schedule_start_time">{{_('Start Time')}}</label>
      <select name="schedule_start_time" id="schedule_start_time" class="form-control">
        {% for n in starttime %}
          <option value="{{n[0]}}" {% if config.schedule_start_time == n[0] %}selected{% endif %}>{{n[1]}}</option>
        {% endfor %}
      </select>
    </div>
    <div class="form-group">
      <label for="schedule_duration">{{_('Maximum Duration')}}</label>
      <select name="schedule_duration" id="schedule_duration" class="form-control">
        {% for n in duration %}
          <option value="{{n[0]}}" {% if config.schedule_duration == n[0] %}selected{% endif %}>{{n[1]}}</option>
        {% endfor %}
      </select>
    </div>
    <div class="form-group">
      <input type="checkbox" id="schedule_generate_book_covers" name="schedule_generate_book_covers" {% if config.schedule_generate_book_covers %}checked{% endif %}>
      <label for="schedule_generate_book_covers">{{_('Generate Thumbnails')}}</label>
    </div>
    <!--div class="form-group">
      <input type="checkbox" id="schedule_generate_series_covers" name="schedule_generate_series_covers" {% if config.schedule_generate_series_covers %}checked{% endif %}>
      <label for="schedule_generate_series_covers">{{_('Generate Series Cover Thumbnails')}}</label>
    </div-->
    <div class="form-group">
      <input type="checkbox" id="schedule_reconnect" name="schedule_reconnect" {% if config.schedule_reconnect %}checked{% endif %}>
      <label for="schedule_reconnect">{{_('Reconnect Calibre Database')}}</label>
    </div>
    <div class="form-group">
      <input type="checkbox" id="schedule_metadata_backup" name="schedule_metadata_backup" {% if config.schedule_metadata_backup %}checked{% endif %}>
      <label for="schedule_metadata_backup">{{_('Generate Metadata Backup Files')}}</label>
    </div>

    <button type="submit" name="submit" value="submit" class="btn btn-default">{{_('Save')}}</button>
    <a href="{{ url_for('admin.admin') }}" id="email_back" class="btn btn-default">{{_('Cancel')}}</a>
  </form>
</div>
{% endblock %}
