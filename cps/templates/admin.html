{% extends "layout.html" %}
{% macro display_bool_setting(setting_value) -%}
  {% if setting_value %}<span class="glyphicon glyphicon-ok"></span>{% else %}<span class="glyphicon glyphicon-remove"></span>{% endif %}
{%- endmacro %}
{% block body %}
<div class="container-fluid">
  <div class="row">
    <div class="col">
      <h2>{{_('Users')}}</h2>
    {% if allUser.__len__() < 10 %}
      <table class="table table-striped" id="table_user">
        <tr>
            <th>{{_('Username')}}</th>
            <th>{{_('Email')}}</th>
            <th>{{_('Send to eReader Email')}}</th>
            <th>{{_('Downloads')}}</th>
            <th class="hidden-xs ">{{_('Admin')}}</th>
            <th class="hidden-xs hidden-sm">{{_('Password')}}</th>
          {% if config.config_uploading %}
            <th class="hidden-xs hidden-sm">{{_('Upload')}}</th>
          {%  endif %}
            <th class="hidden-xs hidden-sm">{{_('Download')}}</th>
            <th class="hidden-xs hidden-sm hidden-md">{{_('View Books')}}</th>
            <th class="hidden-xs hidden-sm hidden-md">{{_('Edit')}}</th>
            <th class="hidden-xs hidden-sm hidden-md">{{_('Delete')}}</th>
            <th class="hidden-xs hidden-sm hidden-md">{{_('Public Shelf')}}</th>
        </tr>
        {% for user in allUser %}
          {% if not user.role_anonymous() or config.config_anonbrowse %}
          <tr>
            <td><a class="session" href="{{url_for('admin.edit_user', user_id=user.id)}}">{{user.name}}</a></td>
            <td>{{user.email}}</td>
            <td>{{user.kindle_mail}}</td>
            <td>{{user.downloads.count()}}</td>
            <td class="hidden-xs">{{ display_bool_setting(user.role_admin()) }}</td>
            <td class="hidden-xs hidden-sm">{{ display_bool_setting(user.role_passwd()) }}</td>
            {% if config.config_uploading %}
            <td class="hidden-xs hidden-sm">{{ display_bool_setting(user.role_upload()) }}</td>
            {%  endif %}
            <td class="hidden-xs hidden-sm">{{ display_bool_setting(user.role_download()) }}</td>
            <td class="hidden-xs hidden-sm hidden-md">{{ display_bool_setting(user.role_viewer()) }}</td>
            <td class="hidden-xs hidden-sm hidden-md">{{ display_bool_setting(user.role_edit()) }}</td>
            <td class="hidden-xs hidden-sm hidden-md">{{ display_bool_setting(user.role_delete_books()) }}</td>
            <td class="hidden-xs hidden-sm hidden-md">{{ display_bool_setting(user.role_edit_shelfs()) }}</td>
          </tr>
          {% endif %}
        {% endfor %}
      </table>
    {% endif %}
        {% if not simple %}
          <a class="btn btn-default" id="admin_user_table" href="{{url_for('admin.edit_user_table')}}">{{_('Edit Users')}}</a>
        {% endif %}
        <a class="btn btn-default" id="admin_new_user" href="{{url_for('admin.new_user')}}">{{_('Add New User')}}</a>
      {% if (config.config_login_type == 1) %}
        <div class="btn btn-default" id="import_ldap_users" data-toggle="modal" data-target="#StatusDialog">{{_('Import LDAP Users')}}</div>
      {% endif %}
    </div>
  </div>

  <div class="row">
    <div class="col">
      <h2>{{_('Email Server Settings')}}</h2>
      {% if config.get_mail_server_configured() %}
        {% if config.mail_server_type == 0 %}
        <div class="col-xs-12 col-sm-12">
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('SMTP Hostname')}}</div>
            <div class="col-xs-6 col-sm-3">{{config.mail_server}}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('SMTP Port')}}</div>
            <div class="col-xs-6 col-sm-3">{{config.mail_port}}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Encryption')}}</div>
            <div class="col-xs-6 col-sm-3">{{ display_bool_setting(config.mail_use_ssl) }}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('SMTP Login')}}</div>
            <div class="col-xs-6 col-sm-3">{{config.mail_login}}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('From Email')}}</div>
            <div class="col-xs-6 col-sm-3">{{config.mail_from}}</div>
          </div>
        </div>
      {% else %}
        <div class="col-xs-12 col-sm-12">
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Email Service')}}</div>
            <div class="col-xs-6 col-sm-3">{{_('Gmail via Oauth2')}}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('From Email')}}</div>
            <div class="col-xs-6 col-sm-3">{{config.mail_gmail_token['email']}}</div>
          </div>
        </div>
      {% endif %}
    {% endif %}
      <a class="btn btn-default emailconfig" id="admin_edit_email" href="{{url_for('admin.edit_mailsettings')}}">{{_('Edit Email Server Settings')}}</a>
    </div>
  </div>

  <div class="row">
    <div class="col">
      <h2>{{_('Configuration')}}</h2>
      <div class="col-xs-12 col-sm-6">
        <div class="row">
            <div class="col-xs-6 col-sm-6">{{_('Calibre Database Directory')}}</div>
            <div class="col-xs-6 col-sm-6">{{config.config_calibre_dir}}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-6">{{_('Log Level')}}</div>
            <div class="col-xs-6 col-sm-6">{{config.get_log_level()}}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-6">{{_('Port')}}</div>
            <div class="col-xs-6 col-sm-6">{{config.config_port}}</div>
        </div>
        {% if kobo_support and config.config_port != config.config_external_port %}
        <div class="row">
          <div class="col-xs-6 col-sm-6">{{_('External Port')}}</div>
          <div class="col-xs-6 col-sm-6">{{config.config_external_port}}</div>
        </div>
        {% endif %}
      </div>
      <div class="col-xs-12 col-sm-6">
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Books per Page')}}</div>
            <div class="col-xs-6 col-sm-5">{{config.config_books_per_page}}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Uploads')}}</div>
            <div class="col-xs-6 col-sm-5">{{ display_bool_setting(config.config_uploading) }}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Anonymous Browsing')}}</div>
            <div class="col-xs-6 col-sm-5">{{ display_bool_setting(config.config_anonbrowse) }}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Public Registration')}}</div>
            <div class="col-xs-6 col-sm-5">{{ display_bool_setting(config.config_public_reg) }}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Magic Link Remote Login')}}</div>
            <div class="col-xs-6 col-sm-5">{{ display_bool_setting(config.config_remote_login) }}</div>
        </div>
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Reverse Proxy Login')}}</div>
            <div class="col-xs-6 col-sm-5">{{ display_bool_setting(config.config_allow_reverse_proxy_header_login) }}</div>
        </div>
        {% if config.config_allow_reverse_proxy_header_login %}
        <div class="row">
            <div class="col-xs-6 col-sm-7">{{_('Reverse Proxy Header Name')}}</div>
            <div class="col-xs-6 col-sm-5">{{ config.config_reverse_proxy_login_header_name }}</div>
        </div>
        {% endif %}
      </div>
      <a class="btn btn-default" id="db_config" href="{{url_for('admin.db_configuration')}}">{{_('Edit Calibre Database Configuration')}}</a>
      <a class="btn btn-default" id="basic_config" href="{{url_for('admin.configuration')}}">{{_('Edit Basic Configuration')}}</a>
      <a class="btn btn-default" id="view_config" href="{{url_for('admin.view_configuration')}}">{{_('Edit UI Configuration')}}</a>
    </div>
  </div>
{%  if feature_support['scheduler'] %}
  <div class="row">
    <div class="col">
      <h2>{{_('Scheduled Tasks')}}</h2>
        <div class="col-xs-12 col-sm-12 scheduled_tasks_details">
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Start Time')}}</div>
            <div class="col-xs-6 col-sm-3">{{schedule_time}}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Maximum Duration')}}</div>
            <div class="col-xs-6 col-sm-3">{{schedule_duration}}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Generate Thumbnails')}}</div>
            <div class="col-xs-6 col-sm-3">{{ display_bool_setting(config.schedule_generate_book_covers) }}</div>
          </div>
          <!--div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Generate series cover thumbnails')}}</div>
            <div class="col-xs-6 col-sm-3">{{ display_bool_setting(config.schedule_generate_series_covers) }}</div>
          </div-->
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Reconnect Calibre Database')}}</div>
            <div class="col-xs-6 col-sm-3">{{ display_bool_setting(config.schedule_reconnect) }}</div>
          </div>
          <div class="row">
            <div class="col-xs-6 col-sm-3">{{_('Generate Metadata Backup Files')}}</div>
            <div class="col-xs-6 col-sm-3">{{ display_bool_setting(config.schedule_metadata_backup) }}</div>
          </div>

        </div>
      <a class="btn btn-default scheduledtasks" id="admin_edit_scheduled_tasks" href="{{url_for('admin.edit_scheduledtasks')}}">{{_('Edit Scheduled Tasks Settings')}}</a>
      {% if config.schedule_generate_book_covers %}
        <a class="btn btn-default" id="admin_refresh_cover_cache">{{_('Refresh Thumbnail Cache')}}</a>
      {%  endif %}
    </div>
  </div>
{%  endif %}
  <div class="row form-group">
    <h2>{{_('Administration')}}</h2>
    <a class="btn btn-default" id="debug" href="{{url_for('admin.download_debug')}}">{{_('Download Debug Package')}}</a>
    <a class="btn btn-default" id="logfile" href="{{url_for('admin.view_logfile')}}">{{_('View Logs')}}</a>
  </div>
  <div class="row form-group">
    <div class="btn btn-default" id="restart_database" data-toggle="modal" data-target="#StatusDialog">{{_('Reconnect Calibre Database')}}</div>
  </div>
  <div class="row form-group">
    <div class="btn btn-default" id="admin_restart" data-toggle="modal" data-target="#RestartDialog">{{_('Restart')}}</div>
    <div class="btn btn-default" id="admin_stop" data-toggle="modal" data-target="#ShutdownDialog">{{_('Shutdown')}}</div>
  </div>
{% if config.schedule_metadata_backup %}
  <div class="row form-group">
    <div class="btn btn-default" id="metadata_backup" data-toggle="modal" data-target="#StatusDialog">{{_('Queue all books for metadata backup')}}</div>
  </div>
{%  endif %}
  <div class="row">
    <div class="col">
      <h2>{{_('Version Information')}}</h2>
      <table class="table table-striped" id="update_table">
        <thead>
          <tr>
              <th class="col-xs-3">{{_('Version')}}</th>
              <th class="col-xl-8">{{_('Details')}}</th>
          </tr>
        </thead>
        <tbody>
          <tr id="current_version">
            <td>{{commit}} </td>
            <td><i>{{_('Current Version')}}</i></td>
          </tr>
        </tbody>
      </table>
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
      {%  if feature_support['updater'] %}
      <div class="hidden" id="update_error"> <span>{{update_error}}</span></div>
      <div class="btn btn-primary" id="check_for_update">{{_('Check for Update')}}</div>
      <div class="btn btn-primary hidden" id="perform_update" data-toggle="modal" data-target="#StatusDialog">{{_('Perform Update')}}</div>
      {%  endif %}
    </div>
  </div>
</div>

<!-- Modal -->
<div id="RestartDialog" class="modal fade" role="dialog">
  <div class="modal-dialog modal-sm">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header bg-info"></div>
      <div class="modal-body text-center">
        <p>{{_('Are you sure you want to restart?')}}</p>
          <div id="spinner" class="spinner"  style="display:none;">
                <img id="img-spinner" src="{{ url_for('static', filename='css/libs/images/loading-icon.gif') }}"/>
          </div>
          <p></p>
        <button type="button" class="btn btn-default" id="restart" >{{_('OK')}}</button>
        <button type="button" class="btn btn-default" data-dismiss="modal">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>
<div id="ShutdownDialog" class="modal fade" role="dialog">
  <div class="modal-dialog modal-sm">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header bg-info">
      </div>
      <div class="modal-body text-center">
        <p>{{_('Are you sure you want to shutdown?')}}</p>
        <button type="button" class="btn btn-default" id="shutdown" data-dismiss="modal">{{_('OK')}}</button>
        <button type="button" class="btn btn-default" data-dismiss="modal">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>
<div id="StatusDialog" class="modal fade" role="dialog">
  <div class="modal-dialog modal-sm">
    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header bg-info text-center">
      <span id="DialogHeader">{{_('Updating, please do not reload this page')}}</span>
      </div>
      <div class="modal-body text-center">
          <div id="spinner2" class="spinner2"  style="display:none;">
                <img id="img-spinner2" src="{{ url_for('static', filename='css/libs/images/loading-icon.gif') }}"/>
          </div>
          <p></p>
         <div id="DialogContent"></div>
          <p></p>
        <button type="button" class="btn btn-default hidden" id="DialogFinished" data-dismiss="modal">{{_('OK')}}</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}
{% block modal %}
{{ change_confirm_modal() }}
{% endblock %}
