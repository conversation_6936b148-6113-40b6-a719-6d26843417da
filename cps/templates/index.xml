<?xml version="1.0" encoding="UTF-8"?>
<feed xmlns="http://www.w3.org/2005/Atom">
  <icon>{{ url_for('static', filename='favicon.ico') }}</icon>
  <id>urn:uuid:2853dacf-ed79-42f5-8e8a-a7bb3d1ae6a2</id>
  <updated>{{ current_time }}</updated>
  <link rel="self" href="{{url_for('opds.feed_index')}}" type="application/atom+xml;profile=opds-catalog;kind=navigation"/>
  <link rel="start" title="{{_('Start')}}" href="{{url_for('opds.feed_index')}}"
        type="application/atom+xml;profile=opds-catalog;kind=navigation"/>
  <link rel="search"
      href="{{url_for('opds.feed_osd')}}"
      type="application/opensearchdescription+xml"/>
  <link type="application/atom+xml" rel="search" title="{{_('Search')}}" href="{{url_for('opds.feed_normal_search')}}/{searchTerms}" />
  <title>{{instance}}</title>
  <author>
    <name>{{instance}}</name>
    <uri>https://github.com/janeczku/calibre-web</uri>
  </author>
  <entry>
    <title>{{_('Alphabetical Books')}}</title>
    <link href="{{url_for('opds.feed_booksindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_booksindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books sorted alphabetically')}}</content>
  </entry>
  {% if current_user.check_visibility(g.constants.SIDEBAR_HOT) %}
  <entry>
    <title>{{_('Hot Books')}}</title>
    <link href="{{url_for('opds.feed_hot')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_hot')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Popular publications from this catalog based on Downloads.')}}</content>
  </entry>
  {%endif %}  
  {% if current_user.check_visibility(g.constants.SIDEBAR_BEST_RATED) %}
  <entry>
    <title>{{_('Top Rated Books')}}</title>
    <link href="{{url_for('opds.feed_best_rated')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_best_rated')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Popular publications from this catalog based on Rating.')}}</content>
  </entry>
  {%endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_RECENT) %}
  <entry>
    <title>{{_('Recently added Books')}}</title>
    <link href="{{url_for('opds.feed_new')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_new')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('The latest Books')}}</content>
  </entry>
  {%endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_RANDOM) %}
  <entry>
    <title>{{_('Random Books')}}</title>
    <link href="{{url_for('opds.feed_discover')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_discover')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Show Random Books')}}</content>
  </entry>
  {%endif %}  
  {% if current_user.check_visibility(g.constants.SIDEBAR_READ_AND_UNREAD) and not current_user.is_anonymous %}
  <entry>
    <title>{{_('Read Books')}}</title>
    <link href="{{url_for('opds.feed_read_books')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_read_books')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Read Books')}}</content>
  </entry>
  <entry>
    <title>{{_('Unread Books')}}</title>
    <link href="{{url_for('opds.feed_unread_books')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_unread_books')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Unread Books')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_AUTHOR) %}
  <entry>
    <title>{{_('Authors')}}</title>
    <link href="{{url_for('opds.feed_authorindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_authorindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by Author')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_PUBLISHER) %}
   <entry>
    <title>{{_('Publishers')}}</title>
    <link href="{{url_for('opds.feed_publisherindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_publisherindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by publisher')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_CATEGORY) %}
  <entry>
    <title>{{_('Categories')}}</title>
    <link href="{{url_for('opds.feed_categoryindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_categoryindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by category')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_SERIES) %}
  <entry>
    <title>{{_('Series')}}</title>
    <link href="{{url_for('opds.feed_seriesindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_seriesindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by series')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_LANGUAGE) %}
  <entry>
    <title>{{_('Languages')}}</title>
    <link href="{{url_for('opds.feed_languagesindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_languagesindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by Languages')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_RATING) %}
  <entry>
    <title>{{_('Ratings')}}</title>
    <link href="{{url_for('opds.feed_ratingindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_ratingindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by Rating')}}</content>
  </entry>
  {% endif %}
  {% if current_user.check_visibility(g.constants.SIDEBAR_FORMAT) %}
  <entry>
    <title>{{_('File formats')}}</title>
    <link href="{{url_for('opds.feed_formatindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_formatindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books ordered by file formats')}}</content>
  </entry>
 {% endif %}
 {% if current_user.is_authenticated or g.allow_anonymous %}  
  <entry>
    <title>{{_('Shelves')}}</title>
    <link  href="{{url_for('opds.feed_shelfindex')}}" type="application/atom+xml;profile=opds-catalog"/>
    <id>{{url_for('opds.feed_shelfindex')}}</id>
    <updated>{{ current_time }}</updated>
    <content type="text">{{_('Books organized in shelves')}}</content>
  </entry>
  {% endif %}
</feed>
