<!DOCTYPE html>
<html lang="{{ current_user.locale }}">
  <head>
    <title>{{instance}} | {{title}}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    {% if g.google_site_verification|length > 0 %}
      <meta name="google-site-verification" content="{{g.google_site_verification}}">
    {% endif %}      

    <!-- Bootstrap -->
    <link rel="apple-touch-icon" sizes="140x140" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="{{ url_for('static', filename='css/libs/bootstrap.min.css') }}" rel="stylesheet" media="screen">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet" media="screen">
    {% if g.current_theme == 1 %}
       <link href="{{ url_for('static', filename='css/caliBlur.css') }}" rel="stylesheet" media="screen">
       <link href="{{ url_for('static', filename='css/caliBlur_override.css') }}" rel="stylesheet" media="screen">
    {% endif %}
    {% block header %}{% endblock %}
  </head>
  <body class="{{ page }} shelf-down">
{% block body %}
<div class="discover">
  <h2>{{title}}</h2>
  <div class="row display-flex">

    {% for entry in entries %}
    <div class="col-sm-3 col-lg-2 col-xs-6 book">

      <div class="meta">
        <p title="{{entry.Books.title}}" class="title">{{entry.Books.title|shortentitle}}</p>
        <p class="author">
          {% for author in entry.Books.authors %}
            <a href="{{url_for('web.books_list',  data='author', sort_param='stored', book_id=author.id) }}">{{author.name.replace('|',',')}}</a>
            {% if not loop.last %}
              &amp;
            {% endif %}
          {% endfor %}
        </p>
        {% if entry.Books.series.__len__() > 0 %}
        <p class="series">
          <a href="{{url_for('web.books_list', data='series', sort_param='stored', book_id=entry.Books.series[0].id )}}">
            {{entry.Books.series[0].name}}
          </a>
          ({{entry.Books.series_index|formatfloat(2)}})
        </p>
        {% endif %}
      </div>

	  <div class="btn-group" role="group" aria-label="Download, send to eReader, reading">
          {% if current_user.role_download() %}
            {% if entry.Books.data|length %}
            <div class="btn-group" role="group">
                {% for format in entry.Books.data %}
                <a href="{{ url_for('web.download_link', book_id=entry.Books.id, book_format=format.format|lower, anyname=entry.Books.id|string+'.'+format.format|lower|replace('kepub', 'kepub.epub')) }}" id="btnGroupDrop{{entry.Books.id}}{{format.format|lower}}" class="btn btn-primary" role="button">
                  <span class="glyphicon glyphicon-download"></span>{{format.format}} ({{ format.uncompressed_size|filesizeformat }})
                </a>
                {% endfor %}
            </div>
            {% endif %}
          {% endif %}
       </div>
    </div>
    {% endfor %}
  </div>
</div>
    <script src="{{ url_for('static', filename='js/libs/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/bootstrap.min.js') }}"></script>
{% endblock %}
  </body>
</html>
