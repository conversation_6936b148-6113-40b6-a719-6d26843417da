<!DOCTYPE html>
<html class="http-error">
  <head>
    <title>{{ instance }} | HTTP Error ({{ error_code }})</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    {% if g.google_site_verification|length > 0 %}
      <meta name="google-site-verification" content="{{g.google_site_verification}}">
    {% endif %}    

    <!-- Bootstrap -->
    <link rel="apple-touch-icon" sizes="140x140" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="{{ url_for('static', filename='css/libs/bootstrap.min.css') }}" rel="stylesheet" media="screen">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet" media="screen">
    {% if g.current_theme == 1 %}
       <link href="{{ url_for('static', filename='css/caliBlur.css') }}" rel="stylesheet" media="screen">
    {% endif %}
  </head>
  <body>
  <div class="container-fluid" style="overflow-y: auto">
  <div class="row">
    <div class="col">
        <h1  class="text-center">{{instance}}</h1>
        <h1  class="text-center">{{ error_code }}</h1>
        <h3>{{ error_name }}</h3>
    </div>
  </div>
  <div class="row">
    <div class="col-md-offset-4 text-left">
        {% if unconfigured %}
            <div>{{_('Calibre-Web Instance is unconfigured, please contact your administrator')}}</div>
        {% endif %}
      {% for element in error_stack %}
        <div>{{ element }}</div>
      {% endfor %}
    </div>
   </div>
  {% if issue %}
    <div class="row">
    <div class="col errorlink">Please report this issue with all related information:
        <a href="https://github.com/janeczku/calibre-web/issues/new/choose">{{_('Create Issue')}}</a>
    </div>
      </div>
    {% endif %}
    <div class="row">
      <div class="col errorlink">
      {% if not unconfigured %}
        {% if goto_admin %}
           <a href="{{url_for('admin.db_configuration')}}" title="{{ _('Return to Database config') }}">{{_('Return to Database config')}}</a>
        {%  else %}
           <a href="{{url_for('web.index')}}" title="{{ _('Return to Home') }}">{{_('Return to Home')}}</a>
        {% endif %}
      {% else %}
        <a href="{{url_for('web.logout')}}" title="{{ _('Logout User') }}">{{ _('Logout User') }}</a>
      {% endif %}
      </div>
    </div>
  </div>
  </body>
</html>
