<!DOCTYPE html>
<html lang="{{ current_user.locale }}">

<head>
<title>{{instance}} | {{title}}</title>
<meta charset="utf-8">
<meta name='viewport' content='initial-scale=1,maximum-scale=5,user-scalable=no' />
<link href="{{ url_for('static', filename='css/basic.css') }}" rel="stylesheet" media="screen">
</head>

<body>
  <div>
    <div>
      {% if current_user.is_authenticated or g.allow_anonymous %}
        <nav>
          <a href="{{url_for('basic.index')}}">
            <span><h1>{{_('Home')}}</h1></span>
          </a>
          <div class="search">
            <form role="search" action="{{url_for('basic.index')}}" method="GET">
              <input type="text" id="query" name="query" placeholder="{{_('Search Library')}}" value="{{ searchterm }}">
              <span>
                <button type="submit" id="query_submit">{{_('Search')}}</button>
              </span>
            </form>
          </div>
          {% if not current_user.is_anonymous %}
            <a href="{{url_for('web.logout')}}">
              <span>{{_('Logout')}}</span>
            </a>
          {% endif %}
        </nav>
        <div class="theme">
                  <a href="{{url_for('web.index')}}">
            <span>{{_('Normal Theme')}}</span>
          </a>
        </div>
      {% endif %}
    </div>
  </div>
  <div class="body">
    {% block body %}
    {% endblock %}
  </div>
</body>

</html>
