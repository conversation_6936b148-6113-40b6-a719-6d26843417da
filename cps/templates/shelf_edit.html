{% extends "layout.html" %}
{% block body %}
<div class="discover">
  <h1>{{title}}</h1>
  <form role="form" method="POST">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-group">
      <label for="title">{{_('Title')}}</label>
      <input type="text" class="form-control" name="title" id="title" value="{{ shelf.name if shelf.name != None }}">
    </div>
    {% if current_user.role_edit_shelfs() %}
      <div class="checkbox">
        <label>
          <input type="checkbox" name="is_public" {% if shelf.is_public == 1 %}checked{% endif %}> {{_('Share with Everyone')}}
        </label>
      </div>
    {% endif %}
    {% if kobo_sync_enabled and sync_only_selected_shelves %}
      <div class="checkbox">
          <label> <input type="checkbox" name="kobo_sync" {% if shelf.kobo_sync == 1 %}checked{% endif %}>
              {{ _('Sync this shelf with Kobo device') }}
          </label>
      </div>
    {% endif %}
    <button type="submit" class="btn btn-default" id="submit">{{_('Save')}}</button>
    {% if shelf.id != None %}
      <a href="{{ url_for('shelf.show_shelf', shelf_id=shelf.id) }}" class="btn btn-default">{{_('Cancel')}}</a>
    {% endif %}
  </form>
</div>
{% endblock %}
