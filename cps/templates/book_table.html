{% extends "layout.html" %}
{% macro text_table_row(parameter, edit_text, show_text, validate, sort) -%}
<th data-field="{{ parameter }}" id="{{ parameter }}"
    {% if sort %}data-sortable="true" {% endif %}
    data-visible = "{{visiblility.get(parameter)}}"
    data-escape="true"
    {% if current_user.role_edit() %}
        data-editable-type="text"
        data-editable-url="{{ url_for('edit-book.edit_list_book', param=parameter)}}"
        data-editable-title="{{ edit_text }}"
        data-edit="true"
        {% if validate %}data-edit-validate="{{ _('This Field is Required') }}" {% endif %}
    {% endif %}
>{{ show_text }}</th>
{%- endmacro %}

{% macro book_checkbox_row(parameter, show_text, sort) -%}
<th data-name="{{parameter}}" data-field="{{parameter}}"
    {% if sort %}data-sortable="true" {% endif %}
    data-visible="{{visiblility.get(parameter)}}"
    data-formatter="bookCheckboxFormatter">
    {{show_text}}
</th>
{%- endmacro %}


{% block header %}
<link href="{{ url_for('static', filename='css/libs/bootstrap-table.min.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='css/libs/bootstrap-editable.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='css/libs/bootstrap-wysihtml5-0.0.3.css') }}" rel="stylesheet">
{% endblock %}
{% block body %}
<h2 class="{{page}}">{{_(title)}}</h2>
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
      <div class="col-xs-12 col-sm-6">
        <div class="row form-group">
          <div class="btn btn-default disabled" id="merge_books" aria-disabled="true">{{_('Merge selected books')}}</div>
          <div class="btn btn-default disabled" id="delete_selection" aria-disabled="true">{{_('Remove Selections')}}</div>
        </div>
        <div class="row form-group">
            <div class="btn btn-default disabled" id="table_xchange" ><span class="glyphicon glyphicon-arrow-up"></span><span class="glyphicon glyphicon-arrow-down"></span>{{_('Exchange author and title')}}</div>
        </div>
      </div>
     <div class="filterheader col-xs-12 col-sm-6">
      <div class="row form-group">
        <input type="checkbox" id="autoupdate_titlesort" name="autoupdate_titlesort" checked>
        <label for="autoupdate_titlesort">{{_('Update Title Sort automatically')}}</label>
      </div>
      <div class="row form-group">
        <input type="checkbox" id="autoupdate_authorsort" name="autoupdate_authorsort" checked>
        <label for="autoupdate_authorsort">{{_('Update Author Sort automatically')}}</label>
      </div>
    </div>

    <table id="books-table" class="table table-no-bordered table-striped"
           data-url="{{url_for('web.list_books')}}" data-locale="{{ current_user.locale }}">
      <thead>
        <tr>
          {% if current_user.role_edit() %}
            <th data-field="state" data-checkbox="true" data-sortable="true"></th>
          {% endif %}
            <th data-field="id" id="id" data-visible="false" data-switchable="false"></th>
            {{ text_table_row('title', _('Enter Title'),_('Title'), true, true) }}
            {{ text_table_row('sort', _('Enter Title Sort'),_('Title Sort'), false, true) }}
            {{ text_table_row('author_sort', _('Enter Author Sort'),_('Author Sort'), false, true) }}
            {{ text_table_row('authors', _('Enter Authors'),_('Authors'), true, true) }}
            {{ text_table_row('tags', _('Enter Categories'),_('Categories'), false, true) }}
            {{ text_table_row('series', _('Enter Series'),_('Series'), false, true) }}
            <th data-field="series_index" id="series_index" data-visible="{{visiblility.get('series_index')}}"  data-formatter="seriesIndexFormatter" data-edit-validate="{{ _('This Field is Required') }}" data-sortable="true" {% if current_user.role_edit() %} data-editable-type="number" data-editable-placeholder="1" data-editable-step="0.01" data-editable-min="0" data-editable-url="{{ url_for('edit-book.edit_list_book', param='series_index')}}" data-edit="true" data-editable-title="{{_('Enter Title')}}"{% endif %}>{{_('Series Index')}}</th>
            {{ text_table_row('languages', _('Enter Languages'),_('Languages'), false, true) }}
            <!--th data-field="pubdate" data-type="date" data-visible="{{visiblility.get('pubdate')}}" data-viewformat="dd.mm.yyyy" id="pubdate" data-sortable="true">{{_('Publishing Date')}}</th-->
            {{ text_table_row('publishers', _('Enter Publishers'),_('Publishers'), false, true) }}
            <th data-field="comments" id="comments" data-escape="true" data-editable-mode="popup"  data-visible="{{visiblility.get('comments')}}" data-sortable="false" {% if current_user.role_edit() %} data-editable-type="wysihtml5" data-editable-url="{{ url_for('edit-book.edit_list_book', param='comments')}}" data-edit="true" data-editable-title="{{_('Enter comments')}}"{% endif %}>{{_('Comments')}}</th>
            {% if current_user.check_visibility(32768) %}
                {{ book_checkbox_row('is_archived', _('Archive Status'), false)}}
            {%  endif %}
            {{ book_checkbox_row('read_status', _('Read Status'), false)}}
            {% for c in cc %}
              {% if c.datatype == "int" %}
                <th data-field="custom_column_{{ c.id|string }}" id="custom_column_{{ c.id|string }}" data-visible="{{visiblility.get('custom_column_'+ c.id|string)}}" data-sortable="false" {% if current_user.role_edit() %} data-editable-type="number" data-editable-placeholder="1" data-editable-step="1" data-editable-url="{{ url_for('edit-book.edit_list_book', param='custom_column_'+ c.id|string)}}" data-edit="true" data-editable-title="{{_('Enter ') + c.name}}"{% endif %}>{{c.name}}</th>
              {% elif c.datatype == "rating" %}
                <th data-field="custom_column_{{ c.id|string }}" id="custom_column_{{ c.id|string }}" data-formatter="ratingFormatter" data-visible="{{visiblility.get('custom_column_'+ c.id|string)}}" data-sortable="false" {% if current_user.role_edit() %} data-editable-type="number" data-editable-placeholder="1" data-editable-step="0.5" data-editable-step="1" data-editable-min="1" data-editable-max="5" data-editable-url="{{ url_for('edit-book.edit_list_book', param='custom_column_'+ c.id|string)}}" data-edit="true" data-editable-title="{{_('Enter ') + c.name}}"{% endif %}>{{c.name}}</th>
              {% elif c.datatype == "float" %}
                <th data-field="custom_column_{{ c.id|string }}" id="custom_column_{{ c.id|string }}" data-visible="{{visiblility.get('custom_column_'+ c.id|string)}}" data-sortable="false" {% if current_user.role_edit() %} data-editable-type="number" data-editable-placeholder="1" data-editable-step="0.01" data-editable-url="{{ url_for('edit-book.edit_list_book', param='custom_column_'+ c.id|string)}}" data-edit="true" data-editable-title="{{_('Enter ') + c.name}}"{% endif %}>{{c.name}}</th>
              {% elif c.datatype == "enumeration" %}
                <th data-field="custom_column_{{ c.id|string }}" id="custom_column_{{ c.id|string }}" data-visible="{{visiblility.get('custom_column_'+ c.id|string)}}" data-sortable="false" {% if current_user.role_edit() %} data-editable-type="select" data-editable-source={{ url_for('edit-book.table_get_custom_enum', c_id=c.id)  }} data-editable-url="{{ url_for('edit-book.edit_list_book', param='custom_column_'+ c.id|string)}}" data-edit="true" data-editable-title="{{_('Enter ') + c.name}}"{% endif %}>{{c.name}}</th>
              {% elif c.datatype in ["datetime"] %}
                  <!-- missing -->
              {% elif c.datatype == "text" %}
                 {{ text_table_row('custom_column_' + c.id|string, _('Enter ') + c.name, c.name, false, false) }}
              {% elif c.datatype == "comments" %}
                  <th data-field="custom_column_{{ c.id|string }}" id="custom_column_{{ c.id|string }}" data-escape="true" data-editable-mode="popup"  data-visible="{{visiblility.get('custom_column_'+ c.id|string)}}" data-sortable="false" {% if current_user.role_edit() %} data-editable-type="wysihtml5" data-editable-url="{{ url_for('edit-book.edit_list_book', param='custom_column_'+ c.id|string)}}" data-edit="true" data-editable-title="{{_('Enter ') + c.name}}"{% endif %}>{{c.name}}</th>
              {% elif c.datatype == "bool" %}
                  {{ book_checkbox_row('custom_column_' + c.id|string, c.name, false)}}
              {% else %}
                <!--{{ text_table_row('custom_column_' + c.id|string, _('Enter ') + c.name, c.name, false, false) }} -->
              {% endif %}
            {% endfor %}
          {% if current_user.role_delete_books() and current_user.role_edit()%}
            <th data-align="right" data-formatter="EbookActions" data-switchable="false">{{_('Delete')}}</th>
          {% endif %}
        </tr>
      </thead>
    </table>
{% endblock %}
{% block modal %}
{{ delete_book(current_user.role_delete_books()) }}
{% if current_user.role_edit() %}
<div class="modal fade" id="mergeModal" role="dialog" aria-labelledby="metaMergeLabel">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-center">
          <span>{{_('Are you really sure?')}}</span>
      </div>
        <div class="modal-body">
          <p></p>
            <div class="text-left">{{_('Books with Title will be merged from:')}}</div>
          <p></p>
            <div class="text-left" id="merge_from"></div>
          <p></p>
            <div class="text-left">{{_('Into Book with Title:')}}</div>
          <p></p>
            <div class="text-left" id="merge_to"></div>
        </div>
      <div class="modal-footer">
        <input id="merge_confirm"  type="button" class="btn btn-danger" value="{{_('Merge')}}" name="merge_confirm" id="merge_confirm" data-dismiss="modal">
        <button id="merge_abort" type="button" class="btn btn-default" data-dismiss="modal">{{_('Cancel')}}</button>
      </div>
    </div>
  </div>
</div>
{% endif %}

{% endblock %}
{% block js %}
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-table.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-table-locale-all.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-table-editable.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-editable.min.js') }}"></script>
{% if not current_user.locale == 'en' %}
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/locale/bootstrap-table-' + current_user.locale + '.min.js') }}" charset="UTF-8"></script>
{% endif %}
<script src="{{ url_for('static', filename='js/libs/wysihtml5-0.3.0.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-wysihtml5-0.0.3.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/wysihtml5-0.0.3.js') }}"></script>
<script src="{{ url_for('static', filename='js/table.js') }}"></script>
{% endblock %}
