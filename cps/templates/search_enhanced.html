{% extends "layout.html" %}
{% block body %}
<div class="discover">
  <h1>{{_('Enhanced Search')}}</h1>
  
  <div class="row">
    <div class="col-md-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">{{_('Search Filters')}}</h3>
        </div>
        <div class="panel-body">
          <form id="enhanced-search-form">
            <div class="row">
              <!-- Text Search Fields -->
              <div class="col-md-6">
                <div class="form-group">
                  <label for="title">{{_('Title')}}</label>
                  <div class="input-group">
                    <input type="text" class="form-control" id="title" name="title" placeholder="{{_('Enter title...')}}" autocomplete="off">
                    <div class="input-group-btn">
                      <select class="form-control" id="title_operator" name="title_operator" style="width: auto;">
                        <option value="contains">{{_('Contains')}}</option>
                        <option value="exact">{{_('Exact')}}</option>
                        <option value="starts_with">{{_('Starts with')}}</option>
                      </select>
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="author">{{_('Author')}}</label>
                  <div class="input-group">
                    <input type="text" class="form-control" id="author" name="author" placeholder="{{_('Enter author...')}}" autocomplete="off">
                    <div class="input-group-btn">
                      <select class="form-control" id="author_operator" name="author_operator" style="width: auto;">
                        <option value="contains">{{_('Contains')}}</option>
                        <option value="exact">{{_('Exact')}}</option>
                        <option value="starts_with">{{_('Starts with')}}</option>
                      </select>
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="description">{{_('Description')}}</label>
                  <input type="text" class="form-control" id="description" name="description" placeholder="{{_('Search in description...')}}" autocomplete="off">
                </div>
              </div>
              
              <!-- Selection Fields -->
              <div class="col-md-6">
                <div class="form-group">
                  <label for="tags">{{_('Tags')}}</label>
                  <select multiple class="form-control" id="tags" name="tags" size="4">
                    {% for tag in tags %}
                    <option value="{{tag.name}}">{{tag.name}}</option>
                    {% endfor %}
                  </select>
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" id="include_tags" name="include_tags" checked>
                      {{_('Include selected tags (uncheck to exclude)')}}
                    </label>
                  </div>
                </div>
                
                <div class="form-group">
                  <label for="series">{{_('Series')}}</label>
                  <select multiple class="form-control" id="series" name="series" size="3">
                    {% for serie in series %}
                    <option value="{{serie.name}}">{{serie.name}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="publishers">{{_('Publishers')}}</label>
                  <select multiple class="form-control" id="publishers" name="publishers" size="3">
                    {% for publisher in publishers %}
                    <option value="{{publisher.name}}">{{publisher.name}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-4">
                {% if languages %}
                <div class="form-group">
                  <label for="languages">{{_('Languages')}}</label>
                  <select multiple class="form-control" id="languages" name="languages" size="3">
                    {% for language in languages %}
                    <option value="{{language.lang_code}}">{{language.name}}</option>
                    {% endfor %}
                  </select>
                </div>
                {% endif %}
              </div>
              
              <div class="col-md-4">
                <div class="form-group">
                  <label for="formats">{{_('Formats')}}</label>
                  <select multiple class="form-control" id="formats" name="formats" size="3">
                    {% for format in formats %}
                    <option value="{{format[0]}}">{{format[0]}}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            
            <!-- Range Filters -->
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{_('Rating Range')}}</label>
                  <div class="row">
                    <div class="col-xs-6">
                      <input type="number" class="form-control" id="rating_min" name="rating_min" placeholder="{{_('Min')}}" min="0" max="5" step="0.5">
                    </div>
                    <div class="col-xs-6">
                      <input type="number" class="form-control" id="rating_max" name="rating_max" placeholder="{{_('Max')}}" min="0" max="5" step="0.5">
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{_('Publication Date Range')}}</label>
                  <div class="row">
                    <div class="col-xs-6">
                      <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-xs-6">
                      <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Sort Options -->
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="sort_field">{{_('Sort by')}}</label>
                  <select class="form-control" id="sort_field" name="sort_field">
                    <option value="relevance">{{_('Relevance')}}</option>
                    <option value="title">{{_('Title')}}</option>
                    <option value="authors">{{_('Author')}}</option>
                    <option value="pubdate">{{_('Publication Date')}}</option>
                    <option value="rating">{{_('Rating')}}</option>
                    <option value="timestamp">{{_('Date Added')}}</option>
                  </select>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-group">
                  <label for="sort_direction">{{_('Sort Direction')}}</label>
                  <select class="form-control" id="sort_direction" name="sort_direction">
                    <option value="asc">{{_('Ascending')}}</option>
                    <option value="desc">{{_('Descending')}}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <!-- Search Buttons -->
            <div class="row">
              <div class="col-md-12">
                <button type="submit" class="btn btn-primary">
                  <span class="glyphicon glyphicon-search"></span>
                  {{_('Search')}}
                </button>
                <button type="button" class="btn btn-default" id="clear-form">
                  <span class="glyphicon glyphicon-remove"></span>
                  {{_('Clear')}}
                </button>
                <button type="button" class="btn btn-info" id="save-search">
                  <span class="glyphicon glyphicon-floppy-disk"></span>
                  {{_('Save Search')}}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Search Results -->
  <div class="row" id="search-results" style="display: none;">
    <div class="col-md-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">
            {{_('Search Results')}}
            <span id="result-count" class="badge"></span>
            <span id="search-info" class="pull-right text-muted"></span>
          </h3>
        </div>
        <div class="panel-body">
          <div id="results-container">
            <!-- Results will be loaded here -->
          </div>
          
          <!-- Pagination -->
          <nav id="pagination-container" style="display: none;">
            <ul class="pagination" id="pagination">
              <!-- Pagination will be generated here -->
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Loading indicator -->
  <div id="loading" style="display: none; text-align: center; margin: 20px;">
    <div class="spinner">
      <span class="glyphicon glyphicon-refresh glyphicon-spin"></span>
      {{_('Searching...')}}
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
    // Initialize autocomplete for text fields
    $('#title, #author').on('input', function() {
        var field = $(this).attr('id');
        var query = $(this).val();
        
        if (query.length >= 2) {
            $.get('{{ url_for("search_enhanced.search_suggestions") }}', {
                q: query,
                field: field,
                limit: 10
            }, function(suggestions) {
                // Simple autocomplete implementation
                // In a real implementation, you'd use a proper autocomplete library
                console.log('Suggestions for ' + field + ':', suggestions);
            });
        }
    });
    
    // Handle form submission
    $('#enhanced-search-form').on('submit', function(e) {
        e.preventDefault();
        performSearch(1);
    });
    
    // Clear form
    $('#clear-form').on('click', function() {
        $('#enhanced-search-form')[0].reset();
        $('#search-results').hide();
    });
    
    // Save search functionality
    $('#save-search').on('click', function() {
        // Implement save search functionality
        alert('{{_("Save search functionality would be implemented here")}}');
    });
    
    function performSearch(page) {
        page = page || 1;
        
        // Collect form data
        var formData = {
            title: $('#title').val(),
            title_operator: $('#title_operator').val(),
            author: $('#author').val(),
            author_operator: $('#author_operator').val(),
            description: $('#description').val(),
            tags: $('#tags').val(),
            include_tags: $('#include_tags').is(':checked'),
            series: $('#series').val(),
            publishers: $('#publishers').val(),
            languages: $('#languages').val(),
            formats: $('#formats').val(),
            rating_min: $('#rating_min').val(),
            rating_max: $('#rating_max').val(),
            date_from: $('#date_from').val(),
            date_to: $('#date_to').val(),
            sort_field: $('#sort_field').val(),
            sort_direction: $('#sort_direction').val(),
            page: page,
            per_page: 20
        };
        
        // Show loading
        $('#loading').show();
        $('#search-results').hide();
        
        // Perform search
        $.ajax({
            url: '{{ url_for("search_enhanced.execute_enhanced_search") }}',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                displayResults(response);
            },
            error: function(xhr, status, error) {
                alert('{{_("Search failed")}}: ' + error);
            },
            complete: function() {
                $('#loading').hide();
            }
        });
    }
    
    function displayResults(response) {
        var results = response.results;
        var pagination = response.pagination;
        var searchInfo = response.search_info;
        
        // Update result count
        $('#result-count').text(pagination.total);
        
        // Update search info
        var infoText = searchInfo.elasticsearch_used ? 
            '{{_("Powered by Elasticsearch")}}' : 
            '{{_("SQL Search")}}';
        $('#search-info').text(infoText);
        
        // Display results
        var resultsHtml = '';
        if (results.length === 0) {
            resultsHtml = '<p class="text-muted">{{_("No results found")}}</p>';
        } else {
            results.forEach(function(book) {
                resultsHtml += '<div class="book-result row" style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">';
                resultsHtml += '<div class="col-md-2">';
                if (book.has_cover) {
                    resultsHtml += '<img src="/cover/' + book.id + '" class="img-responsive" style="max-height: 100px;">';
                } else {
                    resultsHtml += '<div class="no-cover" style="width: 60px; height: 80px; background: #eee; display: flex; align-items: center; justify-content: center;">{{_("No Cover")}}</div>';
                }
                resultsHtml += '</div>';
                resultsHtml += '<div class="col-md-10">';
                resultsHtml += '<h4><a href="/book/' + book.id + '">' + book.title + '</a></h4>';
                resultsHtml += '<p><strong>{{_("Authors")}}:</strong> ' + book.authors.join(', ') + '</p>';
                if (book.series) {
                    resultsHtml += '<p><strong>{{_("Series")}}:</strong> ' + book.series + '</p>';
                }
                if (book.rating > 0) {
                    resultsHtml += '<p><strong>{{_("Rating")}}:</strong> ' + book.rating + '/5</p>';
                }
                if (book.tags.length > 0) {
                    resultsHtml += '<p><strong>{{_("Tags")}}:</strong> ' + book.tags.join(', ') + '</p>';
                }
                if (book.formats.length > 0) {
                    resultsHtml += '<p><strong>{{_("Formats")}}:</strong> ' + book.formats.join(', ') + '</p>';
                }
                resultsHtml += '</div>';
                resultsHtml += '</div>';
            });
        }
        
        $('#results-container').html(resultsHtml);
        
        // Generate pagination
        if (pagination.total_pages > 1) {
            var paginationHtml = '';
            
            // Previous button
            if (pagination.has_prev) {
                paginationHtml += '<li><a href="#" onclick="performSearch(' + (pagination.page - 1) + ')">&laquo;</a></li>';
            }
            
            // Page numbers
            var startPage = Math.max(1, pagination.page - 2);
            var endPage = Math.min(pagination.total_pages, pagination.page + 2);
            
            for (var i = startPage; i <= endPage; i++) {
                if (i === pagination.page) {
                    paginationHtml += '<li class="active"><a href="#">' + i + '</a></li>';
                } else {
                    paginationHtml += '<li><a href="#" onclick="performSearch(' + i + ')">' + i + '</a></li>';
                }
            }
            
            // Next button
            if (pagination.has_next) {
                paginationHtml += '<li><a href="#" onclick="performSearch(' + (pagination.page + 1) + ')">&raquo;</a></li>';
            }
            
            $('#pagination').html(paginationHtml);
            $('#pagination-container').show();
        } else {
            $('#pagination-container').hide();
        }
        
        $('#search-results').show();
    }
    
    // Make performSearch globally available
    window.performSearch = performSearch;
});
</script>
{% endblock %}
