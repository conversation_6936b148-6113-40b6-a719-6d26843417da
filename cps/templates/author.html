{% extends "layout.html" %}
{% block body %}
<h2>{{title}}</h2>

{% if author is not none %}
<section class="author-bio">
  {%if author.image_url is not none %}
  <img title="{{author.name}}" src="{{author.image_url}}" alt="{{author.name}}" class="author-photo pull-left">
  {% endif %}

  {%if author.safe_about is not none %}
  <p>{{author.safe_about|safe}}</p>
  {% endif %}

  - {{_("via")}} <a href="{{author.link}}" class="author-link" target="_blank" rel="noopener">Goodreads</a>
</section>

<div class="clearfix"></div>
{% endif %}

<div class="discover load-more">
  {% if author is not none %}
    <h3>{{_("In Library")}}</h3>
  {% endif %}
    <div class="filterheader hidden-xs">
      <a id="new" data-toggle="tooltip" title="{{_('Sort according to book date, newest first')}}" class="btn btn-primary{% if order == "new" %} active{% endif%}" href="{{url_for('web.books_list', data='author', book_id=id, sort_param='new')}}"><span class="glyphicon glyphicon-book"></span> <span class="glyphicon glyphicon-calendar"></span><span class="glyphicon glyphicon-sort-by-order"></span></a>
      <a id="old" data-toggle="tooltip" title="{{_('Sort according to book date, oldest first')}}" class="btn btn-primary{% if order == "old" %} active{% endif%}" href="{{url_for('web.books_list', data='author', book_id=id, sort_param='old')}}"><span class="glyphicon glyphicon-book"></span> <span class="glyphicon glyphicon-calendar"></span><span class="glyphicon glyphicon-sort-by-order-alt"></span></a>
      <a id="asc" data-toggle="tooltip" title="{{_('Sort title in alphabetical order')}}" class="btn btn-primary{% if order == "abc" %} active{% endif%}" href="{{url_for('web.books_list', data='author', book_id=id, sort_param='abc')}}"><span class="glyphicon glyphicon-font"></span><span class="glyphicon glyphicon-sort-by-alphabet"></span></a>
      <a id="desc" data-toggle="tooltip" title="{{_('Sort title in reverse alphabetical order')}}" class="btn btn-primary{% if order == "zyx" %} active{% endif%}" href="{{url_for('web.books_list', data='author', book_id=id, sort_param='zyx')}}"><span class="glyphicon glyphicon-font"></span><span class="glyphicon glyphicon-sort-by-alphabet-alt"></span></a>
      <a id="pub_new" data-toggle="tooltip" title="{{_('Sort according to publishing date, newest first')}}" class="btn btn-primary{% if order == "pubnew" %} active{% endif%}" href="{{url_for('web.books_list', data='author', book_id=id, sort_param='pubnew')}}"><span class="glyphicon glyphicon-calendar"></span><span class="glyphicon glyphicon-sort-by-order"></span></a>
      <a id="pub_old" data-toggle="tooltip" title="{{_('Sort according to publishing date, oldest first')}}" class="btn btn-primary{% if order == "pubold" %} active{% endif%}" href="{{url_for('web.books_list', data='author', book_id=id, sort_param='pubold')}}"><span class="glyphicon glyphicon-calendar"></span><span class="glyphicon glyphicon-sort-by-order-alt"></span></a>
    </div>
  <div class="row display-flex">
    {% for entry in entries %}
    <div id="books" class="col-sm-3 col-lg-2 col-xs-6 book session">
      <div class="cover">
        <a href="{{ url_for('web.show_book', book_id=entry.Books.id) }}" {% if simple==false %}data-toggle="modal" data-target="#bookDetailsModal" data-remote="false"{% endif %}>
            <span class="img" title="{{entry.Books.title}}">
              {{ image.book_cover(entry.Books, alt=author.name|safe) }}
              {% if entry[2] == True %}<span class="badge read glyphicon glyphicon-ok"></span>{% endif %}
            </span>
        </a>
      </div>
      <div class="meta">
        <a href="{{ url_for('web.show_book', book_id=entry.Books.id) }}" {% if simple==false %}data-toggle="modal" data-target="#bookDetailsModal" data-remote="false"{% endif %}>
          <p title="{{ entry.Books.title }}" class="title">{{entry.Books.title|shortentitle}}</p>
        </a>
        <p class="author">
          {% for author in entry.Books.authors %}
            {% if loop.index > g.config_authors_max and g.config_authors_max != 0 %}
              {% if not loop.first %}
                <span class="author-hidden-divider">&amp;</span>
			  {% endif %}
              <a class="author-name author-hidden" href="{{url_for('web.books_list',  data='author', sort_param='stored', book_id=author.id) }}">{{author.name.replace('|',',')|shortentitle(30)}}</a>
              {% if loop.last %}
                <a href="#" class="author-expand" data-authors-max="{{g.config_authors_max}}" data-collapse-caption="({{_('reduce')}})">(...)</a>
              {% endif %}
            {% else %}
              {% if not loop.first %}
                <span>&amp;</span>
              {% endif %}
              <a class="author-name" href="{{url_for('web.books_list',  data='author', sort_param='stored', book_id=author.id) }}">{{author.name.replace('|',',')|shortentitle(30)}}</a>
            {% endif %}
          {% endfor %}
          {% if entry.Books.data|music %}
            <span class="glyphicon glyphicon-music"></span>
          {% endif %}
        </p>
        {% if entry.Books.series.__len__() > 0 %}
        <p class="series">
          <a href="{{url_for('web.books_list', data='series', sort_param='stored', book_id=entry.Books.series[0].id )}}">
            {{entry.Books.series[0].name}}
          </a>
          ({{entry.Books.series_index|formatfloat(2)}})
        </p>
        {% endif %}
        {% if entry.Books.ratings.__len__() > 0 %}
        <div class="rating">
          {% for number in range((entry.Books.ratings[0].rating/2)|int(2)) %}
          <span class="glyphicon glyphicon-star good"></span>
          {% if loop.last and loop.index < 5 %}
          {% for numer in range(5 - loop.index) %}
          <span class="glyphicon glyphicon-star-empty"></span>
          {% endfor %}
          {% endif %}
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
    {% endfor %}
  </div>
</div>

{% if other_books and author is not none %}
<div class="discover">
  <h3>{{_("More by")}} {{ author.name.replace('|',',') }}</h3>
  <div class="row">
    {% for entry in other_books %}
    <div class="col-sm-3 col-lg-2 col-xs-6 book session">
      <div class="cover">
        <a href="https://www.goodreads.com/book/show/{{ entry.gid['#text'] }}" target="_blank" rel="noopener">
          <img title="{{entry.title}}" src="{{ entry.image_url }}" />
        </a>
      </div>
      <div class="meta">
        <p title="{{ entry.title }}" class="title">{{entry.title|shortentitle}}</p>
        <p class="author">
		  {% for author in entry.authors %}
			{% if loop.index > g.config_authors_max and g.config_authors_max != 0 %}
				<a class="author-name author-hidden" href="https://www.goodreads.com/author/show/{{ author.gid }}" target="_blank" rel="noopener">{{author.name.replace('|',',')}}</a>
				{% if loop.last %}
					<a href="#" class="author-expand" data-authors-max="{{g.config_authors_max}}" data-collapse-caption="({{_('reduce')}})">(...)</a>
				{% endif %}
			{% else %}
				<a class="author-name" href="https://www.goodreads.com/author/show/{{ author.gid }}" target="_blank" rel="noopener">{{author.name.replace('|',',')}}</a>
		    {% endif %}
          {% endfor %}
        </p>
        {% if entry.series.__len__() > 0 %}
        <p class="series">
          <a href="{{url_for('web.books_list', data='series', sort_param='stored', book_id=entry.series[0].id )}}">
            {{entry.series[0].name}}
          </a>
          ({{entry.series_index|formatfloat(2)}})
        </p>
        {% endif %}
        <div class="rating">
          {% for number in range((entry.average_rating)|float|round|int(2)) %}
          <span class="glyphicon glyphicon-star good"></span>
          {% if loop.last and loop.index < 5 %}
          {% for numer in range(5 - loop.index) %}
          <span class="glyphicon glyphicon-star-empty"></span>
          {% endfor %}
          {% endif %}
          {% endfor %}
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  <a href="{{author.link}}" class="author-link" target="_blank" rel="noopener">
    <img src="{{ url_for('static', filename='img/goodreads.svg') }}" alt="Goodreads">
  </a>
</div>
{% endif %}
{% endblock %}
