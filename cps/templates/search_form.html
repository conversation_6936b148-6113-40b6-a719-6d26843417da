{% extends "layout.html" %}
{% block body %}
<h1 class="{{page}}">{{title}}</h1>
<div class="col-md-10 col-lg-6">
  <form role="form" id="search" action="{{ url_for('search.advanced_search_form') }}" method="POST">
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
    <div class="form-group">
      <label for="title">{{_('Book Title')}}</label>
      <input type="text" class="form-control" name="title" id="title" value="">
    </div>
    <div class="form-group">
      <label for="authors">{{_('Author')}}</label>
      <input type="text" class="form-control typeahead" name="authors" id="authors" value="" autocomplete="off">
    </div>
    <div class="form-group">
      <label for="Publisher">{{_('Publisher')}}</label>
      <input type="text" class="form-control" name="publisher" id="publisher" value="">
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label for="publishstart">{{_('Published Date From')}}</label>
        <div class="input-group">
          <input type="text" class="datepicker form-control" name="publishstart" id="publishstart" value="">
          <input type="text" class="form-control fake-input hidden" id="fake_publishstart" value="">
          <span class="input-group-btn">
            <button type="button" id="publishstart_delete" class="datepicker_delete btn btn-default"><span class="glyphicon glyphicon-remove-circle"></span></button>
          </span>
        </div>
      </div>
      <div class="form-group col-sm-6">
        <label for="publishend">{{_('Published Date To')}}</label>
        <div class="input-group ">
          <input type="text" class="datepicker form-control" name="publishend" id="publishend" value="">
          <input type="text" class="form-control fake-input hidden" id="fake_publishend" value="">
          <span class="input-group-btn">
            <button type="button" id="publishend_delete" class="datepicker_delete btn btn-default"><span class="glyphicon glyphicon-remove-circle"></span></button>
          </span>
        </div>
      </div>
    </div>
    <div class="form-group">
      <label for="read_status">{{_('Read Status')}}</label>
      <select name="read_status" id="read_status" class="form-control">
        <option value="Any" selected>{{_('Any')}}</option>
        <option value="">{{_('Empty')}}</option>
        <option value="True" >{{_('Yes')}}</option>
        <option value="False" >{{_('No')}}</option>
      </select>
    </div>
    <div class="row">
      <div class="form-group col-sm-6" id="tag">
        <div><label for="include_tag">{{_('Tags')}}</label></div>
        <select class="selectpicker" name="include_tag" id="include_tag" data-live-search="true" data-style="btn-primary" data-dropup-auto="false" multiple>
          {% for tag in tags %}
          <option class="tags_click" value="{{tag.id}}">{{tag.name}}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group col-sm-6">
        <div><label for="exclude_tag">{{_('Exclude Tags')}}</label></div>
        <select class="selectpicker" name="exclude_tag" id="exclude_tag" data-live-search="true" data-style="btn-danger" data-dropup-auto="false" multiple>
          {% for tag in tags %}
          <option  class="tags_click" value="{{tag.id}}">{{tag.name}}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <div><label for="include_serie">{{_('Series')}}</label></div>
        <select class="selectpicker" name="include_serie" id="include_serie" data-live-search="true" data-style="btn-primary" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for serie in series %}
          <option value="{{serie.id}}">{{serie.name}}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group col-sm-6">
        <div><label for="exclude_serie">{{_('Exclude Series')}}</label></div>
        <select class="selectpicker" name="exclude_serie" id="exclude_serie" data-live-search="true" data-style="btn-danger" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for serie in series %}
          <option value="{{serie.id}}">{{serie.name}}</option>
          {% endfor %}
        </select>
      </div>
    </div>
     <div class="row">
      <div class="form-group col-sm-6">
        <div><label for="include_shelf">{{_('Shelves')}}</label></div>
        <select class="selectpicker" name="include_shelf" id="include_shelf" data-live-search="true" data-style="btn-primary" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for shelf in shelves %}
          <option value="{{shelf.id}}">{{shelf.name}}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group col-sm-6">
        <div><label for="exclude_shelf">{{_('Exclude Shelves')}}</label></div>
        <select class="selectpicker" name="exclude_shelf" id="exclude_shelf" data-live-search="true" data-style="btn-danger" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for shelf in shelves %}
          <option value="{{shelf.id}}">{{shelf.name}}</option>
          {% endfor %}
        </select>
      </div>
    </div>

    {% if languages %}
    <div class="row">
      <div class="form-group col-sm-6">
        <div><label for="include_language">{{_('Languages')}}</label></div>
        <select class="selectpicker" name="include_language" id="include_language" data-live-search="true" data-style="btn-primary" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for language in languages %}
          <option value="{{language.id}}">{{language.name}}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group col-sm-6">
        <div><label for="exclude_language">{{_('Exclude Languages')}}</label></div>
        <select class="selectpicker" name="exclude_language" id="exclude_language" data-live-search="true" data-style="btn-danger" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for language in languages %}
          <option value="{{language.id}}">{{language.name}}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    {% endif%}
    <div class="row">
      <div class="form-group col-sm-6">
        <div><label for="include_extension">{{_('Extensions')}}</label></div>
        <select class="selectpicker" name="include_extension" id="include_extension" data-live-search="true" data-style="btn-primary" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for extension in extensions %}
          <option value="{{extension.format}}">{{extension.format}}</option>
          {% endfor %}
        </select>
      </div>
      <div class="form-group col-sm-6">
        <div><label for="exclude_extension">{{_('Exclude Extensions')}}</label></div>
        <select class="selectpicker" name="exclude_extension" id="exclude_extension" data-live-search="true" data-style="btn-danger" data-dropup-auto="false" data-actions-box="true" multiple>
          {% for extension in extensions %}
          <option value="{{extension.format}}">{{extension.format}}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label for="ratinghigh">{{_('Rating Above')}}</label>
        <input type="number"  name="ratinghigh" id="ratinghigh" class="rating input-lg" data-clearable="" >
      </div>
        <div class="form-group col-sm-6">
          <label for="ratinglow">{{_('Rating Below')}}</label>
          <input type="number"  name="ratinglow" id="ratinglow" class="rating input-lg" data-clearable="" >
        </div>
    </div>
    <div class="form-group">
      <label for="comments">{{_('Description')}}</label>
      <input type="text" class="form-control" name="comments" id="comments" value="">
    </div>

    {% if cc|length > 0 %}
    {% for c in cc %}
    <div class="form-group">
      <!--input type="number" step="1" class="form-control" name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}" value=""-->
      <label for="{{ 'custom_column_' ~ c.id }}">{{ c.name }}</label>
      {% if c.datatype == 'bool' %}
      <select name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}" class="form-control">
        <option value="Any" selected>{{_('Any')}}</option>
        <option value="">{{_('Empty')}}</option>
        <option value="True" >{{_('Yes')}}</option>
        <option value="False" >{{_('No')}}</option>
      </select>
      {% endif %}

      {% if c.datatype == 'int' %}
        <div class="row">
          <div class="form-group col-sm-6">
            <label for="{{ 'custom_column_' ~ c.id }}_low">{{_('From:')}}</label>
              <input type="number" step="1" class="form-control" name="{{ 'custom_column_' ~ c.id }}_low" id="{{ 'custom_column_' ~ c.id }}_low" value="">
            </div>
          <div class="form-group col-sm-6">
            <label for="{{ 'custom_column_' ~ c.id }}_high">{{_('To:')}}</label>
              <input type="number" step="1" class="form-control" name="{{ 'custom_column_' ~ c.id }}_high" id="{{ 'custom_column_' ~ c.id }}_high" value="">
          </div>
        </div>
      {% endif %}
      {% if c.datatype == 'float' %}
        <div class="row">
          <div class="form-group col-sm-6">
            <label for="{{ 'custom_column_' ~ c.id }}_low">{{_('From:')}}</label>
              <input type="number" step="0.01" class="form-control" name="{{ 'custom_column_' ~ c.id }}_low" id="{{ 'custom_column_' ~ c.id }}_low" value="">
            </div>
          <div class="form-group col-sm-6">
            <label for="{{ 'custom_column_' ~ c.id }}_high">{{_('To:')}}</label>
              <input type="number" step="0.01" class="form-control" name="{{ 'custom_column_' ~ c.id }}_high" id="{{ 'custom_column_' ~ c.id }}_high" value="">
          </div>
        </div>
      <!--input type="number" step="0.01" class="form-control" name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}" value=""-->
      {% endif %}

      {% if c.datatype == 'datetime' %}
      <div class="row">
        <div class="form-group col-sm-6">
          <label for="{{ 'custom_column_' ~ c.id }}">{{_('From:')}}</label>
          <div class="input-group">
            <input type="text" class="datepicker form-control" name="{{ 'custom_column_' ~ c.id }}_start" id="{{ 'custom_column_' ~ c.id }}_start" value="">
            <input type="text" class="form-control fake-input hidden" id="fake_{{ 'custom_column_' ~ c.id }}_start" value="">
            <span class="input-group-btn">
              <button type="button" id="{{ 'custom_column_' ~ c.id }}_start_delete" class="datepicker_delete btn btn-default"><span class="glyphicon glyphicon-remove-circle"></span></button>
            </span>
          </div>
        </div>
        <div class="form-group col-sm-6">
          <label for="{{ 'custom_column_' ~ c.id }}">{{_('To:')}}</label>
          <div class="input-group ">
            <input type="text" class="datepicker form-control" name="{{ 'custom_column_' ~ c.id }}_end" id="{{ 'custom_column_' ~ c.id }}_end" value="">
            <input type="text" class="form-control fake-input hidden" id="fake_{{ 'custom_column_' ~ c.id }}_end" value="">
            <span class="input-group-btn">
              <button type="button" id="{{ 'custom_column_' ~ c.id }}_end_delete" class="datepicker_delete btn btn-default"><span class="glyphicon glyphicon-remove-circle"></span></button>
            </span>
          </div>
        </div>
      </div>
      {% endif %}

      {% if c.datatype in ['text', 'series', 'comments'] and not c.is_multiple %}
      <input type="text" class="form-control" name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}" value="">
      {% endif %}

      {% if c.datatype in ['text', 'series'] and c.is_multiple %}
      <input type="text" class="form-control" name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}" value="">
      {% endif %}

      {% if c.datatype == 'enumeration' %}
      <select class="form-control" name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}">
        <option></option>
        {% for opt in c.get_display_dict().enum_values %}
        <option>{{ opt }}</option>
        {% endfor %}
      </select>
      {% endif %}

      {% if c.datatype == 'rating' %}
      <input type="number" min="1" max="5" step="0.5" class="form-control" name="{{ 'custom_column_' ~ c.id }}" id="{{ 'custom_column_' ~ c.id }}">
      {% endif %}
    </div>
    {% endfor %}
    {% endif %}

    <button type="submit" id="adv_submit" class="btn btn-default">{{_('Search')}}</button>
  </form>
</div>
{% endblock %}

{% block js %}
<script>
  var language = '{{ current_user.locale }}';
</script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-datepicker/bootstrap-datepicker.min.js') }}"></script>
{% if not current_user.locale == 'en' %}
<script src="{{ url_for('static', filename='js/libs/bootstrap-datepicker/locales/bootstrap-datepicker.' + current_user.locale + '.min.js') }}" charset="UTF-8"></script>
{% endif %}
<script src="{{ url_for('static', filename='js/libs/bootstrap-rating-input.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/typeahead.bundle.js') }}"></script>
<script src="{{ url_for('static', filename='js/edit_books.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-select.min.js')}}"></script>
{% if not current_user.locale == 'en' %}
<script src="{{ url_for('static', filename='js/libs/bootstrap-select/defaults-' + current_user.locale + '.min.js') }}" charset="UTF-8"></script>
{% endif %}
{% endblock %}
{% block header %}
<link href="{{ url_for('static', filename='css/libs/typeahead.css') }}" rel="stylesheet" media="screen">
<link href="{{ url_for('static', filename='css/libs/bootstrap-datepicker3.min.css') }}" rel="stylesheet" media="screen">
<link href="{{ url_for('static', filename='css/libs/bootstrap-select.min.css') }}" rel="stylesheet" >
{% endblock %}
