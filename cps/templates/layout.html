{% from 'modal_dialogs.html' import restrict_modal, delete_book, filechooser_modal, delete_confirm_modal, change_confirm_modal %}
{% import 'image.html' as image %}
<!DOCTYPE html>
<html lang="{{ current_user.locale }}">
  <head>
    <title>{{instance}} | {{title}}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    {% if g.google_site_verification|length > 0 %}
      <meta name="google-site-verification" content="{{g.google_site_verification}}">
    {% endif %}
    <!-- Bootstrap -->
    <link rel="apple-touch-icon" sizes="140x140" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="{{ url_for('static', filename='css/libs/bootstrap.min.css') }}" rel="stylesheet" media="screen">
    {% block header %}{% endblock %}
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet" media="screen">
    <link href="{{ url_for('static', filename='css/upload.css') }}" rel="stylesheet" media="screen">
    {% if g.current_theme == 1 %}
       <link href="{{ url_for('static', filename='css/caliBlur.css') }}" rel="stylesheet" media="screen">
       <link href="{{ url_for('static', filename='css/caliBlur_override.css') }}" rel="stylesheet" media="screen">
    {% endif %}
  </head>
  <body class="{{ page }} {{ bodyClass }}{% if g.current_theme == 1 %} blur{% endif %}" data-text="{{_('Home')}}" data-textback="{{_('Back')}}">
    <!-- Static navbar -->
    <div class="navbar navbar-default navbar-static-top" role="navigation">
      <div class="container-fluid">
        <div class="navbar-header">
          <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
            <span class="sr-only">{{_('Toggle Navigation')}}</span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
          </button>
          <a class="navbar-brand" href="{{url_for('web.index')}}">{{instance}}</a>
        </div>
        {% if g.current_theme == 1 %}
            <div class="home-btn"><a class="home-btn-tooltip" href="{{url_for("web.index", page=1)}}" data-toggle="tooltip" title="" data-placement="bottom" data-original-title="Home"></a></div>
            <div class="plexBack"><a href="{{url_for('web.index')}}"></a></div>
        {% endif %}
        {% if current_user.is_authenticated or g.allow_anonymous %}
          <!--# margin 0, padding 15, background color-->
           <form class="navbar-form navbar-left" role="search" action="{{url_for('search.simple_search')}}" method="GET">
            <div class="form-group input-group input-group-sm">
              <label for="query" class="sr-only">{{_('Search')}}</label>
              <input type="text" class="form-control" id="query" name="query" placeholder="{{_('Search Library')}}" value="{{searchterm}}">
              <span class="input-group-btn">
                <button type="submit" id="query_submit" class="btn btn-default">{{_('Search')}}</button>
              </span>
            </div>
          </form>
        {% endif %}
        <div class="navbar-collapse collapse">
          {% if current_user.is_authenticated or g.allow_anonymous %}
          <ul class="nav navbar-nav ">
            <li><a href="{{url_for('search.advanced_search')}}" id="advanced_search"><span class="glyphicon glyphicon-search"></span><span class="hidden-sm"> {{_('Advanced Search')}}</span></a></li>
            <li><a href="{{url_for('search_enhanced.advanced_search_interface')}}" id="enhanced_search"><span class="glyphicon glyphicon-zoom-in"></span><span class="hidden-sm"> {{_('Enhanced Search')}}</span></a></li>
            {% if simple==true %} <li><a href="{{url_for('basic.index')}}" id="basic"><span class="glyphicon glyphicon-phone"></span><span>{{_('Simple Theme')}}</span></a><li>{% endif %}
          </ul>
          {% endif %}
          <ul class="nav navbar-nav navbar-right" id="main-nav">
            {% if current_user.is_authenticated or g.allow_anonymous %}
              {% if g.current_theme == 1 %}
              <li class="dropdown"><a href="#" class="dropdown-toggle profileDrop" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><span class="glyphicon glyphicon-user"></span></a>
                <ul class="dropdown-menu profileDropli">
                    <li><a id="top_user" data-text="{{_('Account')}}" href="{{url_for('web.profile')}}" ><span class="glyphicon glyphicon-user"></span> <span class="hidden-sm">{{current_user.name}}</span></a></li>
                    {% if g.allow_registration and not current_user.is_authenticated %}
                    <li><a id="login" href="{{url_for('web.login')}}"><span class="glyphicon glyphicon-log-in"></span> {{_('Login')}}</a></li>
                    <li><a id="register" href="{{url_for('web.register')}}"><span class="glyphicon glyphicon-user"></span> {{_('Register')}}</a></li>
                    {% endif %}
                    {% if not current_user.is_anonymous %}
                    <li><a id="logout" href="{{url_for('web.logout', next=(request.path + "?" + request.query_string.decode("utf-8")).rstrip("?"))}}"><span class="glyphicon glyphicon-log-out"></span> <span class="hidden-sm">{{_('Logout')}}</span></a></li>
                    {% endif %}
                </ul>
              </li>
              {% endif %}
              {% if current_user.role_upload() and g.allow_upload %}
                  <li>
                    <form id="form-upload" class="navbar-form" action="{{ url_for('edit-book.upload') }}" data-title="{{_('Uploading...')}}" data-footer="{{_('Close')}}" data-failed="{{_('Error')}}" data-message="{{_('Upload done, processing, please wait...')}}" method="post" enctype="multipart/form-data">
                      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                      <div class="form-group">
                        <span class="btn btn-default btn-file">{{_('Upload')}}<input id="btn-upload" name="btn-upload"
                        type="file" accept="{% for format in accept %}.{% if format != ''%}{{format}}{% else %}*{% endif %}{{ ',' if not loop.last }}{% endfor %}" multiple></span>
                        <input class="hide" id="btn-upload2" name="btn-upload2" type="file" accept="{% for format in accept %}.{% if format != ''%}{{format}}{% else %}*{% endif %}{{ ',' if not loop.last }}{% endfor %}">
                      </div>
                    </form>
                  </li>
              {% endif %}
              {% if not current_user.is_anonymous and not simple%}
                <li class="top_tasks"><a id="top_tasks" href="{{url_for('tasks.get_tasks_status')}}"><span class="glyphicon glyphicon-tasks"></span> <span class="hidden-sm">{{_('Tasks')}}</span></a></li>
              {% endif %}
              {% if current_user.role_admin() %}
                <li><a id="top_admin" data-text="{{_('Settings')}}" href="{{url_for('admin.admin')}}"><span class="glyphicon glyphicon-dashboard"></span> <span class="hidden-sm">{{_('Admin')}}</span></a></li>
              {% endif %}
              {% if g.current_theme == 0 %}
              <li><a id="top_user" data-text="{{_('Account')}}" href="{% if not current_user.is_anonymous %}{{url_for('web.profile')}}{% else %}{{url_for('web.login', next=(request.path + "?" + request.query_string.decode("utf-8")).rstrip("?"))}}{% endif %}"><span class="glyphicon glyphicon-user"></span> <span class="hidden-sm">{{current_user.name}}</span></a></li>
                {% if not current_user.is_anonymous %}
                <li><a id="logout" href="{{url_for('web.logout', next=(request.path + "?" + request.query_string.decode("utf-8")).rstrip("?"))}}"><span class="glyphicon glyphicon-log-out"></span> <span class="hidden-sm">{{_('Logout')}}</span></a></li>
                {% endif %}
              {% endif %}
            {% endif %}
            {% if g.allow_registration and not current_user.is_authenticated and g.current_theme == 0 %}
            <li><a id="login" href="{{url_for('web.login')}}"><span class="glyphicon glyphicon-log-in"></span> {{_('Login')}}</a></li>
            <li><a id="register" href="{{url_for('web.register')}}"><span class="glyphicon glyphicon-user"></span> {{_('Register')}}</a></li>
            {% endif %}
          </ul>
        </div><!--/.nav-collapse -->
      </div>
    </div>
    {% for message in get_flashed_messages(with_categories=True) %}
      {%if message[0] == "error" %}
      <div class="row-fluid text-center" >
        <div id="flash_danger" class="alert alert-danger">{{ message[1] }}</div>
      </div>
      {%endif%}
      {%if message[0] == "info" %}
      <div class="row-fluid text-center">
        <div id="flash_info" class="alert alert-info">{{ message[1] }}</div>
      </div>
      {%endif%}
      {%if message[0] == "warning" %}
      <div class="row-fluid text-center">
        <div id="flash_warning" class="alert alert-warning">{{ message[1] }}</div>
      </div>
      {%endif%}
      {%if message[0] == "success" %}
      <div class="row-fluid text-center">
        <div id="flash_success" class="alert alert-success">{{ message[1] }}</div>
      </div>
      {%endif%}
    {% endfor %}
    {% block flash %}{% endblock %}
    {% if g.current_theme == 1 %}
      <div id="loader" hidden="true">
        <center>
          <h3>{{_('Uploading...')}}</h3>
          <span>{{_("Please do not refresh the page")}}</span>.
        </center>
      </div>
    {%endif%}
    <div class="container-fluid">
      <div class="row-fluid">
        {% if current_user.is_authenticated or g.allow_anonymous %}
        <div class="col-sm-2">
          <nav class="navigation">
            <ul class="list-unstyled" id="scnd-nav" intent in-standard-append="nav.navigation" in-mobile-after="#main-nav" in-mobile-class="nav navbar-nav">
              <li class="nav-head hidden-xs">{{_('Browse')}}</li>
              {% for element in sidebar %}
                {% if current_user.check_visibility(element['visibility']) and element['public'] %}
                    <li id="nav_{{element['id']}}" {% if page == element['page'] %}class="active"{% endif %}><a href="{{url_for(element['link'], data=element['page'], sort_param='stored')}}"><span class="glyphicon {{element['glyph']}}"></span> {{_(element['text'])}}</a></li>
                {% endif %}
              {% endfor %}
              {% if current_user.is_authenticated or g.allow_anonymous %}
                <li class="nav-head hidden-xs public-shelves">{{_('Shelves')}}</li>
                {% for shelf in g.shelves_access %}
                  <li><a href="{{url_for('shelf.show_shelf', shelf_id=shelf.id)}}"><span class="glyphicon glyphicon-list shelf"></span> {{shelf.name|shortentitle(40)}}{% if shelf.is_public == 1 %} {{_('(Public)')}}{% endif %}</a></li>
                {% endfor %}
              {% if not current_user.is_anonymous %}
                <li id="nav_createshelf" class="create-shelf"><a href="{{url_for('shelf.create_shelf')}}">{{_('Create a Shelf')}}</a></li>
                <li id="nav_about" {% if page == 'stat' %}class="active"{% endif %}><a href="{{url_for('about.stats')}}"><span class="glyphicon glyphicon-info-sign"></span> {{_('About')}}</a></li>
              {% endif %}
              {% endif %}

            </ul>
          </nav>
        </div>
        {% endif %}
        <div class="col-sm-10">
          {% block body %}{% endblock %}
          {% if pagination and (pagination.has_next or pagination.has_prev) %}
            <div class="pagination">
              {% if pagination.has_prev %}
              <li class="page-item page-previous"><a class="page-link" aria-label="next page" href="{{ (pagination.page - 1)|url_for_other_page
                }}">&laquo; {{_('Previous')}}</a></li>
              {% endif %}
            {% for page in pagination.iter_pages() %}
              {% if page %}
                {% if page != pagination.page %}
                  <li class="page-item"><a class="page-link" aria-label="to page {{ page }}" href="{{ (page)|url_for_other_page }}">{{ page }}</a></li>
                {% else %}
                  <li class="page-item active"><a class="page-link" aria-label="to page {{ page }}" href="{{ (page)|url_for_other_page }}">{{ page }}</a></li>
                {% endif %}
              {% else %}
                <li class="page-item page-last-separator disabled"><a class="page-link" aria-label="">…</a></li>
              {% endif %}
            {% endfor %}
            {% if pagination.has_next %}
              <li class="page-item page-next"><a class="page-link next" aria-label="next page" href="{{ (pagination.page + 1)|url_for_other_page
                }}">{{_('Next')}} &raquo;</a></li>
            {% endif %}
            </div>
          {% endif %}
      </div>
    </div>
  </div>
    <div class="modal fade" id="bookDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookDetailsModalLabel">
      <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title" id="bookDetailsModalLabel">{{_('Book Details')}}</h4>
          </div>
          <div class="modal-body">...</div>
          <div class="modal-footer">
            <button type="button" id="details_close" class="btn btn-default" data-dismiss="modal">{{_('Close')}}</button>
          </div>
        </div>
      </div>
    </div>
    {% block modal %}{% endblock %}
    <!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
    <script src="{{ url_for('static', filename='js/libs/jquery.min.js') }}"></script>
    <!-- Include all compiled plugins (below), or include individual files as needed -->
    <script src="{{ url_for('static', filename='js/libs/bootstrap.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/underscore-umd-min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/intention.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/context.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/plugins.js') }}"></script>
    <script src="{{ url_for('static', filename='js/libs/jquery.form.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/uploadprogress.js') }}"> </script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% if g.current_theme == 1 %}
      <script src="{{ url_for('static', filename='js/libs/jquery.visible.min.js') }}"></script>
      <script src="{{ url_for('static', filename='js/libs/compromise.min.js') }}"></script>
      <script src="{{ url_for('static', filename='js/libs/readmore.min.js') }}"></script>
      <script src="{{ url_for('static', filename='js/caliBlur.js') }}"></script>
    {% endif %}
    {% block js %}{% endblock %}
  </body>
</html>
