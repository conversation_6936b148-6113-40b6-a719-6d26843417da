{% extends "layout.html" %}
{% block body %}
<div class="discover">
  <h1>{{_('Elasticsearch Management')}}</h1>
  
  <div class="row">
    <div class="col-sm-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">{{_('Elasticsearch Status')}}</h3>
        </div>
        <div class="panel-body">
          {% if elasticsearch_status.available %}
            <div class="alert alert-success">
              <strong>{{_('Status')}}:</strong> {{_('Connected')}}
            </div>
            
            {% if elasticsearch_status.connected %}
              <table class="table table-striped">
                <tr>
                  <td><strong>{{_('Index Name')}}:</strong></td>
                  <td>{{ elasticsearch_status.index_name or _('Not available') }}</td>
                </tr>
                <tr>
                  <td><strong>{{_('Indexed Books')}}:</strong></td>
                  <td>{{ elasticsearch_status.document_count or 0 }}</td>
                </tr>
                <tr>
                  <td><strong>{{_('Database Books')}}:</strong></td>
                  <td>{{ db_book_count }}</td>
                </tr>
                <tr>
                  <td><strong>{{_('Index Size')}}:</strong></td>
                  <td>
                    {% if elasticsearch_status.index_size %}
                      {{ "%.2f"|format(elasticsearch_status.index_size / 1024 / 1024) }} MB
                    {% else %}
                      {{_('Not available')}}
                    {% endif %}
                  </td>
                </tr>
                <tr>
                  <td><strong>{{_('Sync Status')}}:</strong></td>
                  <td>
                    {% if elasticsearch_status.document_count == db_book_count %}
                      <span class="text-success">{{_('In Sync')}}</span>
                    {% else %}
                      <span class="text-warning">{{_('Out of Sync')}}</span>
                    {% endif %}
                  </td>
                </tr>
              </table>
            {% endif %}
            
          {% else %}
            <div class="alert alert-danger">
              <strong>{{_('Status')}}:</strong> {{_('Not Available')}}
              {% if elasticsearch_status.error %}
                <br><strong>{{_('Error')}}:</strong> {{ elasticsearch_status.error }}
              {% endif %}
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
  
  <div class="row">
    <div class="col-sm-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">{{_('Management Actions')}}</h3>
        </div>
        <div class="panel-body">
          <div class="btn-group" role="group">
            <form method="POST" action="{{ url_for('admin.elasticsearch_test') }}" style="display: inline;">
              <button type="submit" class="btn btn-info">
                <span class="glyphicon glyphicon-refresh"></span>
                {{_('Test Connection')}}
              </button>
            </form>
            
            {% if elasticsearch_status.available %}
              <form method="POST" action="{{ url_for('admin.elasticsearch_reindex') }}" style="display: inline;" 
                    onsubmit="return confirm('{{_('This will reindex all books. This may take a while. Continue?')}}');">
                <button type="submit" class="btn btn-warning">
                  <span class="glyphicon glyphicon-repeat"></span>
                  {{_('Reindex All Books')}}
                </button>
              </form>
            {% endif %}
          </div>
          
          <div class="help-block" style="margin-top: 15px;">
            <h4>{{_('About Elasticsearch Integration')}}</h4>
            <ul>
              <li>{{_('Elasticsearch provides advanced full-text search capabilities')}}</li>
              <li>{{_('Books are automatically indexed when added or modified')}}</li>
              <li>{{_('If Elasticsearch is unavailable, search falls back to SQL-based search')}}</li>
              <li>{{_('Reindexing recreates the entire search index from scratch')}}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row">
    <div class="col-sm-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">{{_('Configuration')}}</h3>
        </div>
        <div class="panel-body">
          <table class="table table-striped">
            <tr>
              <td><strong>{{_('Elasticsearch Enabled')}}:</strong></td>
              <td>
                {% if config.config_elasticsearch_enabled %}
                  <span class="text-success">{{_('Yes')}}</span>
                {% else %}
                  <span class="text-danger">{{_('No')}}</span>
                {% endif %}
              </td>
            </tr>
            <tr>
              <td><strong>{{_('Host')}}:</strong></td>
              <td>{{ config.config_elasticsearch_host }}</td>
            </tr>
            <tr>
              <td><strong>{{_('Port')}}:</strong></td>
              <td>{{ config.config_elasticsearch_port }}</td>
            </tr>
            <tr>
              <td><strong>{{_('Index Name')}}:</strong></td>
              <td>{{ config.config_elasticsearch_index }}</td>
            </tr>
          </table>
          
          <a href="{{ url_for('admin.configuration') }}" class="btn btn-primary">
            <span class="glyphicon glyphicon-cog"></span>
            {{_('Edit Configuration')}}
          </a>
        </div>
      </div>
    </div>
  </div>
  
  <div class="row">
    <div class="col-sm-12">
      <a href="{{ url_for('admin.admin') }}" class="btn btn-default">
        <span class="glyphicon glyphicon-arrow-left"></span>
        {{_('Back to Admin')}}
      </a>
    </div>
  </div>
</div>
{% endblock %}
