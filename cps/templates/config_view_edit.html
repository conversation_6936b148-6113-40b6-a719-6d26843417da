{% extends "layout.html" %}
{% block header %}
<link href="{{ url_for('static', filename='css/libs/bootstrap-table.min.css') }}" rel="stylesheet">
<link href="{{ url_for('static', filename='css/libs/bootstrap-editable.css') }}" rel="stylesheet">
{% endblock %}
{% block body %}
<div class="discover">
  <h2>{{title}}</h2>
<form role="form" method="POST" autocomplete="off" >
<input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
<div class="panel-group" class="col-md-10 col-lg-6">
  <div class="panel panel-default">
    <div class="panel-heading">
      <h4 class="panel-title">
        <a class="accordion-toggle" data-toggle="collapse" href="#collapsefour">
          <span class="glyphicon glyphicon-plus"></span>
          {{_('View Configuration')}}
        </a>
      </h4>
    </div>
    <div id="collapsefour" class="panel-collapse collapse">
      <div class="panel-body">
                <div class="form-group">
          <label for="config_calibre_web_title">{{_('Title')}}</label>
          <input type="text" class="form-control" name="config_calibre_web_title" id="config_calibre_web_title" value="{% if conf.config_calibre_web_title != None %}{{ conf.config_calibre_web_title }}{% endif %}" autocomplete="off" required>
        </div>
        <div class="form-group">
          <label for="config_books_per_page">{{_('Books per Page')}}</label>
          <input type="number" min="1" max="200" class="form-control" name="config_books_per_page" id="config_books_per_page" value="{% if conf.config_books_per_page != None %}{{ conf.config_books_per_page }}{% endif %}" autocomplete="off">
        </div>
        <div class="form-group">
          <label for="config_random_books">{{_('No. of Random Books to Display')}}</label>
          <input type="number" min="1" max="30" class="form-control" name="config_random_books" id="config_random_books" value="{% if conf.config_random_books != None %}{{ conf.config_random_books }}{% endif %}" autocomplete="off">
        </div>
        <div class="form-group">
          <label for="config_authors_max">{{_('No. of Authors to Display Before Hiding (0=Disable Hiding)')}}</label>
          <input type="number" min="0" max="999" class="form-control" name="config_authors_max" id="config_authors_max" value="{% if conf.config_authors_max != None %}{{ conf.config_authors_max }}{% endif %}" autocomplete="off">
        </div>
        <div class="form-group">
        <label for="config_theme">{{_('Theme')}}</label>
            <select name="config_theme" id="config_theme" class="form-control">
                <option value="0" {% if conf.config_theme == 0 %}selected{% endif %}>{{ _("Standard Theme") }}</option>
                <option value="1" {% if conf.config_theme == 1 %}selected{% endif %}>{{ _("caliBlur! Dark Theme") }}</option>
            </select>
        </div>
        <div class="form-group">
          <label for="config_columns_to_ignore">{{_('Regular Expression for Ignoring Columns')}}</label>
          <input type="text" class="form-control" name="config_columns_to_ignore" id="config_columns_to_ignore" value="{% if conf.config_columns_to_ignore != None %}{{ conf.config_columns_to_ignore }}{% endif %}" autocomplete="off">
        </div>
        <div class="form-group">
          <label for="config_read_column">{{_('Link Read/Unread Status to Calibre Column')}}</label>
              <select name="config_read_column" id="config_read_column" class="form-control">
                <option value="0" {% if conf.config_read_column == 0 %}selected{% endif %}></option>
                {%  for readColumn in readColumns %}
                <option value="{{ readColumn.id }}" {% if readColumn.id == conf.config_read_column %}selected{% endif %}>{{ readColumn.name }}</option>
                {% endfor %}
              </select>
        </div>
        <div class="form-group">
          <label for="config_restricted_column">{{_('View Restrictions based on Calibre column')}}</label>
              <select name="config_restricted_column" id="config_restricted_column" class="form-control">
                <option value="0" {% if conf.config_restricted_column == 0 %}selected{% endif %}>{{ _('None') }}</option>
                {%  for restrictColumn in restrictColumns %}
                <option value="{{ restrictColumn.id }}" {% if restrictColumn.id == conf.config_restricted_column %}selected{% endif %}>{{ restrictColumn.name }}</option>
                {% endfor %}
              </select>
        </div>
        <div class="form-group">
          <label for="config_title_regex">{{_('Regular Expression for Title Sorting')}}</label>
          <input type="text" class="form-control" name="config_title_regex" id="config_title_regex" value="{% if conf.config_title_regex != None %}{{ conf.config_title_regex }}{% endif %}" autocomplete="off">
        </div>
        </div>
      </div>
    </div>
  <div class="panel panel-default">
    <div class="panel-heading">
      <h4 class="panel-title">
        <a class="accordion-toggle" data-toggle="collapse" href="#collapsesix">
          <span class="glyphicon glyphicon-plus"></span>
          {{_('Default Settings for New Users')}}
        </a>
      </h4>
    </div>
    <div id="collapsesix" class="panel-collapse collapse">
      <div class="panel-body">
        <div class="form-group">
          <input type="checkbox" name="admin_role" id="admin_role" {% if conf.role_admin() %}checked{% endif %}>
          <label for="admin_role">{{_('Admin User')}}</label>
        </div>
        <div class="form-group">
          <input type="checkbox" name="download_role" id="download_role" {% if conf.role_download() %}checked{% endif %}>
          <label for="download_role">{{_('Allow Downloads')}}</label>
        </div>
        <div class="form-group">
          <input type="checkbox" name="viewer_role" id="viewer_role" {% if conf.role_viewer() %}checked{% endif %}>
          <label for="viewer_role">{{_('Allow eBook Viewer')}}</label>
        </div>
      {% if conf.config_uploading %}
        <div class="form-group">
          <input type="checkbox" name="upload_role" id="upload_role" {% if conf.role_upload() %}checked{% endif %}>
          <label for="upload_role">{{_('Allow Uploads')}}</label>
        </div>
      {% endif %}
        <div class="form-group">
          <input type="checkbox" name="edit_role" data-control="edit_settings" id="edit_role" {% if conf.role_edit() %}checked{% endif %}>
          <label for="edit_role">{{_('Allow Edit')}}</label>
        </div>
        <div data-related="edit_settings">
          <div class="form-group">
            <input type="checkbox" name="delete_role" id="delete_role" {% if conf.role_delete_books() %}checked{% endif %}>
            <label for="delete_role">{{_('Allow Delete Books')}}</label>
          </div>
        </div>
        <div class="form-group">
          <input type="checkbox" name="passwd_role" id="passwd_role" {% if conf.role_passwd() %}checked{% endif %}>
          <label for="passwd_role">{{_('Allow Changing Password')}}</label>
        </div>
        <div class="form-group">
          <input type="checkbox" name="edit_shelf_role" id="edit_shelf_role" {% if conf.role_edit_shelfs() %}checked{% endif %}>
          <label for="edit_shelf_role">{{_('Allow Editing Public Shelves')}}</label>
        </div>
        <div class="form-group">
        <label for="config_default_locale">{{_('Default Language')}}</label>
            <select name="config_default_locale" id="config_default_locale" class="form-control">
                {%  for translation in translations %}
                    <option value="{{translation}}" {% if translation|string == conf.config_default_locale %}selected{% endif %}>{{ translation.display_name|capitalize }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="form-group">
          <label for="config_default_language">{{_('Default Visible Language of Books')}}</label>
            <select name="config_default_language" id="config_default_language" class="form-control">
                <option value="all" {% if conf.config_default_language == "all" %}selected{% endif %}>{{ _('Show All') }}</option>
                {%  for language in languages %}
                <option value="{{ language.lang_code }}" {% if conf.config_default_language == language.lang_code %}selected{% endif %}>{{ language.name }}</option>
                {% endfor %}
            </select>
        </div>
      </div>
    </div>
  </div>
  <div class="panel panel-default">
    <div class="panel-heading">
      <h4 class="panel-title">
        <a class="accordion-toggle" data-toggle="collapse" href="#collapseseven">
          <span class="glyphicon glyphicon-plus"></span>
          {{_('Default Visibilities for New Users')}}
        </a>
      </h4>
    </div>
    <div id="collapseseven" class="panel-collapse collapse">
      <div class="panel-body">
        {% for element in sidebar %}
          {% if element['config_show'] %}
            <div class="form-group">
              <input type="checkbox" name="show_{{element['visibility']}}" id="show_{{element['visibility']}}" {% if conf.show_element_new_user(element['visibility']) %}checked{% endif %}>
              <label for="show_{{element['visibility']}}">{{element['show_text']}}</label>
            </div>
          {% endif %}
        {% endfor %}
        <div class="form-group">
            <input type="checkbox" name="Show_detail_random" id="Show_detail_random" {% if conf.show_detail_random() %}checked{% endif %}>
            <label for="Show_detail_random">{{_('Show Random Books in Detail View')}}</label>
        </div>
      {% if not simple %}
        <a href="#" id="get_tags" data-id="0" class="btn btn-default" data-toggle="modal" data-target="#restrictModal">{{_('Add Allowed/Denied Tags')}}</a>
        <a href="#" id="get_column_values" data-id="0" class="btn btn-default" data-toggle="modal" data-target="#restrictModal">{{_('Add Allowed/Denied custom column values')}}</a>
      {% endif %}
      </div>
    </div>
  </div>
</div>
    <div class="col-sm-12">
    <button type="submit" name="submit" class="btn btn-default">{{_('Save')}}</button>
    <a href="{{ url_for('admin.admin') }}" class="btn btn-default">{{_('Cancel')}}</a>
    </div>
  </form>
</div>
{% endblock %}
{% block modal %}
{{ restrict_modal() }}
{% endblock %}
{% block js %}
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-table.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-table-editable.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/libs/bootstrap-table/bootstrap-editable.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/table.js') }}"></script>
{% endblock %}
