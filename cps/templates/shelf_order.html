{% extends "layout.html" %}
{% block body %}
  <div class="col-sm-8 col-lg-8 col-xs-12">
    <h2>{{title}}</h2>
    <div>{{_('Drag to Rearrange Order')}}</div>
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
      <div id="sortTrue" class="list-group">
        {% for entry in entries %}
          <div id="{{entry['Books']['id']}}" class="list-group-item">
            <div class="row">
              <div class="col-lg-2 col-sm-4 hidden-xs">
                {% if entry['visible'] %}
                  <img title="{{entry['Books']['title']}}" class="cover-height" src="{{ url_for('web.get_cover', book_id=entry['Books']['id']) }}">
                {% else %}
                  <img title="{{entry['Books']['title']}}" class="cover-height" src="{{ url_for('static', filename='generic_cover.jpg') }}">
                {% endif %}
              </div>
              <div class="col-lg-10 col-sm-8 col-xs-12">
              {% if entry['visible'] %}
                  {{entry['Books']['title']}}
                  {% if entry['Books']['series']|length > 0 %}
                    <br>
                    {{entry['Books']['series_index']|formatfloat(2)}} - {{entry['Books']['series'][0].name}}
                  {% endif %}
                  <br>
                  {% for author in entry['Books']['author'] %}
                    {{author.name.replace('|',',')}}
                    {% if not loop.last %}
                      &amp;
                    {% endif %}
                  {% endfor %}
              {% else %}
                  {{_('Hidden Book')}}
                  <br>
              {% endif %}
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
        <button onclick="sendData('{{ url_for('shelf.order_shelf', shelf_id=shelf.id) }}')" class="btn btn-default" id="ChangeOrder">{{_('Save')}}</button>
        <a href="{{ url_for('shelf.show_shelf', shelf_id=shelf.id) }}" id="shelf_back" class="btn btn-default">{{_('Back')}}</a>
  </div>
{% endblock %}

{% block js %}
<script src="{{ url_for('static', filename='js/libs/Sortable.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/shelforder.js') }}"></script>
{% endblock %}
