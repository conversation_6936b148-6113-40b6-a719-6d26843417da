{% import 'image.html' as image %}
{% extends "layout.html" %}
{% block body %}
<h1 class="{{page}}">{{_(title)}}</h1>

    <div class="filterheader hidden-xs">
      {% if entries.__len__() and data == 'author' %}
        <div id="sort_name" class="btn btn-primary"><b>B,A <-> A B</b></div>
      {% endif %}
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
      <div id="asc" data-id="series" data-order="{{ order }}" class="btn btn-primary{% if order == 1 %} active{% endif%}"><span class="glyphicon glyphicon-sort-by-alphabet"></span></div>
      <div id="desc" data-id="series" class="btn btn-primary{% if order == 0 %} active{% endif%}"><span class="glyphicon glyphicon-sort-by-alphabet-alt"></span></div>
      {% if charlist|length %}
      <div id="all" class="active btn btn-primary {% if charlist|length > 9 %}hidden-sm{% endif %}">{{_('All')}}</div>
      {% endif %}
      <div class="btn-group character {% if charlist|length > 9 %}hidden-sm{% endif %}" role="group">
        {% for char in charlist%}
        <div class="btn btn-primary char">{{char.char}}</div>
        {% endfor %}
      </div>
        <div class="update-view btn btn-primary" data-target="series_view" id="list-button" data-view="list">{{_('List')}}</div>
    </div>

    {% if entries[0] %}
        <div id="list" class="row display-flex">
          {% for entry in entries %}
              <div class="col-sm-3 col-lg-2 col-xs-6 book sortable" {% if entry[0].sort %}data-name="{{entry[0].series[0].name}}"{% endif %} data-id="{% if entry[0].series[0].name %}{{entry[0].series[0].name}}{% endif %}">
                  <div class="cover">
                      <a href="{{url_for('web.books_list', data=data, sort_param='stored', book_id=entry[0].series[0].id )}}">
                          <span class="img" title="{{entry[0].series[0].name}}">
                              {{ image.book_cover(entry[0])}}
                              <span class="badge">{{entry.count}}</span>
                            </span>
                      </a>
                  </div>
                  <div class="meta">
                      <a href="{{url_for('web.books_list', data=data, sort_param='stored', book_id=entry[0].series[0].id )}}">
                          <p title="{{entry[0].series[0].name|shortentitle}}" class="title">{{entry[0].series[0].name|shortentitle}}</p>
                      </a>
                  </div>
              </div>
        {% endfor %}
        </div>
    {% endif %}


{% endblock %}
{% block js %}
<script src="{{ url_for('static', filename='js/filter_grid.js') }}"></script>
{% endblock %}
