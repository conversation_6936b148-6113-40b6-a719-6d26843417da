# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

from flask import Blueprint, jsonify, request, abort
from flask_babel import gettext as _

from . import logger, config, calibre_db, db, ub
from .usermanagement import user_login_required
from .elasticsearch_service import elasticsearch_service
from .book_indexer import book_indexer
from .enhanced_book_manager import enhanced_book_manager
from .performance_optimizer import performance_optimizer

api_enhanced = Blueprint('api_enhanced', __name__)
log = logger.create()


@api_enhanced.route("/api/v2/health", methods=["GET"])
def api_health():
    """Enhanced health check endpoint"""
    try:
        health_data = {
            "status": "healthy",
            "version": getattr(config, 'STABLE_VERSION', 'unknown'),
            "database": {
                "connected": config.db_configured,
                "books": calibre_db.session.query(db.Books).count() if config.db_configured else 0
            },
            "elasticsearch": {
                "enabled": config.config_elasticsearch_enabled,
                "connected": elasticsearch_service.is_connected() if config.config_elasticsearch_enabled else False
            },
            "features": {
                "simplified_auth": config.is_simplified_auth_enabled(),
                "password_free_mode": config.is_password_free_mode_enabled(),
                "elasticsearch": config.config_elasticsearch_enabled
            }
        }
        
        return jsonify(health_data), 200
        
    except Exception as e:
        log.error("API health check failed: %s", e)
        return jsonify({"status": "unhealthy", "error": str(e)}), 503


@api_enhanced.route("/api/v2/stats", methods=["GET"])
@user_login_required
def api_stats():
    """Get comprehensive library statistics"""
    try:
        stats = enhanced_book_manager.get_book_statistics()
        
        # Add performance data if available
        perf_data = performance_optimizer.monitor_performance()
        if perf_data:
            stats['performance'] = perf_data
        
        return jsonify(stats), 200
        
    except Exception as e:
        log.error("API stats failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/search", methods=["GET"])
@user_login_required
def api_search():
    """Enhanced search API with Elasticsearch support"""
    try:
        query = request.args.get('q', '')
        offset = int(request.args.get('offset', 0))
        limit = int(request.args.get('limit', 50))
        
        if not query:
            return jsonify({"error": "Query parameter 'q' is required"}), 400
        
        # Try Elasticsearch first
        if elasticsearch_service.is_available():
            book_ids, total_count = elasticsearch_service.search_books(query, offset, limit)
            
            # Get book details
            books = []
            for book_id in book_ids:
                book = calibre_db.get_book(book_id)
                if book:
                    book_data = book_indexer.extract_book_data(book)
                    books.append(book_data)
            
            return jsonify({
                "results": books,
                "total": total_count,
                "offset": offset,
                "limit": limit,
                "search_engine": "elasticsearch"
            }), 200
        
        else:
            # Fallback to SQL search
            join = db.books_series_link, db.Books.id == db.books_series_link.c.book, db.Series
            entries, result_count, pagination = calibre_db.get_search_results(
                query, config, offset, [None, None], limit, *join
            )
            
            books = []
            for book in entries:
                book_data = book_indexer.extract_book_data(book)
                books.append(book_data)
            
            return jsonify({
                "results": books,
                "total": result_count,
                "offset": offset,
                "limit": limit,
                "search_engine": "sql"
            }), 200
        
    except Exception as e:
        log.error("API search failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/books/<int:book_id>/metadata", methods=["GET"])
@user_login_required
def api_book_metadata(book_id):
    """Get detailed book metadata"""
    try:
        book = calibre_db.get_book(book_id)
        if not book:
            return jsonify({"error": "Book not found"}), 404
        
        metadata = book_indexer.extract_book_data(book)
        return jsonify(metadata), 200
        
    except Exception as e:
        log.error("API book metadata failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/books/bulk-update", methods=["POST"])
@user_login_required
def api_bulk_update():
    """Bulk update book metadata"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "JSON data required"}), 400
        
        book_ids = data.get('book_ids', [])
        updates = data.get('updates', {})
        
        if not book_ids:
            return jsonify({"error": "book_ids required"}), 400
        
        if not updates:
            return jsonify({"error": "updates required"}), 400
        
        results = enhanced_book_manager.bulk_metadata_update(book_ids, updates)
        return jsonify(results), 200
        
    except Exception as e:
        log.error("API bulk update failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/books/export", methods=["POST"])
@user_login_required
def api_export_books():
    """Export book metadata"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "JSON data required"}), 400
        
        book_ids = data.get('book_ids', [])
        format_type = data.get('format', 'json').lower()
        
        if not book_ids:
            return jsonify({"error": "book_ids required"}), 400
        
        if format_type not in ['json', 'csv']:
            return jsonify({"error": "Supported formats: json, csv"}), 400
        
        export_data = enhanced_book_manager.export_book_metadata(book_ids, format_type)
        
        return jsonify({
            "format": format_type,
            "data": export_data,
            "book_count": len(book_ids)
        }), 200
        
    except Exception as e:
        log.error("API export failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/books/duplicates", methods=["POST"])
@user_login_required
def api_find_duplicates():
    """Find potential duplicate books"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "JSON data required"}), 400
        
        title = data.get('title', '')
        authors = data.get('authors', [])
        
        if not title:
            return jsonify({"error": "title required"}), 400
        
        duplicates = enhanced_book_manager.duplicate_detection(title, authors)
        
        return jsonify({
            "duplicates": duplicates,
            "count": len(duplicates)
        }), 200
        
    except Exception as e:
        log.error("API duplicate detection failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/elasticsearch/status", methods=["GET"])
@user_login_required
def api_elasticsearch_status():
    """Get Elasticsearch status"""
    try:
        if not config.config_elasticsearch_enabled:
            return jsonify({"enabled": False, "message": "Elasticsearch not enabled"}), 200
        
        status = book_indexer.get_index_status()
        return jsonify(status), 200
        
    except Exception as e:
        log.error("API Elasticsearch status failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/elasticsearch/reindex", methods=["POST"])
@user_login_required
def api_elasticsearch_reindex():
    """Trigger Elasticsearch reindexing"""
    try:
        if not config.config_elasticsearch_enabled:
            return jsonify({"error": "Elasticsearch not enabled"}), 400
        
        # Connect to Elasticsearch
        if not elasticsearch_service.is_connected():
            if not elasticsearch_service.connect():
                return jsonify({"error": "Failed to connect to Elasticsearch"}), 500
        
        # Start reindexing
        success = book_indexer.index_all_books()
        
        if success:
            return jsonify({"message": "Reindexing started successfully"}), 200
        else:
            return jsonify({"error": "Failed to start reindexing"}), 500
        
    except Exception as e:
        log.error("API Elasticsearch reindex failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/performance/cache/clear", methods=["POST"])
@user_login_required
def api_clear_cache():
    """Clear performance cache"""
    try:
        pattern = request.args.get('pattern')
        performance_optimizer.clear_cache(pattern)
        
        return jsonify({
            "message": "Cache cleared successfully",
            "pattern": pattern or "all"
        }), 200
        
    except Exception as e:
        log.error("API cache clear failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/performance/monitor", methods=["GET"])
@user_login_required
def api_performance_monitor():
    """Get performance monitoring data"""
    try:
        perf_data = performance_optimizer.monitor_performance()
        
        if perf_data:
            return jsonify(perf_data), 200
        else:
            return jsonify({"message": "Performance monitoring not available"}), 200
        
    except Exception as e:
        log.error("API performance monitor failed: %s", e)
        return jsonify({"error": str(e)}), 500


@api_enhanced.route("/api/v2/config", methods=["GET"])
@user_login_required
def api_config():
    """Get current configuration (safe subset)"""
    try:
        safe_config = {
            "elasticsearch": {
                "enabled": config.config_elasticsearch_enabled,
                "host": config.config_elasticsearch_host,
                "port": config.config_elasticsearch_port,
                "index": config.config_elasticsearch_index
            },
            "authentication": {
                "simplified_auth": config.is_simplified_auth_enabled(),
                "password_free_mode": config.is_password_free_mode_enabled(),
                "anonymous_browsing": config.config_anonbrowse
            },
            "features": {
                "books_per_page": config.config_books_per_page,
                "random_books": config.config_random_books,
                "read_column": config.config_read_column
            }
        }
        
        return jsonify(safe_config), 200
        
    except Exception as e:
        log.error("API config failed: %s", e)
        return jsonify({"error": str(e)}), 500
