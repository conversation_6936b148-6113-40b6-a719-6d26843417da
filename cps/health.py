# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

import os
import time
from datetime import datetime
from flask import Blueprint, jsonify, request
from . import config, calibre_db, ub, logger

health = Blueprint('health', __name__)
log = logger.create()

@health.route("/health")
def health_check():
    """Basic health check endpoint for Docker and load balancers"""
    try:
        # Check if the application is running
        status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": getattr(config, 'STABLE_VERSION', 'unknown'),
            "uptime": time.time() - getattr(health_check, 'start_time', time.time())
        }
        
        # Quick database connectivity check
        if config.db_configured:
            try:
                # Simple query to check database connectivity
                calibre_db.session.execute("SELECT 1").fetchone()
                status["database"] = "connected"
            except Exception as e:
                log.warning("Database health check failed: %s", e)
                status["database"] = "disconnected"
                status["status"] = "degraded"
        else:
            status["database"] = "not_configured"
            
        return jsonify(status), 200 if status["status"] == "healthy" else 503
        
    except Exception as e:
        log.error("Health check failed: %s", e)
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 503

@health.route("/health/detailed")
def detailed_health_check():
    """Detailed health check with more comprehensive system information"""
    try:
        status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "checks": {}
        }
        
        # Database check
        try:
            if config.db_configured:
                calibre_db.session.execute("SELECT 1").fetchone()
                book_count = calibre_db.session.query(calibre_db.Books).count()
                status["checks"]["database"] = {
                    "status": "healthy",
                    "book_count": book_count,
                    "path": config.config_calibre_dir
                }
            else:
                status["checks"]["database"] = {
                    "status": "not_configured",
                    "message": "Database not configured"
                }
        except Exception as e:
            status["checks"]["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            status["status"] = "degraded"
        
        # User database check
        try:
            user_count = ub.session.query(ub.User).count()
            status["checks"]["user_database"] = {
                "status": "healthy",
                "user_count": user_count
            }
        except Exception as e:
            status["checks"]["user_database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            status["status"] = "degraded"
        
        # Configuration check
        status["checks"]["configuration"] = {
            "status": "healthy",
            "config_dir": config.CONFIG_DIR,
            "db_configured": config.db_configured
        }
        
        # Elasticsearch check (if enabled)
        elasticsearch_enabled = os.environ.get('ELASTICSEARCH_ENABLED', 'false').lower() == 'true'
        if elasticsearch_enabled:
            try:
                # This will be implemented when we add Elasticsearch
                status["checks"]["elasticsearch"] = {
                    "status": "not_implemented",
                    "message": "Elasticsearch integration pending"
                }
            except Exception as e:
                status["checks"]["elasticsearch"] = {
                    "status": "unhealthy",
                    "error": str(e)
                }
                status["status"] = "degraded"
        
        # File system checks
        try:
            config_writable = os.access(config.CONFIG_DIR, os.W_OK)
            status["checks"]["filesystem"] = {
                "status": "healthy" if config_writable else "degraded",
                "config_writable": config_writable
            }
            if not config_writable:
                status["status"] = "degraded"
        except Exception as e:
            status["checks"]["filesystem"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            status["status"] = "degraded"
        
        return jsonify(status), 200 if status["status"] in ["healthy", "degraded"] else 503
        
    except Exception as e:
        log.error("Detailed health check failed: %s", e)
        return jsonify({
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 503

@health.route("/health/ready")
def readiness_check():
    """Kubernetes-style readiness check"""
    try:
        # Check if the application is ready to serve requests
        if not config.db_configured:
            return jsonify({
                "status": "not_ready",
                "reason": "database_not_configured"
            }), 503
        
        # Test database connectivity
        calibre_db.session.execute("SELECT 1").fetchone()
        
        return jsonify({
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        log.error("Readiness check failed: %s", e)
        return jsonify({
            "status": "not_ready",
            "error": str(e)
        }), 503

@health.route("/health/live")
def liveness_check():
    """Kubernetes-style liveness check"""
    try:
        # Simple check that the application process is alive
        return jsonify({
            "status": "alive",
            "timestamp": datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        log.error("Liveness check failed: %s", e)
        return jsonify({
            "status": "dead",
            "error": str(e)
        }), 503

# Initialize start time for uptime calculation
health_check.start_time = time.time()
