# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from werkzeug.utils import secure_filename

from . import logger, config, calibre_db, db, helper
from .book_indexer import book_indexer

log = logger.create()


class EnhancedBookManager:
    """Enhanced book management with improved features"""
    
    def __init__(self):
        self.supported_formats = {
            'ebook': ['.epub', '.pdf', '.mobi', '.azw', '.azw3', '.fb2', '.lit', '.lrf', '.pdb', '.rtf', '.txt'],
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
            'archive': ['.zip', '.rar', '.7z', '.tar', '.gz']
        }
        
    def validate_book_file(self, filename: str) -> Tuple[bool, str, str]:
        """Validate uploaded book file"""
        if not filename:
            return False, "No file provided", ""
        
        # Secure the filename
        secure_name = secure_filename(filename)
        if not secure_name:
            return False, "Invalid filename", ""
        
        # Get file extension
        _, ext = os.path.splitext(secure_name.lower())
        
        # Check if it's a supported format
        file_type = None
        for format_type, extensions in self.supported_formats.items():
            if ext in extensions:
                file_type = format_type
                break
        
        if not file_type:
            return False, f"Unsupported file format: {ext}", ""
        
        return True, "Valid file", file_type
    
    def extract_metadata_from_file(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from book file"""
        metadata = {
            'title': '',
            'authors': [],
            'description': '',
            'publisher': '',
            'published_date': None,
            'isbn': '',
            'language': '',
            'tags': [],
            'series': '',
            'series_index': 0
        }
        
        try:
            # Try to extract metadata using different methods
            if file_path.lower().endswith('.epub'):
                metadata.update(self._extract_epub_metadata(file_path))
            elif file_path.lower().endswith('.pdf'):
                metadata.update(self._extract_pdf_metadata(file_path))
            
            # Fallback to filename-based metadata
            if not metadata['title']:
                filename = os.path.basename(file_path)
                metadata['title'] = os.path.splitext(filename)[0].replace('_', ' ').replace('-', ' ')
            
        except Exception as e:
            log.warning("Failed to extract metadata from %s: %s", file_path, e)
        
        return metadata
    
    def _extract_epub_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from EPUB file"""
        metadata = {}
        
        try:
            import zipfile
            import xml.etree.ElementTree as ET
            
            with zipfile.ZipFile(file_path, 'r') as epub:
                # Find OPF file
                container_xml = epub.read('META-INF/container.xml')
                container_root = ET.fromstring(container_xml)
                
                opf_path = None
                for rootfile in container_root.findall('.//{urn:oasis:names:tc:opendocument:xmlns:container}rootfile'):
                    opf_path = rootfile.get('full-path')
                    break
                
                if opf_path:
                    opf_content = epub.read(opf_path)
                    opf_root = ET.fromstring(opf_content)
                    
                    # Extract title
                    title_elem = opf_root.find('.//{http://purl.org/dc/elements/1.1/}title')
                    if title_elem is not None:
                        metadata['title'] = title_elem.text
                    
                    # Extract authors
                    authors = []
                    for creator in opf_root.findall('.//{http://purl.org/dc/elements/1.1/}creator'):
                        if creator.text:
                            authors.append(creator.text)
                    metadata['authors'] = authors
                    
                    # Extract description
                    desc_elem = opf_root.find('.//{http://purl.org/dc/elements/1.1/}description')
                    if desc_elem is not None:
                        metadata['description'] = desc_elem.text
                    
                    # Extract publisher
                    pub_elem = opf_root.find('.//{http://purl.org/dc/elements/1.1/}publisher')
                    if pub_elem is not None:
                        metadata['publisher'] = pub_elem.text
                    
                    # Extract language
                    lang_elem = opf_root.find('.//{http://purl.org/dc/elements/1.1/}language')
                    if lang_elem is not None:
                        metadata['language'] = lang_elem.text
                    
                    # Extract ISBN
                    for identifier in opf_root.findall('.//{http://purl.org/dc/elements/1.1/}identifier'):
                        if identifier.get('scheme', '').lower() == 'isbn':
                            metadata['isbn'] = identifier.text
                            break
        
        except Exception as e:
            log.debug("Failed to extract EPUB metadata: %s", e)
        
        return metadata
    
    def _extract_pdf_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from PDF file"""
        metadata = {}
        
        try:
            # Try using PyPDF2 if available
            import PyPDF2
            
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)
                pdf_info = pdf_reader.metadata
                
                if pdf_info:
                    if pdf_info.title:
                        metadata['title'] = pdf_info.title
                    if pdf_info.author:
                        metadata['authors'] = [pdf_info.author]
                    if pdf_info.subject:
                        metadata['description'] = pdf_info.subject
                    if pdf_info.creator:
                        metadata['publisher'] = pdf_info.creator
        
        except ImportError:
            log.debug("PyPDF2 not available for PDF metadata extraction")
        except Exception as e:
            log.debug("Failed to extract PDF metadata: %s", e)
        
        return metadata
    
    def bulk_metadata_update(self, book_ids: List[int], updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update metadata for multiple books"""
        results = {
            'success': [],
            'failed': [],
            'total': len(book_ids)
        }
        
        for book_id in book_ids:
            try:
                book = calibre_db.get_book(book_id)
                if not book:
                    results['failed'].append({'id': book_id, 'error': 'Book not found'})
                    continue
                
                # Apply updates
                updated = False
                
                if 'tags' in updates and updates['tags']:
                    # Add tags
                    for tag_name in updates['tags']:
                        tag = calibre_db.session.query(db.Tags).filter(db.Tags.name == tag_name).first()
                        if not tag:
                            tag = db.Tags(name=tag_name)
                            calibre_db.session.add(tag)
                        
                        if tag not in book.tags:
                            book.tags.append(tag)
                            updated = True
                
                if 'series' in updates and updates['series']:
                    # Set series
                    series = calibre_db.session.query(db.Series).filter(db.Series.name == updates['series']).first()
                    if not series:
                        series = db.Series(name=updates['series'])
                        calibre_db.session.add(series)
                    
                    # Clear existing series
                    book.series = [series]
                    updated = True
                
                if 'rating' in updates:
                    # Set rating
                    rating_value = int(updates['rating']) * 2  # Convert to 0-10 scale
                    rating = calibre_db.session.query(db.Ratings).filter(db.Ratings.rating == rating_value).first()
                    if not rating:
                        rating = db.Ratings(rating=rating_value)
                        calibre_db.session.add(rating)
                    
                    book.ratings = [rating]
                    updated = True
                
                if updated:
                    calibre_db.session.commit()
                    
                    # Update search index
                    book_indexer.update_book_index(book_id)
                    
                    results['success'].append(book_id)
                else:
                    results['failed'].append({'id': book_id, 'error': 'No updates applied'})
                    
            except Exception as e:
                log.error("Failed to update book %s: %s", book_id, e)
                results['failed'].append({'id': book_id, 'error': str(e)})
                calibre_db.session.rollback()
        
        return results
    
    def duplicate_detection(self, title: str, authors: List[str]) -> List[Dict[str, Any]]:
        """Detect potential duplicate books"""
        duplicates = []
        
        try:
            # Search for books with similar titles
            similar_books = calibre_db.session.query(db.Books).filter(
                db.Books.title.ilike(f"%{title}%")
            ).all()
            
            for book in similar_books:
                # Check author similarity
                book_authors = [author.name for author in book.authors]
                author_match = any(author.lower() in [ba.lower() for ba in book_authors] for author in authors)
                
                if author_match:
                    duplicates.append({
                        'id': book.id,
                        'title': book.title,
                        'authors': book_authors,
                        'similarity_score': self._calculate_similarity(title, book.title)
                    })
        
        except Exception as e:
            log.error("Duplicate detection failed: %s", e)
        
        return duplicates
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate similarity between two strings"""
        try:
            from difflib import SequenceMatcher
            return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()
        except:
            # Fallback simple similarity
            return 1.0 if str1.lower() == str2.lower() else 0.0
    
    def export_book_metadata(self, book_ids: List[int], format: str = 'json') -> str:
        """Export book metadata in various formats"""
        books_data = []
        
        for book_id in book_ids:
            book = calibre_db.get_book(book_id)
            if book:
                book_data = book_indexer.extract_book_data(book)
                books_data.append(book_data)
        
        if format.lower() == 'json':
            return json.dumps(books_data, indent=2, default=str)
        elif format.lower() == 'csv':
            return self._export_to_csv(books_data)
        else:
            raise ValueError(f"Unsupported export format: {format}")
    
    def _export_to_csv(self, books_data: List[Dict[str, Any]]) -> str:
        """Export books data to CSV format"""
        import csv
        import io
        
        output = io.StringIO()
        
        if not books_data:
            return ""
        
        fieldnames = books_data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        
        writer.writeheader()
        for book_data in books_data:
            # Convert lists to strings for CSV
            row = {}
            for key, value in book_data.items():
                if isinstance(value, list):
                    row[key] = '; '.join(str(v) for v in value)
                else:
                    row[key] = str(value) if value is not None else ''
            writer.writerow(row)
        
        return output.getvalue()
    
    def backup_book_data(self, book_id: int) -> Dict[str, Any]:
        """Create backup of book data before modifications"""
        book = calibre_db.get_book(book_id)
        if not book:
            return {}
        
        backup_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'book_id': book_id,
            'metadata': book_indexer.extract_book_data(book)
        }
        
        return backup_data
    
    def get_book_statistics(self) -> Dict[str, Any]:
        """Get comprehensive book statistics"""
        try:
            stats = {
                'total_books': calibre_db.session.query(db.Books).count(),
                'total_authors': calibre_db.session.query(db.Authors).count(),
                'total_series': calibre_db.session.query(db.Series).count(),
                'total_tags': calibre_db.session.query(db.Tags).count(),
                'total_publishers': calibre_db.session.query(db.Publishers).count(),
                'total_languages': calibre_db.session.query(db.Languages).count(),
                'formats': {},
                'recent_additions': 0
            }
            
            # Get format statistics
            formats = calibre_db.session.query(db.Data.format, db.func.count(db.Data.format)).group_by(db.Data.format).all()
            stats['formats'] = {format_name: count for format_name, count in formats}
            
            # Get recent additions (last 30 days)
            from datetime import timedelta
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            stats['recent_additions'] = calibre_db.session.query(db.Books).filter(
                db.Books.timestamp >= thirty_days_ago
            ).count()
            
            return stats
            
        except Exception as e:
            log.error("Failed to get book statistics: %s", e)
            return {}


# Global instance
enhanced_book_manager = EnhancedBookManager()
