# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

import os
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from . import logger, config

log = logger.create()

# Try to import Elasticsearch
try:
    from elasticsearch import Elasticsearch
    from elasticsearch.exceptions import ConnectionError, NotFoundError, RequestError
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    log.debug("Elasticsearch not available - install with: pip install elasticsearch")
    ELASTICSEARCH_AVAILABLE = False
    Elasticsearch = None
    ConnectionError = NotFoundError = RequestError = Exception


class ElasticsearchService:
    """Service for managing Elasticsearch operations"""
    
    def __init__(self):
        self.client = None
        self.index_name = None
        self.connected = False
        
    def is_available(self) -> bool:
        """Check if Elasticsearch is available and configured"""
        return (ELASTICSEARCH_AVAILABLE and 
                config.config_elasticsearch_enabled and 
                self.is_connected())
    
    def connect(self) -> bool:
        """Connect to Elasticsearch server"""
        if not ELASTICSEARCH_AVAILABLE:
            log.warning("Elasticsearch library not available")
            return False
            
        if not config.config_elasticsearch_enabled:
            log.debug("Elasticsearch not enabled in configuration")
            return False
            
        try:
            elasticsearch_settings = config.get_elasticsearch_settings()
            self.index_name = elasticsearch_settings['index']
            
            # Create Elasticsearch client
            self.client = Elasticsearch([{
                'host': elasticsearch_settings['host'],
                'port': elasticsearch_settings['port'],
                'scheme': 'http'
            }])
            
            # Test connection
            if self.client.ping():
                self.connected = True
                log.info("Connected to Elasticsearch at %s:%s", 
                        elasticsearch_settings['host'], elasticsearch_settings['port'])
                
                # Ensure index exists
                self._ensure_index_exists()
                return True
            else:
                log.error("Failed to ping Elasticsearch server")
                return False
                
        except Exception as e:
            log.error("Failed to connect to Elasticsearch: %s", e)
            self.connected = False
            return False
    
    def is_connected(self) -> bool:
        """Check if connected to Elasticsearch"""
        if not self.client:
            return False
            
        try:
            return self.client.ping()
        except Exception:
            return False
    
    def _ensure_index_exists(self):
        """Ensure the Elasticsearch index exists with proper mapping"""
        if not self.client or not self.index_name:
            return
            
        try:
            if not self.client.indices.exists(index=self.index_name):
                # Create index with mapping
                mapping = {
                    "mappings": {
                        "properties": {
                            "id": {"type": "integer"},
                            "title": {
                                "type": "text",
                                "analyzer": "standard",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "authors": {
                                "type": "text",
                                "analyzer": "standard",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "tags": {
                                "type": "text",
                                "analyzer": "standard"
                            },
                            "series": {
                                "type": "text",
                                "analyzer": "standard",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "publisher": {
                                "type": "text",
                                "analyzer": "standard",
                                "fields": {
                                    "keyword": {"type": "keyword"}
                                }
                            },
                            "description": {
                                "type": "text",
                                "analyzer": "standard"
                            },
                            "isbn": {"type": "keyword"},
                            "pubdate": {"type": "date"},
                            "timestamp": {"type": "date"},
                            "rating": {"type": "float"},
                            "languages": {"type": "keyword"},
                            "formats": {"type": "keyword"},
                            "path": {"type": "keyword"},
                            "has_cover": {"type": "boolean"}
                        }
                    }
                }
                
                self.client.indices.create(index=self.index_name, body=mapping)
                log.info("Created Elasticsearch index: %s", self.index_name)
                
        except Exception as e:
            log.error("Failed to create Elasticsearch index: %s", e)
    
    def index_book(self, book_data: Dict[str, Any]) -> bool:
        """Index a single book in Elasticsearch"""
        if not self.is_available():
            return False
            
        try:
            # Prepare document for indexing
            doc = {
                "id": book_data.get("id"),
                "title": book_data.get("title", ""),
                "authors": book_data.get("authors", []),
                "tags": book_data.get("tags", []),
                "series": book_data.get("series", ""),
                "publisher": book_data.get("publisher", ""),
                "description": book_data.get("description", ""),
                "isbn": book_data.get("isbn", ""),
                "pubdate": book_data.get("pubdate"),
                "timestamp": datetime.utcnow().isoformat(),
                "rating": book_data.get("rating", 0),
                "languages": book_data.get("languages", []),
                "formats": book_data.get("formats", []),
                "path": book_data.get("path", ""),
                "has_cover": book_data.get("has_cover", False)
            }
            
            # Index the document
            self.client.index(
                index=self.index_name,
                id=book_data.get("id"),
                body=doc
            )
            
            log.debug("Indexed book: %s (ID: %s)", book_data.get("title"), book_data.get("id"))
            return True
            
        except Exception as e:
            log.error("Failed to index book %s: %s", book_data.get("id"), e)
            return False
    
    def search_books(self, query: str, offset: int = 0, limit: int = 50) -> Tuple[List[int], int]:
        """Search books using Elasticsearch"""
        if not self.is_available():
            return [], 0

        try:
            # Build Elasticsearch query
            search_body = {
                "query": {
                    "multi_match": {
                        "query": query,
                        "fields": [
                            "title^3",      # Boost title matches
                            "authors^2",    # Boost author matches
                            "tags",
                            "series",
                            "publisher",
                            "description",
                            "isbn"
                        ],
                        "type": "best_fields",
                        "fuzziness": "AUTO"
                    }
                },
                "from": offset,
                "size": limit,
                "_source": ["id"],  # Only return book IDs
                "sort": [
                    {"_score": {"order": "desc"}},
                    {"title.keyword": {"order": "asc"}}
                ]
            }

            # Execute search
            response = self.client.search(
                index=self.index_name,
                body=search_body
            )

            # Extract book IDs from results
            book_ids = [hit["_source"]["id"] for hit in response["hits"]["hits"]]
            total_count = response["hits"]["total"]["value"]

            log.debug("Elasticsearch search for '%s' returned %d results", query, total_count)
            return book_ids, total_count

        except Exception as e:
            log.error("Elasticsearch search failed: %s", e)
            return [], 0

    def search_books_advanced(self, query_body: Dict[str, Any]) -> Tuple[List[int], int]:
        """Advanced search with custom Elasticsearch query"""
        if not self.is_available():
            return [], 0

        try:
            # Execute search with custom query
            response = self.client.search(
                index=self.index_name,
                body=query_body
            )

            # Extract book IDs from results
            book_ids = [hit["_source"]["id"] for hit in response["hits"]["hits"]]
            total_count = response["hits"]["total"]["value"]

            log.debug("Advanced Elasticsearch search returned %d results", total_count)
            return book_ids, total_count

        except Exception as e:
            log.error("Advanced Elasticsearch search failed: %s", e)
            return [], 0
    
    def delete_book(self, book_id: int) -> bool:
        """Delete a book from Elasticsearch index"""
        if not self.is_available():
            return False
            
        try:
            self.client.delete(index=self.index_name, id=book_id)
            log.debug("Deleted book from index: %s", book_id)
            return True
        except NotFoundError:
            log.debug("Book not found in index: %s", book_id)
            return True  # Consider it successful if already not there
        except Exception as e:
            log.error("Failed to delete book from index %s: %s", book_id, e)
            return False
    
    def reindex_all_books(self, books_data: List[Dict[str, Any]]) -> bool:
        """Reindex all books (bulk operation)"""
        if not self.is_available():
            return False
            
        try:
            # Clear existing index
            self.client.indices.delete(index=self.index_name, ignore=[404])
            self._ensure_index_exists()
            
            # Bulk index all books
            actions = []
            for book_data in books_data:
                action = {
                    "_index": self.index_name,
                    "_id": book_data.get("id"),
                    "_source": {
                        "id": book_data.get("id"),
                        "title": book_data.get("title", ""),
                        "authors": book_data.get("authors", []),
                        "tags": book_data.get("tags", []),
                        "series": book_data.get("series", ""),
                        "publisher": book_data.get("publisher", ""),
                        "description": book_data.get("description", ""),
                        "isbn": book_data.get("isbn", ""),
                        "pubdate": book_data.get("pubdate"),
                        "timestamp": datetime.utcnow().isoformat(),
                        "rating": book_data.get("rating", 0),
                        "languages": book_data.get("languages", []),
                        "formats": book_data.get("formats", []),
                        "path": book_data.get("path", ""),
                        "has_cover": book_data.get("has_cover", False)
                    }
                }
                actions.append(action)
            
            # Perform bulk indexing
            from elasticsearch.helpers import bulk
            bulk(self.client, actions)
            
            log.info("Reindexed %d books in Elasticsearch", len(books_data))
            return True
            
        except Exception as e:
            log.error("Failed to reindex books: %s", e)
            return False


# Global instance
elasticsearch_service = ElasticsearchService()
