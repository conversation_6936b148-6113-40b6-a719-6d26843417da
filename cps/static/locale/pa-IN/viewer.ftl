# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.


## Main toolbar buttons (tooltips and alt text for images)

pdfjs-previous-button =
    .title = ਪਿਛਲਾ ਸਫ਼ਾ
pdfjs-previous-button-label = ਪਿੱਛੇ
pdfjs-next-button =
    .title = ਅਗਲਾ ਸਫ਼ਾ
pdfjs-next-button-label = ਅੱਗੇ
# .title: Tooltip for the pageNumber input.
pdfjs-page-input =
    .title = ਸਫ਼ਾ
# Variables:
#   $pagesCount (Number) - the total number of pages in the document
# This string follows an input field with the number of the page currently displayed.
pdfjs-of-pages = { $pagesCount } ਵਿੱਚੋਂ
# Variables:
#   $pageNumber (Number) - the currently visible page
#   $pagesCount (Number) - the total number of pages in the document
pdfjs-page-of-pages = { $pagesCount }) ਵਿੱਚੋਂ ({ $pageNumber }
pdfjs-zoom-out-button =
    .title = ਜ਼ੂਮ ਆਉਟ
pdfjs-zoom-out-button-label = ਜ਼ੂਮ ਆਉਟ
pdfjs-zoom-in-button =
    .title = ਜ਼ੂਮ ਇਨ
pdfjs-zoom-in-button-label = ਜ਼ੂਮ ਇਨ
pdfjs-zoom-select =
    .title = ਜ਼ੂਨ
pdfjs-presentation-mode-button =
    .title = ਪਰਿਜੈਂਟੇਸ਼ਨ ਮੋਡ ਵਿੱਚ ਜਾਓ
pdfjs-presentation-mode-button-label = ਪਰਿਜੈਂਟੇਸ਼ਨ ਮੋਡ
pdfjs-open-file-button =
    .title = ਫਾਈਲ ਨੂੰ ਖੋਲ੍ਹੋ
pdfjs-open-file-button-label = ਖੋਲ੍ਹੋ
pdfjs-print-button =
    .title = ਪਰਿੰਟ
pdfjs-print-button-label = ਪਰਿੰਟ
pdfjs-save-button =
    .title = ਸੰਭਾਲੋ
pdfjs-save-button-label = ਸੰਭਾਲੋ
# Used in Firefox for Android as a tooltip for the download button (“download” is a verb).
pdfjs-download-button =
    .title = ਡਾਊਨਲੋਡ
# Used in Firefox for Android as a label for the download button (“download” is a verb).
# Length of the translation matters since we are in a mobile context, with limited screen estate.
pdfjs-download-button-label = ਡਾਊਨਲੋਡ
pdfjs-bookmark-button =
    .title = ਮੌਜੂਦਾ ਸਫ਼਼ਾ (ਮੌਜੂਦਾ ਸਫ਼ੇ ਤੋਂ URL ਵੇਖੋ)
pdfjs-bookmark-button-label = ਮੌਜੂਦਾ ਸਫ਼਼ਾ

##  Secondary toolbar and context menu

pdfjs-tools-button =
    .title = ਟੂਲ
pdfjs-tools-button-label = ਟੂਲ
pdfjs-first-page-button =
    .title = ਪਹਿਲੇ ਸਫ਼ੇ ਉੱਤੇ ਜਾਓ
pdfjs-first-page-button-label = ਪਹਿਲੇ ਸਫ਼ੇ ਉੱਤੇ ਜਾਓ
pdfjs-last-page-button =
    .title = ਆਖਰੀ ਸਫ਼ੇ ਉੱਤੇ ਜਾਓ
pdfjs-last-page-button-label = ਆਖਰੀ ਸਫ਼ੇ ਉੱਤੇ ਜਾਓ
pdfjs-page-rotate-cw-button =
    .title = ਸੱਜੇ ਦਾਅ ਘੁੰਮਾਓ
pdfjs-page-rotate-cw-button-label = ਸੱਜੇ ਦਾਅ ਘੁੰਮਾਓ
pdfjs-page-rotate-ccw-button =
    .title = ਖੱਬੇ ਦਾਅ ਘੁੰਮਾਓ
pdfjs-page-rotate-ccw-button-label = ਖੱਬੇ ਦਾਅ ਘੁੰਮਾਓ
pdfjs-cursor-text-select-tool-button =
    .title = ਲਿਖਤ ਚੋਣ ਟੂਲ ਸਮਰੱਥ ਕਰੋ
pdfjs-cursor-text-select-tool-button-label = ਲਿਖਤ ਚੋਣ ਟੂਲ
pdfjs-cursor-hand-tool-button =
    .title = ਹੱਥ ਟੂਲ ਸਮਰੱਥ ਕਰੋ
pdfjs-cursor-hand-tool-button-label = ਹੱਥ ਟੂਲ
pdfjs-scroll-page-button =
    .title = ਸਫ਼ਾ ਖਿਸਕਾਉਣ ਨੂੰ ਵਰਤੋਂ
pdfjs-scroll-page-button-label = ਸਫ਼ਾ ਖਿਸਕਾਉਣਾ
pdfjs-scroll-vertical-button =
    .title = ਖੜ੍ਹਵੇਂ ਸਕਰਾਉਣ ਨੂੰ ਵਰਤੋਂ
pdfjs-scroll-vertical-button-label = ਖੜ੍ਹਵਾਂ ਸਰਕਾਉਣਾ
pdfjs-scroll-horizontal-button =
    .title = ਲੇਟਵੇਂ ਸਰਕਾਉਣ ਨੂੰ ਵਰਤੋਂ
pdfjs-scroll-horizontal-button-label = ਲੇਟਵਾਂ ਸਰਕਾਉਣਾ
pdfjs-scroll-wrapped-button =
    .title = ਸਮੇਟੇ ਸਰਕਾਉਣ ਨੂੰ ਵਰਤੋਂ
pdfjs-scroll-wrapped-button-label = ਸਮੇਟਿਆ ਸਰਕਾਉਣਾ
pdfjs-spread-none-button =
    .title = ਸਫ਼ਾ ਫੈਲਾਅ ਵਿੱਚ ਸ਼ਾਮਲ ਨਾ ਹੋਵੋ
pdfjs-spread-none-button-label = ਕੋਈ ਫੈਲਾਅ ਨਹੀਂ
pdfjs-spread-odd-button =
    .title = ਟਾਂਕ ਅੰਕ ਵਾਲੇ ਸਫ਼ਿਆਂ ਨਾਲ ਸ਼ੁਰੂ ਹੋਣ ਵਾਲੇ ਸਫਿਆਂ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ
pdfjs-spread-odd-button-label = ਟਾਂਕ ਫੈਲਾਅ
pdfjs-spread-even-button =
    .title = ਜਿਸਤ ਅੰਕ ਵਾਲੇ ਸਫ਼ਿਆਂ ਨਾਲ ਸ਼ੁਰੂ ਹੋਣ ਵਾਲੇ ਸਫਿਆਂ ਵਿੱਚ ਸ਼ਾਮਲ ਹੋਵੋ
pdfjs-spread-even-button-label = ਜਿਸਤ ਫੈਲਾਅ

## Document properties dialog

pdfjs-document-properties-button =
    .title = …ਦਸਤਾਵੇਜ਼ ਦੀ ਵਿਸ਼ੇਸ਼ਤਾ
pdfjs-document-properties-button-label = …ਦਸਤਾਵੇਜ਼ ਦੀ ਵਿਸ਼ੇਸ਼ਤਾ
pdfjs-document-properties-file-name = ਫਾਈਲ ਦਾ ਨਾਂ:
pdfjs-document-properties-file-size = ਫਾਈਲ ਦਾ ਆਕਾਰ:
# Variables:
#   $size_kb (Number) - the PDF file size in kilobytes
#   $size_b (Number) - the PDF file size in bytes
pdfjs-document-properties-kb = { $size_kb } KB ({ $size_b } ਬਾਈਟ)
# Variables:
#   $size_mb (Number) - the PDF file size in megabytes
#   $size_b (Number) - the PDF file size in bytes
pdfjs-document-properties-mb = { $size_mb } MB ({ $size_b } ਬਾਈਟ)
pdfjs-document-properties-title = ਟਾਈਟਲ:
pdfjs-document-properties-author = ਲੇਖਕ:
pdfjs-document-properties-subject = ਵਿਸ਼ਾ:
pdfjs-document-properties-keywords = ਸ਼ਬਦ:
pdfjs-document-properties-creation-date = ਬਣਾਉਣ ਦੀ ਮਿਤੀ:
pdfjs-document-properties-modification-date = ਸੋਧ ਦੀ ਮਿਤੀ:
# Variables:
#   $date (Date) - the creation/modification date of the PDF file
#   $time (Time) - the creation/modification time of the PDF file
pdfjs-document-properties-date-string = { $date }, { $time }
pdfjs-document-properties-creator = ਨਿਰਮਾਤਾ:
pdfjs-document-properties-producer = PDF ਪ੍ਰੋਡਿਊਸਰ:
pdfjs-document-properties-version = PDF ਵਰਜਨ:
pdfjs-document-properties-page-count = ਸਫ਼ੇ ਦੀ ਗਿਣਤੀ:
pdfjs-document-properties-page-size = ਸਫ਼ਾ ਆਕਾਰ:
pdfjs-document-properties-page-size-unit-inches = ਇੰਚ
pdfjs-document-properties-page-size-unit-millimeters = ਮਿਮੀ
pdfjs-document-properties-page-size-orientation-portrait = ਪੋਰਟਰੇਟ
pdfjs-document-properties-page-size-orientation-landscape = ਲੈਂਡਸਕੇਪ
pdfjs-document-properties-page-size-name-a-three = A3
pdfjs-document-properties-page-size-name-a-four = A4
pdfjs-document-properties-page-size-name-letter = ਲੈਟਰ
pdfjs-document-properties-page-size-name-legal = ਕਨੂੰਨੀ

## Variables:
##   $width (Number) - the width of the (current) page
##   $height (Number) - the height of the (current) page
##   $unit (String) - the unit of measurement of the (current) page
##   $name (String) - the name of the (current) page
##   $orientation (String) - the orientation of the (current) page

pdfjs-document-properties-page-size-dimension-string = { $width } × { $height } { $unit } ({ $orientation })
pdfjs-document-properties-page-size-dimension-name-string = { $width } × { $height } { $unit } ({ $name }, { $orientation })

##

# The linearization status of the document; usually called "Fast Web View" in
# English locales of Adobe software.
pdfjs-document-properties-linearized = ਤੇਜ਼ ਵੈੱਬ ਝਲਕ:
pdfjs-document-properties-linearized-yes = ਹਾਂ
pdfjs-document-properties-linearized-no = ਨਹੀਂ
pdfjs-document-properties-close-button = ਬੰਦ ਕਰੋ

## Print

pdfjs-print-progress-message = …ਪਰਿੰਟ ਕਰਨ ਲਈ ਦਸਤਾਵੇਜ਼ ਨੂੰ ਤਿਆਰ ਕੀਤਾ ਜਾ ਰਿਹਾ ਹੈ
# Variables:
#   $progress (Number) - percent value
pdfjs-print-progress-percent = { $progress }%
pdfjs-print-progress-close-button = ਰੱਦ ਕਰੋ
pdfjs-printing-not-supported = ਸਾਵਧਾਨ: ਇਹ ਬਰਾਊਜ਼ਰ ਪਰਿੰਟ ਕਰਨ ਲਈ ਪੂਰੀ ਤਰ੍ਹਾਂ ਸਹਾਇਕ ਨਹੀਂ ਹੈ।
pdfjs-printing-not-ready = ਸਾਵਧਾਨ: PDF ਨੂੰ ਪਰਿੰਟ ਕਰਨ ਲਈ ਪੂਰੀ ਤਰ੍ਹਾਂ ਲੋਡ ਨਹੀਂ ਹੈ।

## Tooltips and alt text for side panel toolbar buttons

pdfjs-toggle-sidebar-button =
    .title = ਬਾਹੀ ਬਦਲੋ
pdfjs-toggle-sidebar-notification-button =
    .title = ਬਾਹੀ ਨੂੰ ਬਦਲੋ (ਦਸਤਾਵੇਜ਼ ਖਾਕਾ/ਅਟੈਚਮੈਂਟ/ਪਰਤਾਂ ਰੱਖਦਾ ਹੈ)
pdfjs-toggle-sidebar-button-label = ਬਾਹੀ ਬਦਲੋ
pdfjs-document-outline-button =
    .title = ਦਸਤਾਵੇਜ਼ ਖਾਕਾ ਦਿਖਾਓ (ਸਾਰੀਆਂ ਆਈਟਮਾਂ ਨੂੰ ਫੈਲਾਉਣ/ਸਮੇਟਣ ਲਈ ਦੋ ਵਾਰ ਕਲਿੱਕ ਕਰੋ)
pdfjs-document-outline-button-label = ਦਸਤਾਵੇਜ਼ ਖਾਕਾ
pdfjs-attachments-button =
    .title = ਅਟੈਚਮੈਂਟ ਵੇਖਾਓ
pdfjs-attachments-button-label = ਅਟੈਚਮੈਂਟਾਂ
pdfjs-layers-button =
    .title = ਪਰਤਾਂ ਵੇਖਾਓ (ਸਾਰੀਆਂ ਪਰਤਾਂ ਨੂੰ ਮੂਲ ਹਾਲਤ ਉੱਤੇ ਮੁੜ-ਸੈੱਟ ਕਰਨ ਲਈ ਦੋ ਵਾਰ ਕਲਿੱਕ ਕਰੋ)
pdfjs-layers-button-label = ਪਰਤਾਂ
pdfjs-thumbs-button =
    .title = ਥੰਮਨੇਲ ਨੂੰ ਵੇਖਾਓ
pdfjs-thumbs-button-label = ਥੰਮਨੇਲ
pdfjs-current-outline-item-button =
    .title = ਮੌੌਜੂਦਾ ਖਾਕਾ ਚੀਜ਼ ਲੱਭੋ
pdfjs-current-outline-item-button-label = ਮੌਜੂਦਾ ਖਾਕਾ ਚੀਜ਼
pdfjs-findbar-button =
    .title = ਦਸਤਾਵੇਜ਼ ਵਿੱਚ ਲੱਭੋ
pdfjs-findbar-button-label = ਲੱਭੋ
pdfjs-additional-layers = ਵਾਧੂ ਪਰਤਾਂ

## Thumbnails panel item (tooltip and alt text for images)

# Variables:
#   $page (Number) - the page number
pdfjs-thumb-page-title =
    .title = ਸਫ਼ਾ { $page }
# Variables:
#   $page (Number) - the page number
pdfjs-thumb-page-canvas =
    .aria-label = { $page } ਸਫ਼ੇ ਦਾ ਥੰਮਨੇਲ

## Find panel button title and messages

pdfjs-find-input =
    .title = ਲੱਭੋ
    .placeholder = …ਦਸਤਾਵੇਜ਼ 'ਚ ਲੱਭੋ
pdfjs-find-previous-button =
    .title = ਵਾਕ ਦੀ ਪਿਛਲੀ ਮੌਜੂਦਗੀ ਲੱਭੋ
pdfjs-find-previous-button-label = ਪਿੱਛੇ
pdfjs-find-next-button =
    .title = ਵਾਕ ਦੀ ਅਗਲੀ ਮੌਜੂਦਗੀ ਲੱਭੋ
pdfjs-find-next-button-label = ਅੱਗੇ
pdfjs-find-highlight-checkbox = ਸਭ ਉਭਾਰੋ
pdfjs-find-match-case-checkbox-label = ਅੱਖਰ ਆਕਾਰ ਨੂੰ ਮਿਲਾਉ
pdfjs-find-match-diacritics-checkbox-label = ਭੇਦਸੂਚਕ ਮੇਲ
pdfjs-find-entire-word-checkbox-label = ਪੂਰੇ ਸ਼ਬਦ
pdfjs-find-reached-top = ਦਸਤਾਵੇਜ਼ ਦੇ ਉੱਤੇ ਆ ਗਏ ਹਾਂ, ਥੱਲੇ ਤੋਂ ਜਾਰੀ ਰੱਖਿਆ ਹੈ
pdfjs-find-reached-bottom = ਦਸਤਾਵੇਜ਼ ਦੇ ਅੰਤ ਉੱਤੇ ਆ ਗਏ ਹਾਂ, ਉੱਤੇ ਤੋਂ ਜਾਰੀ ਰੱਖਿਆ ਹੈ
# Variables:
#   $current (Number) - the index of the currently active find result
#   $total (Number) - the total number of matches in the document
pdfjs-find-match-count =
    { $total ->
        [one] { $total } ਵਿੱਚੋਂ { $current } ਮੇਲ
       *[other] { $total } ਵਿੱਚੋਂ { $current } ਮੇਲ
    }
# Variables:
#   $limit (Number) - the maximum number of matches
pdfjs-find-match-count-limit =
    { $limit ->
        [one] { $limit } ਤੋਂ ਵੱਧ ਮੇਲ
       *[other] { $limit } ਤੋਂ ਵੱਧ ਮੇਲ
    }
pdfjs-find-not-found = ਵਾਕ ਨਹੀਂ ਲੱਭਿਆ

## Predefined zoom values

pdfjs-page-scale-width = ਸਫ਼ੇ ਦੀ ਚੌੜਾਈ
pdfjs-page-scale-fit = ਸਫ਼ਾ ਫਿੱਟ
pdfjs-page-scale-auto = ਆਟੋਮੈਟਿਕ ਜ਼ੂਮ ਕਰੋ
pdfjs-page-scale-actual = ਆਟੋਮੈਟਿਕ ਆਕਾਰ
# Variables:
#   $scale (Number) - percent value for page scale
pdfjs-page-scale-percent = { $scale }%

## PDF page

# Variables:
#   $page (Number) - the page number
pdfjs-page-landmark =
    .aria-label = ਸਫ਼ਾ { $page }

## Loading indicator messages

pdfjs-loading-error = PDF ਲੋਡ ਕਰਨ ਦੇ ਦੌਰਾਨ ਗਲਤੀ ਆਈ ਹੈ।
pdfjs-invalid-file-error = ਗਲਤ ਜਾਂ ਨਿਕਾਰਾ PDF ਫਾਈਲ ਹੈ।
pdfjs-missing-file-error = ਨਾ-ਮੌਜੂਦ PDF ਫਾਈਲ।
pdfjs-unexpected-response-error = ਅਣਜਾਣ ਸਰਵਰ ਜਵਾਬ।
pdfjs-rendering-error = ਸਫ਼ਾ ਰੈਡਰ ਕਰਨ ਦੇ ਦੌਰਾਨ ਗਲਤੀ ਆਈ ਹੈ।

## Annotations

# Variables:
#   $date (Date) - the modification date of the annotation
#   $time (Time) - the modification time of the annotation
pdfjs-annotation-date-string = { $date }, { $time }
# .alt: This is used as a tooltip.
# Variables:
#   $type (String) - an annotation type from a list defined in the PDF spec
# (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
pdfjs-text-annotation-type =
    .alt = [{ $type } ਵਿਆਖਿਆ]

## Password

pdfjs-password-label = ਇਹ PDF ਫਾਈਲ ਨੂੰ ਖੋਲ੍ਹਣ ਲਈ ਪਾਸਵਰਡ ਦਿਉ।
pdfjs-password-invalid = ਗਲਤ ਪਾਸਵਰਡ। ਫੇਰ ਕੋਸ਼ਿਸ਼ ਕਰੋ ਜੀ।
pdfjs-password-ok-button = ਠੀਕ ਹੈ
pdfjs-password-cancel-button = ਰੱਦ ਕਰੋ
pdfjs-web-fonts-disabled = ਵੈਬ ਫੋਂਟ ਬੰਦ ਹਨ: ਇੰਬੈਡ PDF ਫੋਂਟ ਨੂੰ ਵਰਤਣ ਲਈ ਅਸਮਰੱਥ ਹੈ।

## Editing

pdfjs-editor-free-text-button =
    .title = ਲਿਖਤ
pdfjs-editor-free-text-button-label = ਲਿਖਤ
pdfjs-editor-ink-button =
    .title = ਵਾਹੋ
pdfjs-editor-ink-button-label = ਵਾਹੋ
pdfjs-editor-stamp-button =
    .title = ਚਿੱਤਰ ਜੋੜੋ ਜਾਂ ਸੋਧੋ
pdfjs-editor-stamp-button-label = ਚਿੱਤਰ ਜੋੜੋ ਜਾਂ ਸੋਧੋ
pdfjs-editor-highlight-button =
    .title = ਹਾਈਲਾਈਟ
pdfjs-editor-highlight-button-label = ਹਾਈਲਾਈਟ
pdfjs-highlight-floating-button =
    .title = ਹਾਈਲਾਈਟ
pdfjs-highlight-floating-button1 =
    .title = ਹਾਈਲਾਈਟ
    .aria-label = ਹਾਈਲਾਈਟ
pdfjs-highlight-floating-button-label = ਹਾਈਲਾਈਟ

## Remove button for the various kind of editor.

pdfjs-editor-remove-ink-button =
    .title = ਡਰਾਇੰਗ ਨੂੰ ਹਟਾਓ
pdfjs-editor-remove-freetext-button =
    .title = ਲਿਖਤ ਨੂੰ ਹਟਾਓ
pdfjs-editor-remove-stamp-button =
    .title = ਚਿੱਤਰ ਨੂੰ ਹਟਾਓ
pdfjs-editor-remove-highlight-button =
    .title = ਹਾਈਲਾਈਟ ਨੂੰ ਹਟਾਓ

##

# Editor Parameters
pdfjs-editor-free-text-color-input = ਰੰਗ
pdfjs-editor-free-text-size-input = ਆਕਾਰ
pdfjs-editor-ink-color-input = ਰੰਗ
pdfjs-editor-ink-thickness-input = ਮੋਟਾਈ
pdfjs-editor-ink-opacity-input = ਧੁੰਦਲਾਪਨ
pdfjs-editor-stamp-add-image-button =
    .title = ਚਿੱਤਰ ਜੋੜੋ
pdfjs-editor-stamp-add-image-button-label = ਚਿੱਤਰ ਜੋੜੋ
# This refers to the thickness of the line used for free highlighting (not bound to text)
pdfjs-editor-free-highlight-thickness-input = ਮੋਟਾਈ
pdfjs-editor-free-highlight-thickness-title =
    .title = ਚੀਜ਼ਾਂ ਨੂੰ ਹੋਰ ਲਿਖਤਾਂ ਤੋਂ ਉਘਾੜਨ ਸਮੇਂ ਮੋਟਾਈ ਨੂੰ ਬਦਲੋ
pdfjs-free-text =
    .aria-label = ਲਿਖਤ ਐਡੀਟਰ
pdfjs-free-text-default-content = …ਲਿਖਣਾ ਸ਼ੁਰੂ ਕਰੋ
pdfjs-ink =
    .aria-label = ਵਹਾਉਣ ਐਡੀਟਰ
pdfjs-ink-canvas =
    .aria-label = ਵਰਤੋਂਕਾਰ ਵਲੋਂ ਬਣਾਇਆ ਚਿੱਤਰ

## Alt-text dialog

# Alternative text (alt text) helps when people can't see the image.
pdfjs-editor-alt-text-button-label = ਬਦਲਵੀਂ ਲਿਖਤ
pdfjs-editor-alt-text-edit-button-label = ਬਦਲਵੀ ਲਿਖਤ ਨੂੰ ਸੋਧੋ
pdfjs-editor-alt-text-dialog-label = ਚੋਣ ਕਰੋ
pdfjs-editor-alt-text-dialog-description = ਚਿੱਤਰ ਨਾ ਦਿੱਸਣ ਜਾਂ ਲੋਡ ਨਾ ਹੋਣ ਦੀ ਹਾਲਤ ਵਿੱਚ Alt ਲਿਖਤ (ਬਦਲਵੀਂ ਲਿਖਤ) ਲੋਕਾਂ ਲਈ ਮਦਦਗਾਰ ਹੁੰਦੀ ਹੈ।
pdfjs-editor-alt-text-add-description-label = ਵਰਣਨ ਜੋੜੋ
pdfjs-editor-alt-text-add-description-description = 1-2 ਵਾਕ ਰੱਖੋ, ਜੋ ਕਿ ਵਿਸ਼ੇ, ਸੈਟਿੰਗ ਜਾਂ ਕਾਰਵਾਈਆਂ ਬਾਰੇ ਦਰਸਾਉਂਦੇ ਹੋਣ।
pdfjs-editor-alt-text-mark-decorative-label = ਸਜਾਵਟ ਵਜੋਂ ਨਿਸ਼ਾਨ ਲਾਇਆ
pdfjs-editor-alt-text-mark-decorative-description = ਇਸ ਨੂੰ ਸਜਾਵਟੀ ਚਿੱਤਰਾਂ ਲਈ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ ਜਿਵੇਂ ਕਿ ਹਾਸ਼ੀਆ ਜਾਂ ਵਾਟਰਮਾਰਕ ਆਦਿ।
pdfjs-editor-alt-text-cancel-button = ਰੱਦ ਕਰੋ
pdfjs-editor-alt-text-save-button = ਸੰਭਾਲੋ
pdfjs-editor-alt-text-decorative-tooltip = ਸਜਾਵਟ ਵਜੋਂ ਨਿਸ਼ਾਨ ਲਾਓ
# .placeholder: This is a placeholder for the alt text input area
pdfjs-editor-alt-text-textarea =
    .placeholder = ਮਿਸਾਲ ਵਜੋਂ, “ਗੱਭਰੂ ਭੋਜਨ ਲੈ ਕੇ ਮੇਜ਼ ਉੱਤੇ ਬੈਠਾ ਹੈ”

## Editor resizers
## This is used in an aria label to help to understand the role of the resizer.

pdfjs-editor-resizer-label-top-left = ਉੱਤੇ ਖੱਬਾ ਕੋਨਾ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-top-middle = ਉੱਤੇ ਮੱਧ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-top-right = ਉੱਤੇ ਸੱਜਾ ਕੋਨਾ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-middle-right = ਮੱਧ ਸੱਜਾ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-bottom-right = ਹੇਠਾਂ ਸੱਜਾ ਕੋਨਾ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-bottom-middle = ਹੇਠਾਂ ਮੱਧ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-bottom-left = ਹੇਠਾਂ ਖੱਬਾ ਕੋਨਾ — ਮੁੜ-ਆਕਾਰ ਕਰੋ
pdfjs-editor-resizer-label-middle-left = ਮੱਧ ਖੱਬਾ — ਮੁੜ-ਆਕਾਰ ਕਰੋ

## Color picker

# This means "Color used to highlight text"
pdfjs-editor-highlight-colorpicker-label = ਹਾਈਟਲਾਈਟ ਦਾ ਰੰਗ
pdfjs-editor-colorpicker-button =
    .title = ਰੰਗ ਨੂੰ ਬਦਲੋ
pdfjs-editor-colorpicker-dropdown =
    .aria-label = ਰੰਗ ਚੋਣਾਂ
pdfjs-editor-colorpicker-yellow =
    .title = ਪੀਲਾ
pdfjs-editor-colorpicker-green =
    .title = ਹਰਾ
pdfjs-editor-colorpicker-blue =
    .title = ਨੀਲਾ
pdfjs-editor-colorpicker-pink =
    .title = ਗੁਲਾਬੀ
pdfjs-editor-colorpicker-red =
    .title = ਲਾਲ

## Show all highlights
## This is a toggle button to show/hide all the highlights.

pdfjs-editor-highlight-show-all-button-label = ਸਭ ਵੇਖੋ
pdfjs-editor-highlight-show-all-button =
    .title = ਸਭ ਵੇਖੋ
