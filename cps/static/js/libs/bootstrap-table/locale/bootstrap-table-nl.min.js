/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),c=function(t){try{return!!t()}catch(t){return!0}},f=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!a.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:a},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},g={}.toString,d=function(t){return g.call(t).slice(8,-1)},y="".split,m=c((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==d(t)?y.call(t,""):Object(t)}:Object,h=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return m(h(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=u.document,j=b(O)&&b(O.createElement),P=!f&&!c((function(){return 7!=Object.defineProperty((t="div",j?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,A={f:f?x:function(t,n){if(t=v(t),n=w(n,!0),P)try{return x(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},E=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},C=Object.defineProperty,M={f:f?C:function(t,n,r){if(E(t),n=w(n,!0),E(r),P)try{return C(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},k=f?function(t,n,r){return M.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},R=function(t,n){try{k(u,t,n)}catch(r){u[t]=n}return n},F="__core-js_shared__",L=u[F]||R(F,{}),N=Function.toString;"function"!=typeof L.inspectSource&&(L.inspectSource=function(t){return N.call(t)});var V,I,_,D,G=L.inspectSource,q=u.WeakMap,z="function"==typeof q&&/native code/.test(G(q)),B=o((function(t){(t.exports=function(t,n){return L[t]||(L[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),K=0,W=Math.random(),H=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++K+W).toString(36)},J=B("keys"),Q={},U=u.WeakMap;if(z){var Y=L.state||(L.state=new U),Z=Y.get,X=Y.has,$=Y.set;V=function(t,n){return n.facade=t,$.call(Y,t,n),n},I=function(t){return Z.call(Y,t)||{}},_=function(t){return X.call(Y,t)}}else{var tt=J[D="state"]||(J[D]=H(D));Q[tt]=!0,V=function(t,n){return n.facade=t,k(t,tt,n),n},I=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:V,get:I,has:_,enforce:function(t){return _(t)?I(t):V(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=I(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var c,f=!!i&&!!i.unsafe,a=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||k(o,"name",n),(c=r(o)).source||(c.source=e.join("string"==typeof n?n:""))),t!==u?(f?!l&&t[n]&&(a=!0):delete t[n],a?t[n]=o:k(t,n,o)):a?t[n]=o:R(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||G(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},ct=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ft=Math.ceil,at=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?at:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},gt=Math.max,dt=Math.min,yt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),c=function(t,n){var r=lt(t);return r<0?gt(r+n,0):dt(r,n)}(e,u);if(t&&r!=r){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===r)return t||c||0;return!t&&-1}},mt={includes:yt(!0),indexOf:yt(!1)}.indexOf,ht=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!T(Q,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~mt(i,r)||i.push(r));return i}(t,ht)}},bt={f:Object.getOwnPropertySymbols},wt=ct("Reflect","ownKeys")||function(t){var n=vt.f(E(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=M.f,o=A.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=Pt[jt(t)];return r==At||r!=xt&&("function"==typeof n?c(n):!!n)},jt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},Pt=Ot.data={},xt=Ot.NATIVE="N",At=Ot.POLYFILL="P",Et=Ot,Ct=A.f,Mt=Array.isArray||function(t){return"Array"==d(t)},kt=function(t){return Object(h(t))},Rt=function(t,n,r){var e=w(n);e in t?M.f(t,e,p(0,r)):t[e]=r},Ft="process"==d(u.process),Lt=ct("navigator","userAgent")||"",Nt=u.process,Vt=Nt&&Nt.versions,It=Vt&&Vt.v8;It?rt=(nt=It.split("."))[0]+nt[1]:Lt&&(!(nt=Lt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Lt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,Gt=!!Object.getOwnPropertySymbols&&!c((function(){return!Symbol.sham&&(Ft?38===Dt:Dt>37&&Dt<41)})),qt=Gt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,zt=B("wks"),Bt=u.Symbol,Kt=qt?Bt:Bt&&Bt.withoutSetter||H,Wt=function(t){return T(zt,t)&&(Gt||"string"==typeof zt[t])||(Gt&&T(Bt,t)?zt[t]=Bt[t]:zt[t]=Kt("Symbol."+t)),zt[t]},Ht=Wt("species"),Jt=function(t,n){var r;return Mt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Mt(r.prototype)?b(r)&&null===(r=r[Ht])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Wt("species"),Ut=Wt("isConcatSpreadable"),Yt=9007199254740991,Zt="Maximum allowed index exceeded",Xt=Dt>=51||!c((function(){var t=[];return t[Ut]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!c((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Ut];return void 0!==n?!!n:Mt(t)};!function(t,n){var r,e,o,i,c,f=t.target,a=t.global,l=t.stat;if(r=a?u:l?u[f]||R(f,{}):(u[f]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(c=Ct(r,e))&&c.value:r[e],!Et(a?e:f+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&k(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Xt||!$t},{concat:function(t){var n,r,e,o,i,u=kt(this),c=Jt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(f+(o=pt(i.length))>Yt)throw TypeError(Zt);for(r=0;r<o;r++,f++)r in i&&Rt(c,f,i[r])}else{if(f>=Yt)throw TypeError(Zt);Rt(c,f++,i)}return c.length=f,c}}),r.default.fn.bootstrapTable.locales["nl-NL"]=r.default.fn.bootstrapTable.locales.nl={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Laden, even geduld"},formatRecordsPerPage:function(t){return"".concat(t," records per pagina")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":""," (gefilterd van ").concat(e," records in totaal)"):"Toon ".concat(t," tot ").concat(n," van ").concat(r," record").concat(r>1?"s":"")},formatSRPaginationPreText:function(){return"vorige pagina"},formatSRPaginationPageText:function(t){return"tot pagina ".concat(t)},formatSRPaginationNextText:function(){return"volgende pagina"},formatDetailPagination:function(t){return"Toon ".concat(t," record").concat(t>1?"s":"")},formatClearSearch:function(){return"Verwijder filters"},formatSearch:function(){return"Zoeken"},formatNoMatches:function(){return"Geen resultaten gevonden"},formatPaginationSwitch:function(){return"Verberg/Toon paginering"},formatPaginationSwitchDown:function(){return"Toon paginering"},formatPaginationSwitchUp:function(){return"Verberg paginering"},formatRefresh:function(){return"Vernieuwen"},formatToggle:function(){return"Omschakelen"},formatToggleOn:function(){return"Toon kaartweergave"},formatToggleOff:function(){return"Verberg kaartweergave"},formatColumns:function(){return"Kolommen"},formatColumnsToggleAll:function(){return"Allen omschakelen"},formatFullscreen:function(){return"Volledig scherm"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisch vernieuwen"},formatExport:function(){return"Exporteer gegevens"},formatJumpTo:function(){return"GA"},formatAdvancedSearch:function(){return"Geavanceerd zoeken"},formatAdvancedCloseButton:function(){return"Sluiten"},formatFilterControlSwitch:function(){return"Verberg/Toon controls"},formatFilterControlSwitchHide:function(){return"Verberg controls"},formatFilterControlSwitchShow:function(){return"Toon controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["nl-NL"])}));
