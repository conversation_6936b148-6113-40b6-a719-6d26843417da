/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!f.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:f},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},y="".split,h=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?y.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return h(m(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!v(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!v(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,P=function(t,n){return S.call(t,n)},O=u.document,T=v(O)&&v(O.createElement),j=!c&&!a((function(){return 7!=Object.defineProperty((t="div",T?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,E={f:c?x:function(t,n){if(t=b(t),n=w(n,!0),j)try{return x(t,n)}catch(t){}if(P(t,n))return p(!s.f.call(t,n),t[n])}},A=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},C=Object.defineProperty,M={f:c?C:function(t,n,r){if(A(t),n=w(n,!0),A(r),j)try{return C(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},R=c?function(t,n,r){return M.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{R(u,t,n)}catch(r){u[t]=n}return n},N="__core-js_shared__",q=u[N]||F(N,{}),I=Function.toString;"function"!=typeof q.inspectSource&&(q.inspectSource=function(t){return I.call(t)});var L,k,_,D,z=q.inspectSource,B=u.WeakMap,G="function"==typeof B&&/native code/.test(z(B)),H=o((function(t){(t.exports=function(t,n){return q[t]||(q[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),W=0,J=Math.random(),K=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++W+J).toString(36)},Q=H("keys"),U={},V=u.WeakMap;if(G){var Y=q.state||(q.state=new V),X=Y.get,Z=Y.has,$=Y.set;L=function(t,n){return n.facade=t,$.call(Y,t,n),n},k=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=Q[D="state"]||(Q[D]=K(D));U[tt]=!0,L=function(t,n){return n.facade=t,R(t,tt,n),n},k=function(t){return P(t,tt)?t[tt]:{}},_=function(t){return P(t,tt)}}var nt,rt,et={set:L,get:k,has:_,enforce:function(t){return _(t)?k(t):L(t,{})},getterFor:function(t){return function(n){var r;if(!v(n)||(r=k(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,c=!!i&&!!i.unsafe,f=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||P(o,"name")||R(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(c?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=o:R(t,n,o)):f?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||z(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ct=Math.ceil,ft=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ft:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,yt=function(t){return function(n,r,e){var o,i=b(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ht={includes:yt(!0),indexOf:yt(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=b(t),o=0,i=[];for(r in e)!P(U,r)&&P(e,r)&&i.push(r);for(;n.length>o;)P(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,mt)}},vt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=bt.f(A(t)),r=vt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=M.f,o=E.f,i=0;i<r.length;i++){var u=r[i];P(t,u)||e(t,u,o(n,u))}},Pt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Tt(t)];return r==Et||r!=xt&&("function"==typeof n?a(n):!!n)},Tt=Ot.normalize=function(t){return String(t).replace(Pt,".").toLowerCase()},jt=Ot.data={},xt=Ot.NATIVE="N",Et=Ot.POLYFILL="P",At=Ot,Ct=E.f,Mt=Array.isArray||function(t){return"Array"==g(t)},Rt=function(t){return Object(m(t))},Ft=function(t,n,r){var e=w(n);e in t?M.f(t,e,p(0,r)):t[e]=r},Nt="process"==g(u.process),qt=at("navigator","userAgent")||"",It=u.process,Lt=It&&It.versions,kt=Lt&&Lt.v8;kt?rt=(nt=kt.split("."))[0]+nt[1]:qt&&(!(nt=qt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=qt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,zt=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(Nt?38===Dt:Dt>37&&Dt<41)})),Bt=zt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=H("wks"),Ht=u.Symbol,Wt=Bt?Ht:Ht&&Ht.withoutSetter||K,Jt=function(t){return P(Gt,t)&&(zt||"string"==typeof Gt[t])||(zt&&P(Ht,t)?Gt[t]=Ht[t]:Gt[t]=Wt("Symbol."+t)),Gt[t]},Kt=Jt("species"),Qt=function(t,n){var r;return Mt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Mt(r.prototype)?v(r)&&null===(r=r[Kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Jt("species"),Vt=Jt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Dt>=51||!a((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!a((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!v(t))return!1;var n=t[Vt];return void 0!==n?!!n:Mt(t)};!function(t,n){var r,e,o,i,a,c=t.target,f=t.global,l=t.stat;if(r=f?u:l?u[c]||F(c,{}):(u[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=Ct(r,e))&&a.value:r[e],!At(f?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&R(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Rt(this),a=Qt(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(c+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in i&&Ft(a,c,i[r])}else{if(c>=Yt)throw TypeError(Xt);Ft(a,c++,i)}return a.length=c,a}}),r.default.fn.bootstrapTable.locales["pt-BR"]={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Carregando, aguarde"},formatRecordsPerPage:function(t){return"".concat(t," registros por página")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Exibindo ".concat(t," até ").concat(n," de ").concat(r," linhas (filtradas de um total de ").concat(e," linhas)"):"Exibindo ".concat(t," até ").concat(n," de ").concat(r," linhas")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"Para a página ".concat(t)},formatSRPaginationNextText:function(){return"próxima página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," linhas")},formatClearSearch:function(){return"Limpar Pesquisa"},formatSearch:function(){return"Pesquisar"},formatNoMatches:function(){return"Nenhum registro encontrado"},formatPaginationSwitch:function(){return"Ocultar/Exibir paginação"},formatPaginationSwitchDown:function(){return"Mostrar Paginação"},formatPaginationSwitchUp:function(){return"Esconder Paginação"},formatRefresh:function(){return"Recarregar"},formatToggle:function(){return"Alternar"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Colunas"},formatColumnsToggleAll:function(){return"Alternar tudo"},formatFullscreen:function(){return"Tela cheia"},formatAllRows:function(){return"Tudo"},formatAutoRefresh:function(){return"Atualização Automática"},formatExport:function(){return"Exportar dados"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Pesquisa Avançada"},formatAdvancedCloseButton:function(){return"Fechar"},formatFilterControlSwitch:function(){return"Ocultar/Exibir controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Exibir controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["pt-BR"])}));
