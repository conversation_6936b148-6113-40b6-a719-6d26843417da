/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},a=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},y={}.toString,d=function(t){return y.call(t).slice(8,-1)},g="".split,h=f((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==d(t)?g.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return h(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,P=function(t,n){return S.call(t,n)},T=u.document,j=b(T)&&b(T.createElement),O=!a&&!f((function(){return 7!=Object.defineProperty((t="div",j?T.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,A={f:a?x:function(t,n){if(t=v(t),n=w(n,!0),O)try{return x(t,n)}catch(t){}if(P(t,n))return p(!s.f.call(t,n),t[n])}},C=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,M={f:a?E:function(t,n,r){if(C(t),n=w(n,!0),C(r),O)try{return E(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},k=a?function(t,n,r){return M.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{k(u,t,n)}catch(r){u[t]=n}return n},R="__core-js_shared__",N=u[R]||F(R,{}),I=Function.toString;"function"!=typeof N.inspectSource&&(N.inspectSource=function(t){return I.call(t)});var H,L,_,D,q=N.inspectSource,z=u.WeakMap,G="function"==typeof z&&/native code/.test(q(z)),V=o((function(t){(t.exports=function(t,n){return N[t]||(N[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),B=0,K=Math.random(),W=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++B+K).toString(36)},J=V("keys"),Q={},U=u.WeakMap;if(G){var Y=N.state||(N.state=new U),X=Y.get,Z=Y.has,$=Y.set;H=function(t,n){return n.facade=t,$.call(Y,t,n),n},L=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=J[D="state"]||(J[D]=W(D));Q[tt]=!0,H=function(t,n){return n.facade=t,k(t,tt,n),n},L=function(t){return P(t,tt)?t[tt]:{}},_=function(t){return P(t,tt)}}var nt,rt,et={set:H,get:L,has:_,enforce:function(t){return _(t)?L(t):H(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=L(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var f,a=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||P(o,"name")||k(o,"name",n),(f=r(o)).source||(f.source=e.join("string"==typeof n?n:""))),t!==u?(a?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:k(t,n,o)):c?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},ft=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},at=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:at)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},yt=Math.max,dt=Math.min,gt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),f=function(t,n){var r=lt(t);return r<0?yt(r+n,0):dt(r,n)}(e,u);if(t&&r!=r){for(;u>f;)if((o=i[f++])!=o)return!0}else for(;u>f;f++)if((t||f in i)&&i[f]===r)return t||f||0;return!t&&-1}},ht={includes:gt(!0),indexOf:gt(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!P(Q,r)&&P(e,r)&&i.push(r);for(;n.length>o;)P(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=ft("Reflect","ownKeys")||function(t){var n=vt.f(C(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=M.f,o=A.f,i=0;i<r.length;i++){var u=r[i];P(t,u)||e(t,u,o(n,u))}},Pt=/#|\.prototype\./,Tt=function(t,n){var r=Ot[jt(t)];return r==At||r!=xt&&("function"==typeof n?f(n):!!n)},jt=Tt.normalize=function(t){return String(t).replace(Pt,".").toLowerCase()},Ot=Tt.data={},xt=Tt.NATIVE="N",At=Tt.POLYFILL="P",Ct=Tt,Et=A.f,Mt=Array.isArray||function(t){return"Array"==d(t)},kt=function(t){return Object(m(t))},Ft=function(t,n,r){var e=w(n);e in t?M.f(t,e,p(0,r)):t[e]=r},Rt="process"==d(u.process),Nt=ft("navigator","userAgent")||"",It=u.process,Ht=It&&It.versions,Lt=Ht&&Ht.v8;Lt?rt=(nt=Lt.split("."))[0]+nt[1]:Nt&&(!(nt=Nt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Nt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!f((function(){return!Symbol.sham&&(Rt?38===Dt:Dt>37&&Dt<41)})),zt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=V("wks"),Vt=u.Symbol,Bt=zt?Vt:Vt&&Vt.withoutSetter||W,Kt=function(t){return P(Gt,t)&&(qt||"string"==typeof Gt[t])||(qt&&P(Vt,t)?Gt[t]=Vt[t]:Gt[t]=Bt("Symbol."+t)),Gt[t]},Wt=Kt("species"),Jt=function(t,n){var r;return Mt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Mt(r.prototype)?b(r)&&null===(r=r[Wt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Kt("species"),Ut=Kt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Dt>=51||!f((function(){var t=[];return t[Ut]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!f((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Ut];return void 0!==n?!!n:Mt(t)};!function(t,n){var r,e,o,i,f,a=t.target,c=t.global,l=t.stat;if(r=c?u:l?u[a]||F(a,{}):(u[a]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(f=Et(r,e))&&f.value:r[e],!Ct(c?e:a+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&k(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=kt(this),f=Jt(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(a+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,a++)r in i&&Ft(f,a,i[r])}else{if(a>=Yt)throw TypeError(Xt);Ft(f,a++,i)}return f.length=a,f}}),r.default.fn.bootstrapTable.locales["fi-FI"]=r.default.fn.bootstrapTable.locales.fi={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Ladataan, ole hyvä ja odota"},formatRecordsPerPage:function(t){return"".concat(t," riviä sivulla")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Näytetään rivit ".concat(t," - ").concat(n," / ").concat(r," (filtered from ").concat(e," total rows)"):"Näytetään rivit ".concat(t," - ").concat(n," / ").concat(r)},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Poista suodattimet"},formatSearch:function(){return"Hae"},formatNoMatches:function(){return"Hakuehtoja vastaavia tuloksia ei löytynyt"},formatPaginationSwitch:function(){return"Näytä/Piilota sivutus"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Päivitä"},formatToggle:function(){return"Valitse"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Sarakkeet"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Kaikki"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Vie tiedot"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fi-FI"])}));
