/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},f=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},h="".split,y=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?h.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return y(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=u.document,P=b(O)&&b(O.createElement),j=!f&&!a((function(){return 7!=Object.defineProperty((t="div",P?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,E={f:f?x:function(t,n){if(t=v(t),n=w(n,!0),j)try{return x(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},A=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},C=Object.defineProperty,M={f:f?C:function(t,n,r){if(A(t),n=w(n,!0),A(r),j)try{return C(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},R=f?function(t,n,r){return M.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{R(u,t,n)}catch(r){u[t]=n}return n},k="__core-js_shared__",I=u[k]||F(k,{}),L=Function.toString;"function"!=typeof I.inspectSource&&(I.inspectSource=function(t){return L.call(t)});var N,H,_,D,q=I.inspectSource,z=u.WeakMap,G="function"==typeof z&&/native code/.test(q(z)),V=o((function(t){(t.exports=function(t,n){return I[t]||(I[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),B=0,U=Math.random(),W=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++B+U).toString(36)},J=V("keys"),K={},Q=u.WeakMap;if(G){var Y=I.state||(I.state=new Q),X=Y.get,Z=Y.has,$=Y.set;N=function(t,n){return n.facade=t,$.call(Y,t,n),n},H=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=J[D="state"]||(J[D]=W(D));K[tt]=!0,N=function(t,n){return n.facade=t,R(t,tt,n),n},H=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:N,get:H,has:_,enforce:function(t){return _(t)?H(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=H(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,f=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||R(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(f?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:R(t,n,o)):c?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ft=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,ht=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},yt={includes:ht(!0),indexOf:ht(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!T(K,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~yt(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=vt.f(A(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=M.f,o=E.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Pt(t)];return r==Et||r!=xt&&("function"==typeof n?a(n):!!n)},Pt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Ot.data={},xt=Ot.NATIVE="N",Et=Ot.POLYFILL="P",At=Ot,Ct=E.f,Mt=Array.isArray||function(t){return"Array"==g(t)},Rt=function(t){return Object(m(t))},Ft=function(t,n,r){var e=w(n);e in t?M.f(t,e,p(0,r)):t[e]=r},kt="process"==g(u.process),It=at("navigator","userAgent")||"",Lt=u.process,Nt=Lt&&Lt.versions,Ht=Nt&&Nt.v8;Ht?rt=(nt=Ht.split("."))[0]+nt[1]:It&&(!(nt=It.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=It.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(kt?38===Dt:Dt>37&&Dt<41)})),zt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=V("wks"),Vt=u.Symbol,Bt=zt?Vt:Vt&&Vt.withoutSetter||W,Ut=function(t){return T(Gt,t)&&(qt||"string"==typeof Gt[t])||(qt&&T(Vt,t)?Gt[t]=Vt[t]:Gt[t]=Bt("Symbol."+t)),Gt[t]},Wt=Ut("species"),Jt=function(t,n){var r;return Mt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Mt(r.prototype)?b(r)&&null===(r=r[Wt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Kt=Ut("species"),Qt=Ut("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Dt>=51||!a((function(){var t=[];return t[Qt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!a((function(){var t=[];return(t.constructor={})[Kt]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Qt];return void 0!==n?!!n:Mt(t)};!function(t,n){var r,e,o,i,a,f=t.target,c=t.global,l=t.stat;if(r=c?u:l?u[f]||F(f,{}):(u[f]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=Ct(r,e))&&a.value:r[e],!At(c?e:f+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&R(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Rt(this),a=Jt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(f+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,f++)r in i&&Ft(a,f,i[r])}else{if(f>=Yt)throw TypeError(Xt);Ft(a,f++,i)}return a.length=f,a}}),r.default.fn.bootstrapTable.locales["sv-SE"]=r.default.fn.bootstrapTable.locales.sv={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Laddar, vänligen vänta"},formatRecordsPerPage:function(t){return"".concat(t," rader per sida")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Visa ".concat(t," till ").concat(n," av ").concat(r," rader (filtered from ").concat(e," total rows)"):"Visa ".concat(t," till ").concat(n," av ").concat(r," rader")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Sök"},formatNoMatches:function(){return"Inga matchande resultat funna."},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Uppdatera"},formatToggle:function(){return"Skifta"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"kolumn"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sv-SE"])}));
