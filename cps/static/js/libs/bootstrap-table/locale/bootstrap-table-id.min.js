/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},f=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,m=function(t){return d.call(t).slice(8,-1)},g="".split,h=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==m(t)?g.call(t,""):Object(t)}:Object,y=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return h(y(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!v(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!v(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=u.document,P=v(O)&&v(O.createElement),j=!f&&!a((function(){return 7!=Object.defineProperty((t="div",P?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,M={f:f?x:function(t,n){if(t=b(t),n=w(n,!0),j)try{return x(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},k=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,E={f:f?A:function(t,n,r){if(k(t),n=w(n,!0),k(r),j)try{return A(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},C=f?function(t,n,r){return E.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},R=function(t,n){try{C(u,t,n)}catch(r){u[t]=n}return n},F="__core-js_shared__",I=u[F]||R(F,{}),D=Function.toString;"function"!=typeof I.inspectSource&&(I.inspectSource=function(t){return D.call(t)});var N,L,_,H,q=I.inspectSource,B=u.WeakMap,z="function"==typeof B&&/native code/.test(q(B)),G=o((function(t){(t.exports=function(t,n){return I[t]||(I[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),W=0,J=Math.random(),K=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++W+J).toString(36)},Q=G("keys"),U={},V=u.WeakMap;if(z){var Y=I.state||(I.state=new V),X=Y.get,Z=Y.has,$=Y.set;N=function(t,n){return n.facade=t,$.call(Y,t,n),n},L=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=Q[H="state"]||(Q[H]=K(H));U[tt]=!0,N=function(t,n){return n.facade=t,C(t,tt,n),n},L=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:N,get:L,has:_,enforce:function(t){return _(t)?L(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!v(n)||(r=L(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,f=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||C(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(f?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:C(t,n,o)):c?t[n]=o:R(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ft=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,mt=Math.min,gt=function(t){return function(n,r,e){var o,i=b(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?dt(r+n,0):mt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ht={includes:gt(!0),indexOf:gt(!1)}.indexOf,yt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=b(t),o=0,i=[];for(r in e)!T(U,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,yt)}},vt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=bt.f(k(t)),r=vt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=E.f,o=M.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Pt(t)];return r==Mt||r!=xt&&("function"==typeof n?a(n):!!n)},Pt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Ot.data={},xt=Ot.NATIVE="N",Mt=Ot.POLYFILL="P",kt=Ot,At=M.f,Et=Array.isArray||function(t){return"Array"==m(t)},Ct=function(t){return Object(y(t))},Rt=function(t,n,r){var e=w(n);e in t?E.f(t,e,p(0,r)):t[e]=r},Ft="process"==m(u.process),It=at("navigator","userAgent")||"",Dt=u.process,Nt=Dt&&Dt.versions,Lt=Nt&&Nt.v8;Lt?rt=(nt=Lt.split("."))[0]+nt[1]:It&&(!(nt=It.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=It.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Ht=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(Ft?38===Ht:Ht>37&&Ht<41)})),Bt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,zt=G("wks"),Gt=u.Symbol,Wt=Bt?Gt:Gt&&Gt.withoutSetter||K,Jt=function(t){return T(zt,t)&&(qt||"string"==typeof zt[t])||(qt&&T(Gt,t)?zt[t]=Gt[t]:zt[t]=Wt("Symbol."+t)),zt[t]},Kt=Jt("species"),Qt=function(t,n){var r;return Et(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Et(r.prototype)?v(r)&&null===(r=r[Kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Jt("species"),Vt=Jt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Ht>=51||!a((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Ht>=51||!a((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!v(t))return!1;var n=t[Vt];return void 0!==n?!!n:Et(t)};!function(t,n){var r,e,o,i,a,f=t.target,c=t.global,l=t.stat;if(r=c?u:l?u[f]||R(f,{}):(u[f]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=At(r,e))&&a.value:r[e],!kt(c?e:f+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&C(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Ct(this),a=Qt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(f+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,f++)r in i&&Rt(a,f,i[r])}else{if(f>=Yt)throw TypeError(Xt);Rt(a,f++,i)}return a.length=f,a}}),r.default.fn.bootstrapTable.locales["id-ID"]=r.default.fn.bootstrapTable.locales.id={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Memuat, mohon tunggu"},formatRecordsPerPage:function(t){return"".concat(t," baris per halaman")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Menampilkan ".concat(t," sampai ").concat(n," dari ").concat(r," baris (filtered from ").concat(e," total rows)"):"Menampilkan ".concat(t," sampai ").concat(n," dari ").concat(r," baris")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Bersihkan filter"},formatSearch:function(){return"Pencarian"},formatNoMatches:function(){return"Tidak ditemukan data yang cocok"},formatPaginationSwitch:function(){return"Sembunyikan/Tampilkan halaman"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Muat ulang"},formatToggle:function(){return"Beralih"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"kolom"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Semua"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Ekspor data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["id-ID"])}));
