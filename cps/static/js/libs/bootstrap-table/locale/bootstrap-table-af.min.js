/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},a=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},y="".split,h=f((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?y.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return h(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=u.document,P=b(O)&&b(O.createElement),j=!a&&!f((function(){return 7!=Object.defineProperty((t="div",P?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,A={f:a?x:function(t,n){if(t=v(t),n=w(n,!0),j)try{return x(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},C=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,R={f:a?E:function(t,n,r){if(C(t),n=w(n,!0),C(r),j)try{return E(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=a?function(t,n,r){return R.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{M(u,t,n)}catch(r){u[t]=n}return n},k="__core-js_shared__",N=u[k]||F(k,{}),H=Function.toString;"function"!=typeof N.inspectSource&&(N.inspectSource=function(t){return H.call(t)});var I,L,_,D,q=N.inspectSource,G=u.WeakMap,W="function"==typeof G&&/native code/.test(q(G)),z=o((function(t){(t.exports=function(t,n){return N[t]||(N[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),B=0,K=Math.random(),Z=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++B+K).toString(36)},J=z("keys"),Q={},U=u.WeakMap;if(W){var V=N.state||(N.state=new U),Y=V.get,X=V.has,$=V.set;I=function(t,n){return n.facade=t,$.call(V,t,n),n},L=function(t){return Y.call(V,t)||{}},_=function(t){return X.call(V,t)}}else{var tt=J[D="state"]||(J[D]=Z(D));Q[tt]=!0,I=function(t,n){return n.facade=t,M(t,tt,n),n},L=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:I,get:L,has:_,enforce:function(t){return _(t)?L(t):I(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=L(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var f,a=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||M(o,"name",n),(f=r(o)).source||(f.source=e.join("string"==typeof n?n:""))),t!==u?(a?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:M(t,n,o)):c?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},ft=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},at=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:at)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,yt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),f=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>f;)if((o=i[f++])!=o)return!0}else for(;u>f;f++)if((t||f in i)&&i[f]===r)return t||f||0;return!t&&-1}},ht={includes:yt(!0),indexOf:yt(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!T(Q,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=ft("Reflect","ownKeys")||function(t){var n=vt.f(C(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=R.f,o=A.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Pt(t)];return r==At||r!=xt&&("function"==typeof n?f(n):!!n)},Pt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Ot.data={},xt=Ot.NATIVE="N",At=Ot.POLYFILL="P",Ct=Ot,Et=A.f,Rt=Array.isArray||function(t){return"Array"==g(t)},Mt=function(t){return Object(m(t))},Ft=function(t,n,r){var e=w(n);e in t?R.f(t,e,p(0,r)):t[e]=r},kt="process"==g(u.process),Nt=ft("navigator","userAgent")||"",Ht=u.process,It=Ht&&Ht.versions,Lt=It&&It.v8;Lt?rt=(nt=Lt.split("."))[0]+nt[1]:Nt&&(!(nt=Nt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Nt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!f((function(){return!Symbol.sham&&(kt?38===Dt:Dt>37&&Dt<41)})),Gt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Wt=z("wks"),zt=u.Symbol,Bt=Gt?zt:zt&&zt.withoutSetter||Z,Kt=function(t){return T(Wt,t)&&(qt||"string"==typeof Wt[t])||(qt&&T(zt,t)?Wt[t]=zt[t]:Wt[t]=Bt("Symbol."+t)),Wt[t]},Zt=Kt("species"),Jt=function(t,n){var r;return Rt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Rt(r.prototype)?b(r)&&null===(r=r[Zt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Kt("species"),Ut=Kt("isConcatSpreadable"),Vt=9007199254740991,Yt="Maximum allowed index exceeded",Xt=Dt>=51||!f((function(){var t=[];return t[Ut]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!f((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Ut];return void 0!==n?!!n:Rt(t)};!function(t,n){var r,e,o,i,f,a=t.target,c=t.global,l=t.stat;if(r=c?u:l?u[a]||F(a,{}):(u[a]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(f=Et(r,e))&&f.value:r[e],!Ct(c?e:a+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&M(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Xt||!$t},{concat:function(t){var n,r,e,o,i,u=Mt(this),f=Jt(u,0),a=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(a+(o=pt(i.length))>Vt)throw TypeError(Yt);for(r=0;r<o;r++,a++)r in i&&Ft(f,a,i[r])}else{if(a>=Vt)throw TypeError(Yt);Ft(f,a++,i)}return f.length=a,f}}),r.default.fn.bootstrapTable.locales["af-ZA"]=r.default.fn.bootstrapTable.locales.af={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Besig om te laai, wag asseblief"},formatRecordsPerPage:function(t){return"".concat(t," rekords per bladsy")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye (filtered from ").concat(e," total rows)"):"Resultate ".concat(t," tot ").concat(n," van ").concat(r," rye")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Soek"},formatNoMatches:function(){return"Geen rekords gevind nie"},formatPaginationSwitch:function(){return"Wys/verberg bladsy nummering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Herlaai"},formatToggle:function(){return"Wissel"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolomme"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["af-ZA"])}));
