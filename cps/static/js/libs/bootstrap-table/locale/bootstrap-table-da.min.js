/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},c=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!a.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:a},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},y="".split,h=f((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?y.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return h(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,O=function(t,n){return S.call(t,n)},T=u.document,j=b(T)&&b(T.createElement),P=!c&&!f((function(){return 7!=Object.defineProperty((t="div",j?T.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,A={f:c?x:function(t,n){if(t=v(t),n=w(n,!0),P)try{return x(t,n)}catch(t){}if(O(t,n))return p(!s.f.call(t,n),t[n])}},E=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},k=Object.defineProperty,C={f:c?k:function(t,n,r){if(E(t),n=w(n,!0),E(r),P)try{return k(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=c?function(t,n,r){return C.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},R=function(t,n){try{M(u,t,n)}catch(r){u[t]=n}return n},F="__core-js_shared__",I=u[F]||R(F,{}),D=Function.toString;"function"!=typeof I.inspectSource&&(I.inspectSource=function(t){return D.call(t)});var N,L,_,H,q=I.inspectSource,K=u.WeakMap,V="function"==typeof K&&/native code/.test(q(K)),z=o((function(t){(t.exports=function(t,n){return I[t]||(I[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),G=0,B=Math.random(),W=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++G+B).toString(36)},J=z("keys"),Q={},U=u.WeakMap;if(V){var Y=I.state||(I.state=new U),X=Y.get,Z=Y.has,$=Y.set;N=function(t,n){return n.facade=t,$.call(Y,t,n),n},L=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=J[H="state"]||(J[H]=W(H));Q[tt]=!0,N=function(t,n){return n.facade=t,M(t,tt,n),n},L=function(t){return O(t,tt)?t[tt]:{}},_=function(t){return O(t,tt)}}var nt,rt,et={set:N,get:L,has:_,enforce:function(t){return _(t)?L(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=L(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var f,c=!!i&&!!i.unsafe,a=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||O(o,"name")||M(o,"name",n),(f=r(o)).source||(f.source=e.join("string"==typeof n?n:""))),t!==u?(c?!l&&t[n]&&(a=!0):delete t[n],a?t[n]=o:M(t,n,o)):a?t[n]=o:R(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},ft=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ct=Math.ceil,at=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?at:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,yt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),f=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>f;)if((o=i[f++])!=o)return!0}else for(;u>f;f++)if((t||f in i)&&i[f]===r)return t||f||0;return!t&&-1}},ht={includes:yt(!0),indexOf:yt(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!O(Q,r)&&O(e,r)&&i.push(r);for(;n.length>o;)O(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=ft("Reflect","ownKeys")||function(t){var n=vt.f(E(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=C.f,o=A.f,i=0;i<r.length;i++){var u=r[i];O(t,u)||e(t,u,o(n,u))}},Ot=/#|\.prototype\./,Tt=function(t,n){var r=Pt[jt(t)];return r==At||r!=xt&&("function"==typeof n?f(n):!!n)},jt=Tt.normalize=function(t){return String(t).replace(Ot,".").toLowerCase()},Pt=Tt.data={},xt=Tt.NATIVE="N",At=Tt.POLYFILL="P",Et=Tt,kt=A.f,Ct=Array.isArray||function(t){return"Array"==g(t)},Mt=function(t){return Object(m(t))},Rt=function(t,n,r){var e=w(n);e in t?C.f(t,e,p(0,r)):t[e]=r},Ft="process"==g(u.process),It=ft("navigator","userAgent")||"",Dt=u.process,Nt=Dt&&Dt.versions,Lt=Nt&&Nt.v8;Lt?rt=(nt=Lt.split("."))[0]+nt[1]:It&&(!(nt=It.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=It.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Ht=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!f((function(){return!Symbol.sham&&(Ft?38===Ht:Ht>37&&Ht<41)})),Kt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Vt=z("wks"),zt=u.Symbol,Gt=Kt?zt:zt&&zt.withoutSetter||W,Bt=function(t){return O(Vt,t)&&(qt||"string"==typeof Vt[t])||(qt&&O(zt,t)?Vt[t]=zt[t]:Vt[t]=Gt("Symbol."+t)),Vt[t]},Wt=Bt("species"),Jt=function(t,n){var r;return Ct(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Ct(r.prototype)?b(r)&&null===(r=r[Wt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Bt("species"),Ut=Bt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Ht>=51||!f((function(){var t=[];return t[Ut]=!1,t.concat()[0]!==t})),$t=(_t="concat",Ht>=51||!f((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Ut];return void 0!==n?!!n:Ct(t)};!function(t,n){var r,e,o,i,f,c=t.target,a=t.global,l=t.stat;if(r=a?u:l?u[c]||R(c,{}):(u[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(f=kt(r,e))&&f.value:r[e],!Et(a?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&M(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Mt(this),f=Jt(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(c+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in i&&Rt(f,c,i[r])}else{if(c>=Yt)throw TypeError(Xt);Rt(f,c++,i)}return f.length=c,f}}),r.default.fn.bootstrapTable.locales["da-DK"]=r.default.fn.bootstrapTable.locales.da={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Indlæser, vent venligst"},formatRecordsPerPage:function(t){return"".concat(t," poster pr side")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Viser ".concat(t," til ").concat(n," af ").concat(r," række").concat(r>1?"r":""," (filtered from ").concat(e," total rows)"):"Viser ".concat(t," til ").concat(n," af ").concat(r," række").concat(r>1?"r":"")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Viser ".concat(t," række").concat(t>1?"r":"")},formatClearSearch:function(){return"Ryd filtre"},formatSearch:function(){return"Søg"},formatNoMatches:function(){return"Ingen poster fundet"},formatPaginationSwitch:function(){return"Skjul/vis nummerering"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Opdater"},formatToggle:function(){return"Skift"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolonner"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Eksporter"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["da-DK"])}));
