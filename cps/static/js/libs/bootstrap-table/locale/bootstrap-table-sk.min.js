/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},a=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u=function(t){try{return!!t()}catch(t){return!0}},f=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},y={}.toString,d=function(t){return y.call(t).slice(8,-1)},h="".split,m=u((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==d(t)?h.call(t,""):Object(t)}:Object,g=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return m(g(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},S=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},w={}.hasOwnProperty,j=function(t,n){return w.call(t,n)},k=a.document,P=b(k)&&b(k.createElement),O=!f&&!u((function(){return 7!=Object.defineProperty((t="div",P?k.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),T=Object.getOwnPropertyDescriptor,z={f:f?T:function(t,n){if(t=v(t),n=S(n,!0),O)try{return T(t,n)}catch(t){}if(j(t,n))return p(!s.f.call(t,n),t[n])}},x=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,A={f:f?E:function(t,n,r){if(x(t),n=S(n,!0),x(r),O)try{return E(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},C=f?function(t,n,r){return A.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},M=function(t,n){try{C(a,t,n)}catch(r){a[t]=n}return n},R="__core-js_shared__",F=a[R]||M(R,{}),N=Function.toString;"function"!=typeof F.inspectSource&&(F.inspectSource=function(t){return N.call(t)});var Z,I,L,_,D=F.inspectSource,q=a.WeakMap,V="function"==typeof q&&/native code/.test(D(q)),K=o((function(t){(t.exports=function(t,n){return F[t]||(F[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),B=0,G=Math.random(),W=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++B+G).toString(36)},H=K("keys"),J={},Q=a.WeakMap;if(V){var U=F.state||(F.state=new Q),Y=U.get,X=U.has,$=U.set;Z=function(t,n){return n.facade=t,$.call(U,t,n),n},I=function(t){return Y.call(U,t)||{}},L=function(t){return X.call(U,t)}}else{var tt=H[_="state"]||(H[_]=W(_));J[tt]=!0,Z=function(t,n){return n.facade=t,C(t,tt,n),n},I=function(t){return j(t,tt)?t[tt]:{}},L=function(t){return j(t,tt)}}var nt,rt,et={set:Z,get:I,has:L,enforce:function(t){return L(t)?I(t):Z(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=I(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var u,f=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||j(o,"name")||C(o,"name",n),(u=r(o)).source||(u.source=e.join("string"==typeof n?n:""))),t!==a?(f?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:C(t,n,o)):c?t[n]=o:M(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||D(this)}))})),it=a,at=function(t){return"function"==typeof t?t:void 0},ut=function(t,n){return arguments.length<2?at(it[t])||at(a[t]):it[t]&&it[t][n]||a[t]&&a[t][n]},ft=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},yt=Math.max,dt=Math.min,ht=function(t){return function(n,r,e){var o,i=v(n),a=pt(i.length),u=function(t,n){var r=lt(t);return r<0?yt(r+n,0):dt(r,n)}(e,a);if(t&&r!=r){for(;a>u;)if((o=i[u++])!=o)return!0}else for(;a>u;u++)if((t||u in i)&&i[u]===r)return t||u||0;return!t&&-1}},mt={includes:ht(!0),indexOf:ht(!1)}.indexOf,gt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!j(J,r)&&j(e,r)&&i.push(r);for(;n.length>o;)j(e,r=n[o++])&&(~mt(i,r)||i.push(r));return i}(t,gt)}},bt={f:Object.getOwnPropertySymbols},St=ut("Reflect","ownKeys")||function(t){var n=vt.f(x(t)),r=bt.f;return r?n.concat(r(t)):n},wt=function(t,n){for(var r=St(n),e=A.f,o=z.f,i=0;i<r.length;i++){var a=r[i];j(t,a)||e(t,a,o(n,a))}},jt=/#|\.prototype\./,kt=function(t,n){var r=Ot[Pt(t)];return r==zt||r!=Tt&&("function"==typeof n?u(n):!!n)},Pt=kt.normalize=function(t){return String(t).replace(jt,".").toLowerCase()},Ot=kt.data={},Tt=kt.NATIVE="N",zt=kt.POLYFILL="P",xt=kt,Et=z.f,At=Array.isArray||function(t){return"Array"==d(t)},Ct=function(t){return Object(g(t))},Mt=function(t,n,r){var e=S(n);e in t?A.f(t,e,p(0,r)):t[e]=r},Rt="process"==d(a.process),Ft=ut("navigator","userAgent")||"",Nt=a.process,Zt=Nt&&Nt.versions,It=Zt&&Zt.v8;It?rt=(nt=It.split("."))[0]+nt[1]:Ft&&(!(nt=Ft.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Ft.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var Lt,_t=rt&&+rt,Dt=!!Object.getOwnPropertySymbols&&!u((function(){return!Symbol.sham&&(Rt?38===_t:_t>37&&_t<41)})),qt=Dt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Vt=K("wks"),Kt=a.Symbol,Bt=qt?Kt:Kt&&Kt.withoutSetter||W,Gt=function(t){return j(Vt,t)&&(Dt||"string"==typeof Vt[t])||(Dt&&j(Kt,t)?Vt[t]=Kt[t]:Vt[t]=Bt("Symbol."+t)),Vt[t]},Wt=Gt("species"),Ht=function(t,n){var r;return At(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!At(r.prototype)?b(r)&&null===(r=r[Wt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Jt=Gt("species"),Qt=Gt("isConcatSpreadable"),Ut=9007199254740991,Yt="Maximum allowed index exceeded",Xt=_t>=51||!u((function(){var t=[];return t[Qt]=!1,t.concat()[0]!==t})),$t=(Lt="concat",_t>=51||!u((function(){var t=[];return(t.constructor={})[Jt]=function(){return{foo:1}},1!==t[Lt](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Qt];return void 0!==n?!!n:At(t)};!function(t,n){var r,e,o,i,u,f=t.target,c=t.global,l=t.stat;if(r=c?a:l?a[f]||M(f,{}):(a[f]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(u=Et(r,e))&&u.value:r[e],!xt(c?e:f+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;wt(i,o)}(t.sham||o&&o.sham)&&C(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Xt||!$t},{concat:function(t){var n,r,e,o,i,a=Ct(this),u=Ht(a,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?a:arguments[n])){if(f+(o=pt(i.length))>Ut)throw TypeError(Yt);for(r=0;r<o;r++,f++)r in i&&Mt(u,f,i[r])}else{if(f>=Ut)throw TypeError(Yt);Mt(u,f++,i)}return u.length=f,u}}),r.default.fn.bootstrapTable.locales["sk-SK"]=r.default.fn.bootstrapTable.locales.sk={formatCopyRows:function(){return"Skopírovať riadky"},formatPrint:function(){return"Vytlačiť"},formatLoadingMessage:function(){return"Prosím čakajte"},formatRecordsPerPage:function(t){return"".concat(t," záznamov na stranu")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Zobrazená ".concat(t,". - ").concat(n,". položka z celkových ").concat(r," (filtered from ").concat(e," total rows)"):"Zobrazená ".concat(t,". - ").concat(n,". položka z celkových ").concat(r)},formatSRPaginationPreText:function(){return"Predchádzajúca strana"},formatSRPaginationPageText:function(t){return"na stranu ".concat(t)},formatSRPaginationNextText:function(){return"Nasledujúca strana"},formatDetailPagination:function(t){return"Zobrazuje sa ".concat(t," riadkov")},formatClearSearch:function(){return"Odstráň filtre"},formatSearch:function(){return"Vyhľadávanie"},formatNoMatches:function(){return"Nenájdená žiadna vyhovujúca položka"},formatPaginationSwitch:function(){return"Skry/Zobraz stránkovanie"},formatPaginationSwitchDown:function(){return"Zobraziť stránkovanie"},formatPaginationSwitchUp:function(){return"Skryť stránkovanie"},formatRefresh:function(){return"Obnoviť"},formatToggle:function(){return"Prepni"},formatToggleOn:function(){return"Zobraziť kartové zobrazenie"},formatToggleOff:function(){return"skryť kartové zobrazenie"},formatColumns:function(){return"Stĺpce"},formatColumnsToggleAll:function(){return"Prepnúť všetky"},formatFullscreen:function(){return"Celá obrazovka"},formatAllRows:function(){return"Všetky"},formatAutoRefresh:function(){return"Automatické obnovenie"},formatExport:function(){return"Exportuj dáta"},formatJumpTo:function(){return"Ísť"},formatAdvancedSearch:function(){return"Pokročilé vyhľadávanie"},formatAdvancedCloseButton:function(){return"Zatvoriť"},formatFilterControlSwitch:function(){return"Zobraziť/Skryť tlačidlá"},formatFilterControlSwitchHide:function(){return"Skryť tlačidlá"},formatFilterControlSwitchShow:function(){return"Zobraziť tlačidlá"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["sk-SK"])}));
