/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},c=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!a.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:a},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},g={}.toString,h=function(t){return g.call(t).slice(8,-1)},y="".split,m=f((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==h(t)?y.call(t,""):Object(t)}:Object,d=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return m(d(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=u.document,P=b(O)&&b(O.createElement),j=!c&&!f((function(){return 7!=Object.defineProperty((t="div",P?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),A=Object.getOwnPropertyDescriptor,x={f:c?A:function(t,n){if(t=v(t),n=w(n,!0),j)try{return A(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},E=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},M=Object.defineProperty,C={f:c?M:function(t,n,r){if(E(t),n=w(n,!0),E(r),j)try{return M(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},R=c?function(t,n,r){return C.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{R(u,t,n)}catch(r){u[t]=n}return n},q="__core-js_shared__",L=u[q]||F(q,{}),I=Function.toString;"function"!=typeof L.inspectSource&&(L.inspectSource=function(t){return I.call(t)});var N,k,_,D,z=L.inspectSource,B=u.WeakMap,G="function"==typeof B&&/native code/.test(z(B)),W=o((function(t){(t.exports=function(t,n){return L[t]||(L[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),H=0,J=Math.random(),K=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++H+J).toString(36)},Q=W("keys"),U={},V=u.WeakMap;if(G){var Y=L.state||(L.state=new V),X=Y.get,Z=Y.has,$=Y.set;N=function(t,n){return n.facade=t,$.call(Y,t,n),n},k=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=Q[D="state"]||(Q[D]=K(D));U[tt]=!0,N=function(t,n){return n.facade=t,R(t,tt,n),n},k=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:N,get:k,has:_,enforce:function(t){return _(t)?k(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=k(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var f,c=!!i&&!!i.unsafe,a=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||R(o,"name",n),(f=r(o)).source||(f.source=e.join("string"==typeof n?n:""))),t!==u?(c?!l&&t[n]&&(a=!0):delete t[n],a?t[n]=o:R(t,n,o)):a?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||z(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},ft=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ct=Math.ceil,at=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?at:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},gt=Math.max,ht=Math.min,yt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),f=function(t,n){var r=lt(t);return r<0?gt(r+n,0):ht(r,n)}(e,u);if(t&&r!=r){for(;u>f;)if((o=i[f++])!=o)return!0}else for(;u>f;f++)if((t||f in i)&&i[f]===r)return t||f||0;return!t&&-1}},mt={includes:yt(!0),indexOf:yt(!1)}.indexOf,dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!T(U,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~mt(i,r)||i.push(r));return i}(t,dt)}},bt={f:Object.getOwnPropertySymbols},wt=ft("Reflect","ownKeys")||function(t){var n=vt.f(E(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=C.f,o=x.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Pt(t)];return r==xt||r!=At&&("function"==typeof n?f(n):!!n)},Pt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Ot.data={},At=Ot.NATIVE="N",xt=Ot.POLYFILL="P",Et=Ot,Mt=x.f,Ct=Array.isArray||function(t){return"Array"==h(t)},Rt=function(t){return Object(d(t))},Ft=function(t,n,r){var e=w(n);e in t?C.f(t,e,p(0,r)):t[e]=r},qt="process"==h(u.process),Lt=ft("navigator","userAgent")||"",It=u.process,Nt=It&&It.versions,kt=Nt&&Nt.v8;kt?rt=(nt=kt.split("."))[0]+nt[1]:Lt&&(!(nt=Lt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Lt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,zt=!!Object.getOwnPropertySymbols&&!f((function(){return!Symbol.sham&&(qt?38===Dt:Dt>37&&Dt<41)})),Bt=zt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=W("wks"),Wt=u.Symbol,Ht=Bt?Wt:Wt&&Wt.withoutSetter||K,Jt=function(t){return T(Gt,t)&&(zt||"string"==typeof Gt[t])||(zt&&T(Wt,t)?Gt[t]=Wt[t]:Gt[t]=Ht("Symbol."+t)),Gt[t]},Kt=Jt("species"),Qt=function(t,n){var r;return Ct(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Ct(r.prototype)?b(r)&&null===(r=r[Kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Jt("species"),Vt=Jt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Dt>=51||!f((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!f((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Vt];return void 0!==n?!!n:Ct(t)};!function(t,n){var r,e,o,i,f,c=t.target,a=t.global,l=t.stat;if(r=a?u:l?u[c]||F(c,{}):(u[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(f=Mt(r,e))&&f.value:r[e],!Et(a?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&R(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Rt(this),f=Qt(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(c+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in i&&Ft(f,c,i[r])}else{if(c>=Yt)throw TypeError(Xt);Ft(f,c++,i)}return f.length=c,f}}),r.default.fn.bootstrapTable.locales["fr-FR"]=r.default.fn.bootstrapTable.locales.fr={formatCopyRows:function(){return"Copier les lignes"},formatPrint:function(){return"Imprimer"},formatLoadingMessage:function(){return"Chargement en cours"},formatRecordsPerPage:function(t){return"".concat(t," lignes par page")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes (filtrés à partir de ").concat(e," lignes)"):"Affiche de ".concat(t," à ").concat(n," sur ").concat(r," lignes")},formatSRPaginationPreText:function(){return"page précédente"},formatSRPaginationPageText:function(t){return"vers la page ".concat(t)},formatSRPaginationNextText:function(){return"page suivante"},formatDetailPagination:function(t){return"Affiche ".concat(t," lignes")},formatClearSearch:function(){return"Effacer la recherche"},formatSearch:function(){return"Recherche"},formatNoMatches:function(){return"Aucun résultat"},formatPaginationSwitch:function(){return"Masquer/Afficher la pagination"},formatPaginationSwitchDown:function(){return"Afficher la pagination"},formatPaginationSwitchUp:function(){return"Masquer la pagination"},formatRefresh:function(){return"Actualiser"},formatToggle:function(){return"Basculer"},formatToggleOn:function(){return"Afficher la vue carte"},formatToggleOff:function(){return"Masquer la vue carte"},formatColumns:function(){return"Colonnes"},formatColumnsToggleAll:function(){return"Tout basculer"},formatFullscreen:function(){return"Plein écran"},formatAllRows:function(){return"Tout"},formatAutoRefresh:function(){return"Actualisation automatique"},formatExport:function(){return"Exporter les données"},formatJumpTo:function(){return"ALLER"},formatAdvancedSearch:function(){return"Recherche avancée"},formatAdvancedCloseButton:function(){return"Fermer"},formatFilterControlSwitch:function(){return"Masquer/Afficher les contrôles"},formatFilterControlSwitchHide:function(){return"Masquer les contrôles"},formatFilterControlSwitchShow:function(){return"Afficher les contrôles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["fr-FR"])}));
