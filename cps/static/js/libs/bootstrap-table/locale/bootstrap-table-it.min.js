/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!f.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:f},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},m="".split,y=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?m.call(t,""):Object(t)}:Object,h=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return y(h(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},P=u.document,O=b(P)&&b(P.createElement),j=!c&&!a((function(){return 7!=Object.defineProperty((t="div",O?P.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),A=Object.getOwnPropertyDescriptor,x={f:c?A:function(t,n){if(t=v(t),n=w(n,!0),j)try{return A(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},C=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},M=Object.defineProperty,E={f:c?M:function(t,n,r){if(C(t),n=w(n,!0),C(r),j)try{return M(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},z=c?function(t,n,r){return E.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},N=function(t,n){try{z(u,t,n)}catch(r){u[t]=n}return n},R="__core-js_shared__",F=u[R]||N(R,{}),I=Function.toString;"function"!=typeof F.inspectSource&&(F.inspectSource=function(t){return I.call(t)});var k,D,L,_,q=F.inspectSource,V=u.WeakMap,H="function"==typeof V&&/native code/.test(q(V)),B=o((function(t){(t.exports=function(t,n){return F[t]||(F[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),G=0,W=Math.random(),J=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++G+W).toString(36)},K=B("keys"),Q={},U=u.WeakMap;if(H){var Y=F.state||(F.state=new U),X=Y.get,Z=Y.has,$=Y.set;k=function(t,n){return n.facade=t,$.call(Y,t,n),n},D=function(t){return X.call(Y,t)||{}},L=function(t){return Z.call(Y,t)}}else{var tt=K[_="state"]||(K[_]=J(_));Q[tt]=!0,k=function(t,n){return n.facade=t,z(t,tt,n),n},D=function(t){return T(t,tt)?t[tt]:{}},L=function(t){return T(t,tt)}}var nt,rt,et={set:k,get:D,has:L,enforce:function(t){return L(t)?D(t):k(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=D(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,c=!!i&&!!i.unsafe,f=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||z(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(c?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=o:z(t,n,o)):f?t[n]=o:N(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ct=Math.ceil,ft=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ft:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,mt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},yt={includes:mt(!0),indexOf:mt(!1)}.indexOf,ht=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!T(Q,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~yt(i,r)||i.push(r));return i}(t,ht)}},bt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=vt.f(C(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=E.f,o=x.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Pt=function(t,n){var r=jt[Ot(t)];return r==xt||r!=At&&("function"==typeof n?a(n):!!n)},Ot=Pt.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Pt.data={},At=Pt.NATIVE="N",xt=Pt.POLYFILL="P",Ct=Pt,Mt=x.f,Et=Array.isArray||function(t){return"Array"==g(t)},zt=function(t){return Object(h(t))},Nt=function(t,n,r){var e=w(n);e in t?E.f(t,e,p(0,r)):t[e]=r},Rt="process"==g(u.process),Ft=at("navigator","userAgent")||"",It=u.process,kt=It&&It.versions,Dt=kt&&kt.v8;Dt?rt=(nt=Dt.split("."))[0]+nt[1]:Ft&&(!(nt=Ft.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Ft.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var Lt,_t=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(Rt?38===_t:_t>37&&_t<41)})),Vt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Ht=B("wks"),Bt=u.Symbol,Gt=Vt?Bt:Bt&&Bt.withoutSetter||J,Wt=function(t){return T(Ht,t)&&(qt||"string"==typeof Ht[t])||(qt&&T(Bt,t)?Ht[t]=Bt[t]:Ht[t]=Gt("Symbol."+t)),Ht[t]},Jt=Wt("species"),Kt=function(t,n){var r;return Et(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Et(r.prototype)?b(r)&&null===(r=r[Jt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Wt("species"),Ut=Wt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=_t>=51||!a((function(){var t=[];return t[Ut]=!1,t.concat()[0]!==t})),$t=(Lt="concat",_t>=51||!a((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[Lt](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Ut];return void 0!==n?!!n:Et(t)};!function(t,n){var r,e,o,i,a,c=t.target,f=t.global,l=t.stat;if(r=f?u:l?u[c]||N(c,{}):(u[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=Mt(r,e))&&a.value:r[e],!Ct(f?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&z(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=zt(this),a=Kt(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(c+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in i&&Nt(a,c,i[r])}else{if(c>=Yt)throw TypeError(Xt);Nt(a,c++,i)}return a.length=c,a}}),r.default.fn.bootstrapTable.locales["it-IT"]=r.default.fn.bootstrapTable.locales.it={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Caricamento in corso"},formatRecordsPerPage:function(t){return"".concat(t," elementi per pagina")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Visualizzazione da ".concat(t," a ").concat(n," di ").concat(r," elementi (filtrati da ").concat(e," elementi totali)"):"Visualizzazione da ".concat(t," a ").concat(n," di ").concat(r," elementi")},formatSRPaginationPreText:function(){return"pagina precedente"},formatSRPaginationPageText:function(t){return"alla pagina ".concat(t)},formatSRPaginationNextText:function(){return"pagina successiva"},formatDetailPagination:function(t){return"Mostrando ".concat(t," elementi")},formatClearSearch:function(){return"Pulisci filtri"},formatSearch:function(){return"Cerca"},formatNoMatches:function(){return"Nessun elemento trovato"},formatPaginationSwitch:function(){return"Nascondi/Mostra paginazione"},formatPaginationSwitchDown:function(){return"Mostra paginazione"},formatPaginationSwitchUp:function(){return"Nascondi paginazione"},formatRefresh:function(){return"Aggiorna"},formatToggle:function(){return"Attiva/Disattiva"},formatToggleOn:function(){return"Mostra visuale a scheda"},formatToggleOff:function(){return"Nascondi visuale a scheda"},formatColumns:function(){return"Colonne"},formatColumnsToggleAll:function(){return"Mostra tutte"},formatFullscreen:function(){return"Schermo intero"},formatAllRows:function(){return"Tutto"},formatAutoRefresh:function(){return"Auto Aggiornamento"},formatExport:function(){return"Esporta dati"},formatJumpTo:function(){return"VAI"},formatAdvancedSearch:function(){return"Filtri avanzati"},formatAdvancedCloseButton:function(){return"Chiudi"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["it-IT"])}));
