/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var u=function(t){return t&&t.Math==Math&&t},i=u("object"==typeof globalThis&&globalThis)||u("object"==typeof window&&window)||u("object"==typeof self&&self)||u("object"==typeof e&&e)||function(){return this}()||Function("return this")(),f=function(t){try{return!!t()}catch(t){return!0}},c=!f((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!a.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:a},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},y={}.toString,g=function(t){return y.call(t).slice(8,-1)},m="".split,d=f((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?m.call(t,""):Object(t)}:Object,h=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return d(h(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!v(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!v(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=i.document,j=v(O)&&v(O.createElement),P=!c&&!f((function(){return 7!=Object.defineProperty((t="div",j?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,E={f:c?x:function(t,n){if(t=b(t),n=w(n,!0),P)try{return x(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},A=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},C=Object.defineProperty,M={f:c?C:function(t,n,r){if(A(t),n=w(n,!0),A(r),P)try{return C(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},R=c?function(t,n,r){return M.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{R(i,t,n)}catch(r){i[t]=n}return n},N="__core-js_shared__",k=i[N]||F(N,{}),I=Function.toString;"function"!=typeof k.inspectSource&&(k.inspectSource=function(t){return I.call(t)});var L,_,D,q,z=k.inspectSource,U=i.WeakMap,B="function"==typeof U&&/native code/.test(z(U)),G=o((function(t){(t.exports=function(t,n){return k[t]||(k[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),W=0,H=Math.random(),J=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++W+H).toString(36)},K=G("keys"),Q={},V=i.WeakMap;if(B){var Y=k.state||(k.state=new V),X=Y.get,Z=Y.has,$=Y.set;L=function(t,n){return n.facade=t,$.call(Y,t,n),n},_=function(t){return X.call(Y,t)||{}},D=function(t){return Z.call(Y,t)}}else{var tt=K[q="state"]||(K[q]=J(q));Q[tt]=!0,L=function(t,n){return n.facade=t,R(t,tt,n),n},_=function(t){return T(t,tt)?t[tt]:{}},D=function(t){return T(t,tt)}}var nt,rt,et={set:L,get:_,has:D,enforce:function(t){return D(t)?_(t):L(t,{})},getterFor:function(t){return function(n){var r;if(!v(n)||(r=_(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,u){var f,c=!!u&&!!u.unsafe,a=!!u&&!!u.enumerable,l=!!u&&!!u.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||R(o,"name",n),(f=r(o)).source||(f.source=e.join("string"==typeof n?n:""))),t!==i?(c?!l&&t[n]&&(a=!0):delete t[n],a?t[n]=o:R(t,n,o)):a?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||z(this)}))})),ut=i,it=function(t){return"function"==typeof t?t:void 0},ft=function(t,n){return arguments.length<2?it(ut[t])||it(i[t]):ut[t]&&ut[t][n]||i[t]&&i[t][n]},ct=Math.ceil,at=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?at:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},yt=Math.max,gt=Math.min,mt=function(t){return function(n,r,e){var o,u=b(n),i=pt(u.length),f=function(t,n){var r=lt(t);return r<0?yt(r+n,0):gt(r,n)}(e,i);if(t&&r!=r){for(;i>f;)if((o=u[f++])!=o)return!0}else for(;i>f;f++)if((t||f in u)&&u[f]===r)return t||f||0;return!t&&-1}},dt={includes:mt(!0),indexOf:mt(!1)}.indexOf,ht=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=b(t),o=0,u=[];for(r in e)!T(Q,r)&&T(e,r)&&u.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~dt(u,r)||u.push(r));return u}(t,ht)}},vt={f:Object.getOwnPropertySymbols},wt=ft("Reflect","ownKeys")||function(t){var n=bt.f(A(t)),r=vt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=M.f,o=E.f,u=0;u<r.length;u++){var i=r[u];T(t,i)||e(t,i,o(n,i))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=Pt[jt(t)];return r==Et||r!=xt&&("function"==typeof n?f(n):!!n)},jt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},Pt=Ot.data={},xt=Ot.NATIVE="N",Et=Ot.POLYFILL="P",At=Ot,Ct=E.f,Mt=Array.isArray||function(t){return"Array"==g(t)},Rt=function(t){return Object(h(t))},Ft=function(t,n,r){var e=w(n);e in t?M.f(t,e,p(0,r)):t[e]=r},Nt="process"==g(i.process),kt=ft("navigator","userAgent")||"",It=i.process,Lt=It&&It.versions,_t=Lt&&Lt.v8;_t?rt=(nt=_t.split("."))[0]+nt[1]:kt&&(!(nt=kt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=kt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var Dt,qt=rt&&+rt,zt=!!Object.getOwnPropertySymbols&&!f((function(){return!Symbol.sham&&(Nt?38===qt:qt>37&&qt<41)})),Ut=zt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Bt=G("wks"),Gt=i.Symbol,Wt=Ut?Gt:Gt&&Gt.withoutSetter||J,Ht=function(t){return T(Bt,t)&&(zt||"string"==typeof Bt[t])||(zt&&T(Gt,t)?Bt[t]=Gt[t]:Bt[t]=Wt("Symbol."+t)),Bt[t]},Jt=Ht("species"),Kt=function(t,n){var r;return Mt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Mt(r.prototype)?v(r)&&null===(r=r[Jt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Ht("species"),Vt=Ht("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=qt>=51||!f((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(Dt="concat",qt>=51||!f((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[Dt](Boolean).foo}))),tn=function(t){if(!v(t))return!1;var n=t[Vt];return void 0!==n?!!n:Mt(t)};!function(t,n){var r,e,o,u,f,c=t.target,a=t.global,l=t.stat;if(r=a?i:l?i[c]||F(c,{}):(i[c]||{}).prototype)for(e in n){if(u=n[e],o=t.noTargetGet?(f=Ct(r,e))&&f.value:r[e],!At(a?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof u==typeof o)continue;St(u,o)}(t.sham||o&&o.sham)&&R(u,"sham",!0),ot(r,e,u,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,u,i=Rt(this),f=Kt(i,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(u=-1===n?i:arguments[n])){if(c+(o=pt(u.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in u&&Ft(f,c,u[r])}else{if(c>=Yt)throw TypeError(Xt);Ft(f,c++,u)}return f.length=c,f}}),r.default.fn.bootstrapTable.locales["ru-RU"]=r.default.fn.bootstrapTable.locales.ru={formatCopyRows:function(){return"Скопировать строки"},formatPrint:function(){return"Печать"},formatLoadingMessage:function(){return"Пожалуйста, подождите, идёт загрузка"},formatRecordsPerPage:function(t){return"".concat(t," записей на страницу")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Записи с ".concat(t," по ").concat(n," из ").concat(r," (отфильтровано, всего на сервере ").concat(e," записей)"):"Записи с ".concat(t," по ").concat(n," из ").concat(r)},formatSRPaginationPreText:function(){return"предыдущая страница"},formatSRPaginationPageText:function(t){return"перейти к странице ".concat(t)},formatSRPaginationNextText:function(){return"следующая страница"},formatDetailPagination:function(t){return"Загружено ".concat(t," строк")},formatClearSearch:function(){return"Очистить фильтры"},formatSearch:function(){return"Поиск"},formatNoMatches:function(){return"Ничего не найдено"},formatPaginationSwitch:function(){return"Скрыть/Показать постраничную навигацию"},formatPaginationSwitchDown:function(){return"Показать постраничную навигацию"},formatPaginationSwitchUp:function(){return"Скрыть постраничную навигацию"},formatRefresh:function(){return"Обновить"},formatToggle:function(){return"Переключить"},formatToggleOn:function(){return"Показать записи в виде карточек"},formatToggleOff:function(){return"Табличный режим просмотра"},formatColumns:function(){return"Колонки"},formatColumnsToggleAll:function(){return"Выбрать все"},formatFullscreen:function(){return"Полноэкранный режим"},formatAllRows:function(){return"Все"},formatAutoRefresh:function(){return"Автоматическое обновление"},formatExport:function(){return"Экспортировать данные"},formatJumpTo:function(){return"Стр."},formatAdvancedSearch:function(){return"Расширенный поиск"},formatAdvancedCloseButton:function(){return"Закрыть"},formatFilterControlSwitch:function(){return"Скрыть/Показать панель инструментов"},formatFilterControlSwitchHide:function(){return"Скрыть панель инструментов"},formatFilterControlSwitchShow:function(){return"Показать панель инструментов"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ru-RU"])}));
