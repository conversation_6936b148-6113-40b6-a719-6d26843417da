/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var n=e(t),r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,e){return t(e={exports:{}},e.exports),e.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof r&&r)||function(){return this}()||Function("return this")(),c=function(t){try{return!!t()}catch(t){return!0}},f=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),a={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!a.call({1:2},1)?function(t){var e=l(this,t);return!!e&&e.enumerable}:a},p=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},m={}.toString,g=function(t){return m.call(t).slice(8,-1)},d="".split,h=c((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?d.call(t,""):Object(t)}:Object,y=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return h(y(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},S=function(t,e){if(!v(t))return t;var n,r;if(e&&"function"==typeof(n=t.toString)&&!v(r=n.call(t)))return r;if("function"==typeof(n=t.valueOf)&&!v(r=n.call(t)))return r;if(!e&&"function"==typeof(n=t.toString)&&!v(r=n.call(t)))return r;throw TypeError("Can't convert object to primitive value")},w={}.hasOwnProperty,T=function(t,e){return w.call(t,e)},O=u.document,j=v(O)&&v(O.createElement),P=!f&&!c((function(){return 7!=Object.defineProperty((t="div",j?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),A=Object.getOwnPropertyDescriptor,E={f:f?A:function(t,e){if(t=b(t),e=S(e,!0),P)try{return A(t,e)}catch(t){}if(T(t,e))return p(!s.f.call(t,e),t[e])}},x=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},Z=Object.defineProperty,C={f:f?Z:function(t,e,n){if(x(t),e=S(e,!0),x(n),P)try{return Z(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},M=f?function(t,e,n){return C.f(t,e,p(1,n))}:function(t,e,n){return t[e]=n,t},D=function(t,e){try{M(u,t,e)}catch(n){u[t]=e}return e},N="__core-js_shared__",F=u[N]||D(N,{}),k=Function.toString;"function"!=typeof F.inspectSource&&(F.inspectSource=function(t){return k.call(t)});var R,L,V,I,_=F.inspectSource,B=u.WeakMap,q="function"==typeof B&&/native code/.test(_(B)),z=o((function(t){(t.exports=function(t,e){return F[t]||(F[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),G=0,K=Math.random(),U=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++G+K).toString(36)},W=z("keys"),H={},J=u.WeakMap;if(q){var Q=F.state||(F.state=new J),Y=Q.get,X=Q.has,$=Q.set;R=function(t,e){return e.facade=t,$.call(Q,t,e),e},L=function(t){return Y.call(Q,t)||{}},V=function(t){return X.call(Q,t)}}else{var tt=W[I="state"]||(W[I]=U(I));H[tt]=!0,R=function(t,e){return e.facade=t,M(t,tt,e),e},L=function(t){return T(t,tt)?t[tt]:{}},V=function(t){return T(t,tt)}}var et,nt,rt={set:R,get:L,has:V,enforce:function(t){return V(t)?L(t):R(t,{})},getterFor:function(t){return function(e){var n;if(!v(e)||(n=L(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}}},ot=o((function(t){var e=rt.get,n=rt.enforce,r=String(String).split("String");(t.exports=function(t,e,o,i){var c,f=!!i&&!!i.unsafe,a=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof e||T(o,"name")||M(o,"name",e),(c=n(o)).source||(c.source=r.join("string"==typeof e?e:""))),t!==u?(f?!l&&t[e]&&(a=!0):delete t[e],a?t[e]=o:M(t,e,o)):a?t[e]=o:D(e,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||_(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},ct=function(t,e){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][e]||u[t]&&u[t][e]},ft=Math.ceil,at=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?at:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},mt=Math.max,gt=Math.min,dt=function(t){return function(e,n,r){var o,i=b(e),u=pt(i.length),c=function(t,e){var n=lt(t);return n<0?mt(n+e,0):gt(n,e)}(r,u);if(t&&n!=n){for(;u>c;)if((o=i[c++])!=o)return!0}else for(;u>c;c++)if((t||c in i)&&i[c]===n)return t||c||0;return!t&&-1}},ht={includes:dt(!0),indexOf:dt(!1)}.indexOf,yt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,e){var n,r=b(t),o=0,i=[];for(n in r)!T(H,n)&&T(r,n)&&i.push(n);for(;e.length>o;)T(r,n=e[o++])&&(~ht(i,n)||i.push(n));return i}(t,yt)}},vt={f:Object.getOwnPropertySymbols},St=ct("Reflect","ownKeys")||function(t){var e=bt.f(x(t)),n=vt.f;return n?e.concat(n(t)):e},wt=function(t,e){for(var n=St(e),r=C.f,o=E.f,i=0;i<n.length;i++){var u=n[i];T(t,u)||r(t,u,o(e,u))}},Tt=/#|\.prototype\./,Ot=function(t,e){var n=Pt[jt(t)];return n==Et||n!=At&&("function"==typeof e?c(e):!!e)},jt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},Pt=Ot.data={},At=Ot.NATIVE="N",Et=Ot.POLYFILL="P",xt=Ot,Zt=E.f,Ct=Array.isArray||function(t){return"Array"==g(t)},Mt=function(t){return Object(y(t))},Dt=function(t,e,n){var r=S(e);r in t?C.f(t,r,p(0,n)):t[r]=n},Nt="process"==g(u.process),Ft=ct("navigator","userAgent")||"",kt=u.process,Rt=kt&&kt.versions,Lt=Rt&&Rt.v8;Lt?nt=(et=Lt.split("."))[0]+et[1]:Ft&&(!(et=Ft.match(/Edge\/(\d+)/))||et[1]>=74)&&(et=Ft.match(/Chrome\/(\d+)/))&&(nt=et[1]);var Vt,It=nt&&+nt,_t=!!Object.getOwnPropertySymbols&&!c((function(){return!Symbol.sham&&(Nt?38===It:It>37&&It<41)})),Bt=_t&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,qt=z("wks"),zt=u.Symbol,Gt=Bt?zt:zt&&zt.withoutSetter||U,Kt=function(t){return T(qt,t)&&(_t||"string"==typeof qt[t])||(_t&&T(zt,t)?qt[t]=zt[t]:qt[t]=Gt("Symbol."+t)),qt[t]},Ut=Kt("species"),Wt=function(t,e){var n;return Ct(t)&&("function"!=typeof(n=t.constructor)||n!==Array&&!Ct(n.prototype)?v(n)&&null===(n=n[Ut])&&(n=void 0):n=void 0),new(void 0===n?Array:n)(0===e?0:e)},Ht=Kt("species"),Jt=Kt("isConcatSpreadable"),Qt=9007199254740991,Yt="Maximum allowed index exceeded",Xt=It>=51||!c((function(){var t=[];return t[Jt]=!1,t.concat()[0]!==t})),$t=(Vt="concat",It>=51||!c((function(){var t=[];return(t.constructor={})[Ht]=function(){return{foo:1}},1!==t[Vt](Boolean).foo}))),te=function(t){if(!v(t))return!1;var e=t[Jt];return void 0!==e?!!e:Ct(t)};!function(t,e){var n,r,o,i,c,f=t.target,a=t.global,l=t.stat;if(n=a?u:l?u[f]||D(f,{}):(u[f]||{}).prototype)for(r in e){if(i=e[r],o=t.noTargetGet?(c=Zt(n,r))&&c.value:n[r],!xt(a?r:f+(l?".":"#")+r,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;wt(i,o)}(t.sham||o&&o.sham)&&M(i,"sham",!0),ot(n,r,i,t)}}({target:"Array",proto:!0,forced:!Xt||!$t},{concat:function(t){var e,n,r,o,i,u=Mt(this),c=Wt(u,0),f=0;for(e=-1,r=arguments.length;e<r;e++)if(te(i=-1===e?u:arguments[e])){if(f+(o=pt(i.length))>Qt)throw TypeError(Yt);for(n=0;n<o;n++,f++)n in i&&Dt(c,f,i[n])}else{if(f>=Qt)throw TypeError(Yt);Dt(c,f++,i)}return c.length=f,c}}),n.default.fn.bootstrapTable.locales["de-DE"]=n.default.fn.bootstrapTable.locales.de={formatCopyRows:function(){return"Zeilen kopieren"},formatPrint:function(){return"Drucken"},formatLoadingMessage:function(){return"Lade, bitte warten"},formatRecordsPerPage:function(t){return"".concat(t," Zeilen pro Seite.")},formatShowingRows:function(t,e,n,r){return void 0!==r&&r>0&&r>n?"Zeige Zeile ".concat(t," bis ").concat(e," von ").concat(n," Zeile").concat(n>1?"n":""," (Gefiltert von ").concat(r," Zeile").concat(r>1?"n":"",")"):"Zeige Zeile ".concat(t," bis ").concat(e," von ").concat(n," Zeile").concat(n>1?"n":"",".")},formatSRPaginationPreText:function(){return"Vorherige Seite"},formatSRPaginationPageText:function(t){return"Zu Seite ".concat(t)},formatSRPaginationNextText:function(){return"Nächste Seite"},formatDetailPagination:function(t){return"Zeige ".concat(t," Zeile").concat(t>1?"n":"",".")},formatClearSearch:function(){return"Lösche Filter"},formatSearch:function(){return"Suchen"},formatNoMatches:function(){return"Keine passenden Ergebnisse gefunden"},formatPaginationSwitch:function(){return"Verstecke/Zeige Nummerierung"},formatPaginationSwitchDown:function(){return"Zeige Nummerierung"},formatPaginationSwitchUp:function(){return"Verstecke Nummerierung"},formatRefresh:function(){return"Neu laden"},formatToggle:function(){return"Umschalten"},formatToggleOn:function(){return"Normale Ansicht"},formatToggleOff:function(){return"Kartenansicht"},formatColumns:function(){return"Spalten"},formatColumnsToggleAll:function(){return"Alle umschalten"},formatFullscreen:function(){return"Vollbild"},formatAllRows:function(){return"Alle"},formatAutoRefresh:function(){return"Automatisches Neuladen"},formatExport:function(){return"Datenexport"},formatJumpTo:function(){return"Springen"},formatAdvancedSearch:function(){return"Erweiterte Suche"},formatAdvancedCloseButton:function(){return"Schließen"},formatFilterControlSwitch:function(){return"Verstecke/Zeige Filter"},formatFilterControlSwitchHide:function(){return"Verstecke Filter"},formatFilterControlSwitchShow:function(){return"Zeige Filter"},formatAddLevel:function(){return"Ebene hinzufügen"},formatCancel:function(){return"Abbrechen"},formatColumn:function(){return"Spalte"},formatDeleteLevel:function(){return"Ebene entfernen"},formatDuplicateAlertTitle:function(){return"Doppelte Einträge gefunden!"},formatDuplicateAlertDescription:function(){return"Bitte doppelte Spalten entfenen oder ändern"},formatMultipleSort:function(){return"Mehrfachsortierung"},formatOrder:function(){return"Reihenfolge"},formatSort:function(){return"Sortieren"},formatSortBy:function(){return"Sortieren nach"},formatThenBy:function(){return"anschließend"},formatSortOrders:function(){return{asc:"Aufsteigend",desc:"Absteigend"}}},n.default.extend(n.default.fn.bootstrapTable.defaults,n.default.fn.bootstrapTable.locales["de-DE"])}));
