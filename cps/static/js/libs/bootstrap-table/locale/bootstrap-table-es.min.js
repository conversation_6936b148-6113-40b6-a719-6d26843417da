/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var a=function(t){return t&&t.Math==Math&&t},i=a("object"==typeof globalThis&&globalThis)||a("object"==typeof window&&window)||a("object"==typeof self&&self)||a("object"==typeof e&&e)||function(){return this}()||Function("return this")(),u=function(t){try{return!!t()}catch(t){return!0}},c=!u((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!f.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:f},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},y="".split,m=u((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?y.call(t,""):Object(t)}:Object,h=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},b=function(t){return m(h(t))},v=function(t){return"object"==typeof t?null!==t:"function"==typeof t},S=function(t,n){if(!v(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!v(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!v(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},w={}.hasOwnProperty,O=function(t,n){return w.call(t,n)},T=i.document,P=v(T)&&v(T.createElement),j=!c&&!u((function(){return 7!=Object.defineProperty((t="div",P?T.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),M=Object.getOwnPropertyDescriptor,E={f:c?M:function(t,n){if(t=b(t),n=S(n,!0),j)try{return M(t,n)}catch(t){}if(O(t,n))return p(!s.f.call(t,n),t[n])}},x=function(t){if(!v(t))throw TypeError(String(t)+" is not an object");return t},C=Object.defineProperty,A={f:c?C:function(t,n,r){if(x(t),n=S(n,!0),x(r),j)try{return C(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},R=c?function(t,n,r){return A.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{R(i,t,n)}catch(r){i[t]=n}return n},I="__core-js_shared__",N=i[I]||F(I,{}),L=Function.toString;"function"!=typeof N.inspectSource&&(N.inspectSource=function(t){return L.call(t)});var k,q,_,D,z=N.inspectSource,B=i.WeakMap,G="function"==typeof B&&/native code/.test(z(B)),W=o((function(t){(t.exports=function(t,n){return N[t]||(N[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),H=0,J=Math.random(),K=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++H+J).toString(36)},Q=W("keys"),U={},V=i.WeakMap;if(G){var Y=N.state||(N.state=new V),X=Y.get,Z=Y.has,$=Y.set;k=function(t,n){return n.facade=t,$.call(Y,t,n),n},q=function(t){return X.call(Y,t)||{}},_=function(t){return Z.call(Y,t)}}else{var tt=Q[D="state"]||(Q[D]=K(D));U[tt]=!0,k=function(t,n){return n.facade=t,R(t,tt,n),n},q=function(t){return O(t,tt)?t[tt]:{}},_=function(t){return O(t,tt)}}var nt,rt,et={set:k,get:q,has:_,enforce:function(t){return _(t)?q(t):k(t,{})},getterFor:function(t){return function(n){var r;if(!v(n)||(r=q(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,a){var u,c=!!a&&!!a.unsafe,f=!!a&&!!a.enumerable,l=!!a&&!!a.noTargetGet;"function"==typeof o&&("string"!=typeof n||O(o,"name")||R(o,"name",n),(u=r(o)).source||(u.source=e.join("string"==typeof n?n:""))),t!==i?(c?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=o:R(t,n,o)):f?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||z(this)}))})),at=i,it=function(t){return"function"==typeof t?t:void 0},ut=function(t,n){return arguments.length<2?it(at[t])||it(i[t]):at[t]&&at[t][n]||i[t]&&i[t][n]},ct=Math.ceil,ft=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ft:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,yt=function(t){return function(n,r,e){var o,a=b(n),i=pt(a.length),u=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,i);if(t&&r!=r){for(;i>u;)if((o=a[u++])!=o)return!0}else for(;i>u;u++)if((t||u in a)&&a[u]===r)return t||u||0;return!t&&-1}},mt={includes:yt(!0),indexOf:yt(!1)}.indexOf,ht=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),bt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=b(t),o=0,a=[];for(r in e)!O(U,r)&&O(e,r)&&a.push(r);for(;n.length>o;)O(e,r=n[o++])&&(~mt(a,r)||a.push(r));return a}(t,ht)}},vt={f:Object.getOwnPropertySymbols},St=ut("Reflect","ownKeys")||function(t){var n=bt.f(x(t)),r=vt.f;return r?n.concat(r(t)):n},wt=function(t,n){for(var r=St(n),e=A.f,o=E.f,a=0;a<r.length;a++){var i=r[a];O(t,i)||e(t,i,o(n,i))}},Ot=/#|\.prototype\./,Tt=function(t,n){var r=jt[Pt(t)];return r==Et||r!=Mt&&("function"==typeof n?u(n):!!n)},Pt=Tt.normalize=function(t){return String(t).replace(Ot,".").toLowerCase()},jt=Tt.data={},Mt=Tt.NATIVE="N",Et=Tt.POLYFILL="P",xt=Tt,Ct=E.f,At=Array.isArray||function(t){return"Array"==g(t)},Rt=function(t){return Object(h(t))},Ft=function(t,n,r){var e=S(n);e in t?A.f(t,e,p(0,r)):t[e]=r},It="process"==g(i.process),Nt=ut("navigator","userAgent")||"",Lt=i.process,kt=Lt&&Lt.versions,qt=kt&&kt.v8;qt?rt=(nt=qt.split("."))[0]+nt[1]:Nt&&(!(nt=Nt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Nt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,zt=!!Object.getOwnPropertySymbols&&!u((function(){return!Symbol.sham&&(It?38===Dt:Dt>37&&Dt<41)})),Bt=zt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=W("wks"),Wt=i.Symbol,Ht=Bt?Wt:Wt&&Wt.withoutSetter||K,Jt=function(t){return O(Gt,t)&&(zt||"string"==typeof Gt[t])||(zt&&O(Wt,t)?Gt[t]=Wt[t]:Gt[t]=Ht("Symbol."+t)),Gt[t]},Kt=Jt("species"),Qt=function(t,n){var r;return At(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!At(r.prototype)?v(r)&&null===(r=r[Kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Jt("species"),Vt=Jt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Dt>=51||!u((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!u((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!v(t))return!1;var n=t[Vt];return void 0!==n?!!n:At(t)};!function(t,n){var r,e,o,a,u,c=t.target,f=t.global,l=t.stat;if(r=f?i:l?i[c]||F(c,{}):(i[c]||{}).prototype)for(e in n){if(a=n[e],o=t.noTargetGet?(u=Ct(r,e))&&u.value:r[e],!xt(f?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof a==typeof o)continue;wt(a,o)}(t.sham||o&&o.sham)&&R(a,"sham",!0),ot(r,e,a,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,a,i=Rt(this),u=Qt(i,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(a=-1===n?i:arguments[n])){if(c+(o=pt(a.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in a&&Ft(u,c,a[r])}else{if(c>=Yt)throw TypeError(Xt);Ft(u,c++,a)}return u.length=c,u}}),r.default.fn.bootstrapTable.locales["es-ES"]=r.default.fn.bootstrapTable.locales.es={formatCopyRows:function(){return"Copiar filas"},formatPrint:function(){return"Imprimir"},formatLoadingMessage:function(){return"Por favor espere"},formatRecordsPerPage:function(t){return"".concat(t," resultados por página")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Mostrando desde ".concat(t," hasta ").concat(n," - En total ").concat(r," resultados (filtrado de ").concat(e," filas totales)"):"Mostrando desde ".concat(t," hasta ").concat(n," - En total ").concat(r," resultados")},formatSRPaginationPreText:function(){return"página anterior"},formatSRPaginationPageText:function(t){return"a la página ".concat(t)},formatSRPaginationNextText:function(){return"siguiente página"},formatDetailPagination:function(t){return"Mostrando ".concat(t," filas")},formatClearSearch:function(){return"Limpiar búsqueda"},formatSearch:function(){return"Buscar"},formatNoMatches:function(){return"No se encontraron resultados"},formatPaginationSwitch:function(){return"Ocultar/Mostrar paginación"},formatPaginationSwitchDown:function(){return"Mostrar paginación"},formatPaginationSwitchUp:function(){return"Ocultar paginación"},formatRefresh:function(){return"Recargar"},formatToggle:function(){return"Ocultar/Mostrar"},formatToggleOn:function(){return"Mostrar vista de carta"},formatToggleOff:function(){return"Ocultar vista de carta"},formatColumns:function(){return"Columnas"},formatColumnsToggleAll:function(){return"Cambiar todo"},formatFullscreen:function(){return"Pantalla completa"},formatAllRows:function(){return"Todos"},formatAutoRefresh:function(){return"Auto Recargar"},formatExport:function(){return"Exportar los datos"},formatJumpTo:function(){return"IR"},formatAdvancedSearch:function(){return"Búsqueda avanzada"},formatAdvancedCloseButton:function(){return"Cerrar"},formatFilterControlSwitch:function(){return"Ocultar/Mostrar controles"},formatFilterControlSwitchHide:function(){return"Ocultar controles"},formatFilterControlSwitchShow:function(){return"Mostrar controles"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["es-ES"])}));
