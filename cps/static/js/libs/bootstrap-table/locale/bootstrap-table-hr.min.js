/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},f=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},h="".split,y=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?h.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return y(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,j=function(t,n){return S.call(t,n)},P=u.document,O=b(P)&&b(P.createElement),T=!f&&!a((function(){return 7!=Object.defineProperty((t="div",O?P.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,A={f:f?x:function(t,n){if(t=v(t),n=w(n,!0),T)try{return x(t,n)}catch(t){}if(j(t,n))return p(!s.f.call(t,n),t[n])}},C=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,k={f:f?E:function(t,n,r){if(C(t),n=w(n,!0),C(r),T)try{return E(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=f?function(t,n,r){return k.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},R=function(t,n){try{M(u,t,n)}catch(r){u[t]=n}return n},z="__core-js_shared__",F=u[z]||R(z,{}),N=Function.toString;"function"!=typeof F.inspectSource&&(F.inspectSource=function(t){return N.call(t)});var H,I,L,_,D=F.inspectSource,q=u.WeakMap,G="function"==typeof q&&/native code/.test(D(q)),B=o((function(t){(t.exports=function(t,n){return F[t]||(F[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),K=0,W=Math.random(),J=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++K+W).toString(36)},Q=B("keys"),U={},V=u.WeakMap;if(G){var Y=F.state||(F.state=new V),X=Y.get,Z=Y.has,$=Y.set;H=function(t,n){return n.facade=t,$.call(Y,t,n),n},I=function(t){return X.call(Y,t)||{}},L=function(t){return Z.call(Y,t)}}else{var tt=Q[_="state"]||(Q[_]=J(_));U[tt]=!0,H=function(t,n){return n.facade=t,M(t,tt,n),n},I=function(t){return j(t,tt)?t[tt]:{}},L=function(t){return j(t,tt)}}var nt,rt,et={set:H,get:I,has:L,enforce:function(t){return L(t)?I(t):H(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=I(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,f=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||j(o,"name")||M(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(f?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:M(t,n,o)):c?t[n]=o:R(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||D(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ft=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,ht=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},yt={includes:ht(!0),indexOf:ht(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!j(U,r)&&j(e,r)&&i.push(r);for(;n.length>o;)j(e,r=n[o++])&&(~yt(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=vt.f(C(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=k.f,o=A.f,i=0;i<r.length;i++){var u=r[i];j(t,u)||e(t,u,o(n,u))}},jt=/#|\.prototype\./,Pt=function(t,n){var r=Tt[Ot(t)];return r==At||r!=xt&&("function"==typeof n?a(n):!!n)},Ot=Pt.normalize=function(t){return String(t).replace(jt,".").toLowerCase()},Tt=Pt.data={},xt=Pt.NATIVE="N",At=Pt.POLYFILL="P",Ct=Pt,Et=A.f,kt=Array.isArray||function(t){return"Array"==g(t)},Mt=function(t){return Object(m(t))},Rt=function(t,n,r){var e=w(n);e in t?k.f(t,e,p(0,r)):t[e]=r},zt="process"==g(u.process),Ft=at("navigator","userAgent")||"",Nt=u.process,Ht=Nt&&Nt.versions,It=Ht&&Ht.v8;It?rt=(nt=It.split("."))[0]+nt[1]:Ft&&(!(nt=Ft.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Ft.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var Lt,_t=rt&&+rt,Dt=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(zt?38===_t:_t>37&&_t<41)})),qt=Dt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=B("wks"),Bt=u.Symbol,Kt=qt?Bt:Bt&&Bt.withoutSetter||J,Wt=function(t){return j(Gt,t)&&(Dt||"string"==typeof Gt[t])||(Dt&&j(Bt,t)?Gt[t]=Bt[t]:Gt[t]=Kt("Symbol."+t)),Gt[t]},Jt=Wt("species"),Qt=function(t,n){var r;return kt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!kt(r.prototype)?b(r)&&null===(r=r[Jt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Wt("species"),Vt=Wt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=_t>=51||!a((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(Lt="concat",_t>=51||!a((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[Lt](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Vt];return void 0!==n?!!n:kt(t)};!function(t,n){var r,e,o,i,a,f=t.target,c=t.global,l=t.stat;if(r=c?u:l?u[f]||R(f,{}):(u[f]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=Et(r,e))&&a.value:r[e],!Ct(c?e:f+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&M(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Mt(this),a=Qt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(f+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,f++)r in i&&Rt(a,f,i[r])}else{if(f>=Yt)throw TypeError(Xt);Rt(a,f++,i)}return a.length=f,a}}),r.default.fn.bootstrapTable.locales["hr-HR"]=r.default.fn.bootstrapTable.locales.hr={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Molimo pričekajte"},formatRecordsPerPage:function(t){return"".concat(t," broj zapisa po stranici")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Prikazujem ".concat(t,". - ").concat(n,". od ukupnog broja zapisa ").concat(r," (filtered from ").concat(e," total rows)"):"Prikazujem ".concat(t,". - ").concat(n,". od ukupnog broja zapisa ").concat(r)},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Pretraži"},formatNoMatches:function(){return"Nije pronađen niti jedan zapis"},formatPaginationSwitch:function(){return"Prikaži/sakrij stranice"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Osvježi"},formatToggle:function(){return"Promijeni prikaz"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Kolone"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Sve"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["hr-HR"])}));
