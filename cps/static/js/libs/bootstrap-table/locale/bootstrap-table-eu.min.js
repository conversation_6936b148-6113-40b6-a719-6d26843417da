/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},f=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),c={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!c.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:c},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},g={}.toString,d=function(t){return g.call(t).slice(8,-1)},y="".split,h=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==d(t)?y.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return h(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,T=function(t,n){return S.call(t,n)},O=u.document,P=b(O)&&b(O.createElement),j=!f&&!a((function(){return 7!=Object.defineProperty((t="div",P?O.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),k=Object.getOwnPropertyDescriptor,E={f:f?k:function(t,n){if(t=v(t),n=w(n,!0),j)try{return k(t,n)}catch(t){}if(T(t,n))return p(!s.f.call(t,n),t[n])}},x=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},A=Object.defineProperty,C={f:f?A:function(t,n,r){if(x(t),n=w(n,!0),x(r),j)try{return A(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=f?function(t,n,r){return C.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},R=function(t,n){try{M(u,t,n)}catch(r){u[t]=n}return n},z="__core-js_shared__",F=u[z]||R(z,{}),I=Function.toString;"function"!=typeof F.inspectSource&&(F.inspectSource=function(t){return I.call(t)});var N,L,_,D,H=F.inspectSource,q=u.WeakMap,G="function"==typeof q&&/native code/.test(H(q)),B=o((function(t){(t.exports=function(t,n){return F[t]||(F[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),U=0,W=Math.random(),J=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++U+W).toString(36)},K=B("keys"),Q={},V=u.WeakMap;if(G){var Y=F.state||(F.state=new V),Z=Y.get,X=Y.has,$=Y.set;N=function(t,n){return n.facade=t,$.call(Y,t,n),n},L=function(t){return Z.call(Y,t)||{}},_=function(t){return X.call(Y,t)}}else{var tt=K[D="state"]||(K[D]=J(D));Q[tt]=!0,N=function(t,n){return n.facade=t,M(t,tt,n),n},L=function(t){return T(t,tt)?t[tt]:{}},_=function(t){return T(t,tt)}}var nt,rt,et={set:N,get:L,has:_,enforce:function(t){return _(t)?L(t):N(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=L(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,f=!!i&&!!i.unsafe,c=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||T(o,"name")||M(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(f?!l&&t[n]&&(c=!0):delete t[n],c?t[n]=o:M(t,n,o)):c?t[n]=o:R(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||H(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ft=Math.ceil,ct=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ct:ft)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},gt=Math.max,dt=Math.min,yt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?gt(r+n,0):dt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ht={includes:yt(!0),indexOf:yt(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!T(Q,r)&&T(e,r)&&i.push(r);for(;n.length>o;)T(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=vt.f(x(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=C.f,o=E.f,i=0;i<r.length;i++){var u=r[i];T(t,u)||e(t,u,o(n,u))}},Tt=/#|\.prototype\./,Ot=function(t,n){var r=jt[Pt(t)];return r==Et||r!=kt&&("function"==typeof n?a(n):!!n)},Pt=Ot.normalize=function(t){return String(t).replace(Tt,".").toLowerCase()},jt=Ot.data={},kt=Ot.NATIVE="N",Et=Ot.POLYFILL="P",xt=Ot,At=E.f,Ct=Array.isArray||function(t){return"Array"==d(t)},Mt=function(t){return Object(m(t))},Rt=function(t,n,r){var e=w(n);e in t?C.f(t,e,p(0,r)):t[e]=r},zt="process"==d(u.process),Ft=at("navigator","userAgent")||"",It=u.process,Nt=It&&It.versions,Lt=Nt&&Nt.v8;Lt?rt=(nt=Lt.split("."))[0]+nt[1]:Ft&&(!(nt=Ft.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=Ft.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var _t,Dt=rt&&+rt,Ht=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(zt?38===Dt:Dt>37&&Dt<41)})),qt=Ht&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=B("wks"),Bt=u.Symbol,Ut=qt?Bt:Bt&&Bt.withoutSetter||J,Wt=function(t){return T(Gt,t)&&(Ht||"string"==typeof Gt[t])||(Ht&&T(Bt,t)?Gt[t]=Bt[t]:Gt[t]=Ut("Symbol."+t)),Gt[t]},Jt=Wt("species"),Kt=function(t,n){var r;return Ct(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Ct(r.prototype)?b(r)&&null===(r=r[Jt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Qt=Wt("species"),Vt=Wt("isConcatSpreadable"),Yt=9007199254740991,Zt="Maximum allowed index exceeded",Xt=Dt>=51||!a((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(_t="concat",Dt>=51||!a((function(){var t=[];return(t.constructor={})[Qt]=function(){return{foo:1}},1!==t[_t](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Vt];return void 0!==n?!!n:Ct(t)};!function(t,n){var r,e,o,i,a,f=t.target,c=t.global,l=t.stat;if(r=c?u:l?u[f]||R(f,{}):(u[f]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=At(r,e))&&a.value:r[e],!xt(c?e:f+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&M(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Xt||!$t},{concat:function(t){var n,r,e,o,i,u=Mt(this),a=Kt(u,0),f=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(f+(o=pt(i.length))>Yt)throw TypeError(Zt);for(r=0;r<o;r++,f++)r in i&&Rt(a,f,i[r])}else{if(f>=Yt)throw TypeError(Zt);Rt(a,f++,i)}return a.length=f,a}}),r.default.fn.bootstrapTable.locales["eu-EU"]=r.default.fn.bootstrapTable.locales.eu={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Itxaron mesedez"},formatRecordsPerPage:function(t){return"".concat(t," emaitza orriko.")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"".concat(r," erregistroetatik ").concat(t,"etik ").concat(n,"erakoak erakusten (filtered from ").concat(e," total rows)"):"".concat(r," erregistroetatik ").concat(t,"etik ").concat(n,"erakoak erakusten.")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Bilatu"},formatNoMatches:function(){return"Ez da emaitzarik aurkitu"},formatPaginationSwitch:function(){return"Ezkutatu/Erakutsi orrikatzea"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Eguneratu"},formatToggle:function(){return"Ezkutatu/Erakutsi"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Zutabeak"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Guztiak"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["eu-EU"])}));
