/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.18.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).jQuery)}(this,(function(t){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var r=n(t),e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function o(t,n){return t(n={exports:{}},n.exports),n.exports}var i=function(t){return t&&t.Math==Math&&t},u=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof e&&e)||function(){return this}()||Function("return this")(),a=function(t){try{return!!t()}catch(t){return!0}},c=!a((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),f={}.propertyIsEnumerable,l=Object.getOwnPropertyDescriptor,s={f:l&&!f.call({1:2},1)?function(t){var n=l(this,t);return!!n&&n.enumerable}:f},p=function(t,n){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:n}},d={}.toString,g=function(t){return d.call(t).slice(8,-1)},y="".split,h=a((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==g(t)?y.call(t,""):Object(t)}:Object,m=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},v=function(t){return h(m(t))},b=function(t){return"object"==typeof t?null!==t:"function"==typeof t},w=function(t,n){if(!b(t))return t;var r,e;if(n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;if("function"==typeof(r=t.valueOf)&&!b(e=r.call(t)))return e;if(!n&&"function"==typeof(r=t.toString)&&!b(e=r.call(t)))return e;throw TypeError("Can't convert object to primitive value")},S={}.hasOwnProperty,O=function(t,n){return S.call(t,n)},T=u.document,P=b(T)&&b(T.createElement),j=!c&&!a((function(){return 7!=Object.defineProperty((t="div",P?T.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),x=Object.getOwnPropertyDescriptor,A={f:c?x:function(t,n){if(t=v(t),n=w(n,!0),j)try{return x(t,n)}catch(t){}if(O(t,n))return p(!s.f.call(t,n),t[n])}},C=function(t){if(!b(t))throw TypeError(String(t)+" is not an object");return t},E=Object.defineProperty,R={f:c?E:function(t,n,r){if(C(t),n=w(n,!0),C(r),j)try{return E(t,n,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[n]=r.value),t}},M=c?function(t,n,r){return R.f(t,n,p(1,r))}:function(t,n,r){return t[n]=r,t},F=function(t,n){try{M(u,t,n)}catch(r){u[t]=n}return n},N="__core-js_shared__",k=u[N]||F(N,{}),I=Function.toString;"function"!=typeof k.inspectSource&&(k.inspectSource=function(t){return I.call(t)});var L,_,D,H,q=k.inspectSource,z=u.WeakMap,G="function"==typeof z&&/native code/.test(q(z)),B=o((function(t){(t.exports=function(t,n){return k[t]||(k[t]=void 0!==n?n:{})})("versions",[]).push({version:"3.9.1",mode:"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})})),W=0,J=Math.random(),K=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++W+J).toString(36)},Q=B("keys"),U={},V=u.WeakMap;if(G){var Y=k.state||(k.state=new V),X=Y.get,Z=Y.has,$=Y.set;L=function(t,n){return n.facade=t,$.call(Y,t,n),n},_=function(t){return X.call(Y,t)||{}},D=function(t){return Z.call(Y,t)}}else{var tt=Q[H="state"]||(Q[H]=K(H));U[tt]=!0,L=function(t,n){return n.facade=t,M(t,tt,n),n},_=function(t){return O(t,tt)?t[tt]:{}},D=function(t){return O(t,tt)}}var nt,rt,et={set:L,get:_,has:D,enforce:function(t){return D(t)?_(t):L(t,{})},getterFor:function(t){return function(n){var r;if(!b(n)||(r=_(n)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},ot=o((function(t){var n=et.get,r=et.enforce,e=String(String).split("String");(t.exports=function(t,n,o,i){var a,c=!!i&&!!i.unsafe,f=!!i&&!!i.enumerable,l=!!i&&!!i.noTargetGet;"function"==typeof o&&("string"!=typeof n||O(o,"name")||M(o,"name",n),(a=r(o)).source||(a.source=e.join("string"==typeof n?n:""))),t!==u?(c?!l&&t[n]&&(f=!0):delete t[n],f?t[n]=o:M(t,n,o)):f?t[n]=o:F(n,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&n(this).source||q(this)}))})),it=u,ut=function(t){return"function"==typeof t?t:void 0},at=function(t,n){return arguments.length<2?ut(it[t])||ut(u[t]):it[t]&&it[t][n]||u[t]&&u[t][n]},ct=Math.ceil,ft=Math.floor,lt=function(t){return isNaN(t=+t)?0:(t>0?ft:ct)(t)},st=Math.min,pt=function(t){return t>0?st(lt(t),9007199254740991):0},dt=Math.max,gt=Math.min,yt=function(t){return function(n,r,e){var o,i=v(n),u=pt(i.length),a=function(t,n){var r=lt(t);return r<0?dt(r+n,0):gt(r,n)}(e,u);if(t&&r!=r){for(;u>a;)if((o=i[a++])!=o)return!0}else for(;u>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},ht={includes:yt(!0),indexOf:yt(!1)}.indexOf,mt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"].concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return function(t,n){var r,e=v(t),o=0,i=[];for(r in e)!O(U,r)&&O(e,r)&&i.push(r);for(;n.length>o;)O(e,r=n[o++])&&(~ht(i,r)||i.push(r));return i}(t,mt)}},bt={f:Object.getOwnPropertySymbols},wt=at("Reflect","ownKeys")||function(t){var n=vt.f(C(t)),r=bt.f;return r?n.concat(r(t)):n},St=function(t,n){for(var r=wt(n),e=R.f,o=A.f,i=0;i<r.length;i++){var u=r[i];O(t,u)||e(t,u,o(n,u))}},Ot=/#|\.prototype\./,Tt=function(t,n){var r=jt[Pt(t)];return r==At||r!=xt&&("function"==typeof n?a(n):!!n)},Pt=Tt.normalize=function(t){return String(t).replace(Ot,".").toLowerCase()},jt=Tt.data={},xt=Tt.NATIVE="N",At=Tt.POLYFILL="P",Ct=Tt,Et=A.f,Rt=Array.isArray||function(t){return"Array"==g(t)},Mt=function(t){return Object(m(t))},Ft=function(t,n,r){var e=w(n);e in t?R.f(t,e,p(0,r)):t[e]=r},Nt="process"==g(u.process),kt=at("navigator","userAgent")||"",It=u.process,Lt=It&&It.versions,_t=Lt&&Lt.v8;_t?rt=(nt=_t.split("."))[0]+nt[1]:kt&&(!(nt=kt.match(/Edge\/(\d+)/))||nt[1]>=74)&&(nt=kt.match(/Chrome\/(\d+)/))&&(rt=nt[1]);var Dt,Ht=rt&&+rt,qt=!!Object.getOwnPropertySymbols&&!a((function(){return!Symbol.sham&&(Nt?38===Ht:Ht>37&&Ht<41)})),zt=qt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Gt=B("wks"),Bt=u.Symbol,Wt=zt?Bt:Bt&&Bt.withoutSetter||K,Jt=function(t){return O(Gt,t)&&(qt||"string"==typeof Gt[t])||(qt&&O(Bt,t)?Gt[t]=Bt[t]:Gt[t]=Wt("Symbol."+t)),Gt[t]},Kt=Jt("species"),Qt=function(t,n){var r;return Rt(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!Rt(r.prototype)?b(r)&&null===(r=r[Kt])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===n?0:n)},Ut=Jt("species"),Vt=Jt("isConcatSpreadable"),Yt=9007199254740991,Xt="Maximum allowed index exceeded",Zt=Ht>=51||!a((function(){var t=[];return t[Vt]=!1,t.concat()[0]!==t})),$t=(Dt="concat",Ht>=51||!a((function(){var t=[];return(t.constructor={})[Ut]=function(){return{foo:1}},1!==t[Dt](Boolean).foo}))),tn=function(t){if(!b(t))return!1;var n=t[Vt];return void 0!==n?!!n:Rt(t)};!function(t,n){var r,e,o,i,a,c=t.target,f=t.global,l=t.stat;if(r=f?u:l?u[c]||F(c,{}):(u[c]||{}).prototype)for(e in n){if(i=n[e],o=t.noTargetGet?(a=Et(r,e))&&a.value:r[e],!Ct(f?e:c+(l?".":"#")+e,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;St(i,o)}(t.sham||o&&o.sham)&&M(i,"sham",!0),ot(r,e,i,t)}}({target:"Array",proto:!0,forced:!Zt||!$t},{concat:function(t){var n,r,e,o,i,u=Mt(this),a=Qt(u,0),c=0;for(n=-1,e=arguments.length;n<e;n++)if(tn(i=-1===n?u:arguments[n])){if(c+(o=pt(i.length))>Yt)throw TypeError(Xt);for(r=0;r<o;r++,c++)r in i&&Ft(a,c,i[r])}else{if(c>=Yt)throw TypeError(Xt);Ft(a,c++,i)}return a.length=c,a}}),r.default.fn.bootstrapTable.locales["ro-RO"]=r.default.fn.bootstrapTable.locales.ro={formatCopyRows:function(){return"Copy Rows"},formatPrint:function(){return"Print"},formatLoadingMessage:function(){return"Se incarca, va rugam asteptati"},formatRecordsPerPage:function(t){return"".concat(t," inregistrari pe pagina")},formatShowingRows:function(t,n,r,e){return void 0!==e&&e>0&&e>r?"Arata de la ".concat(t," pana la ").concat(n," din ").concat(r," randuri (filtered from ").concat(e," total rows)"):"Arata de la ".concat(t," pana la ").concat(n," din ").concat(r," randuri")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatClearSearch:function(){return"Clear Search"},formatSearch:function(){return"Cauta"},formatNoMatches:function(){return"Nu au fost gasite inregistrari"},formatPaginationSwitch:function(){return"Ascunde/Arata paginatia"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Reincarca"},formatToggle:function(){return"Comuta"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Coloane"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"Toate"},formatAutoRefresh:function(){return"Auto Refresh"},formatExport:function(){return"Export data"},formatJumpTo:function(){return"GO"},formatAdvancedSearch:function(){return"Advanced search"},formatAdvancedCloseButton:function(){return"Close"},formatFilterControlSwitch:function(){return"Hide/Show controls"},formatFilterControlSwitchHide:function(){return"Hide controls"},formatFilterControlSwitchShow:function(){return"Show controls"}},r.default.extend(r.default.fn.bootstrapTable.defaults,r.default.fn.bootstrapTable.locales["ro-RO"])}));
