/* pwstrength-bootstrap 2021-10-29 - GPLv3 & MIT License */

!function(s){var t={};!function(o){"use strict";t.fallback={wordMinLength:"Your password is too short",wordMaxLength:"Your password is too long",wordInvalidChar:"Your password contains an invalid character",wordNotEmail:"Do not use your email as your password",wordSimilarToUsername:"Your password cannot contain your username",wordTwoCharacterClasses:"Use different character classes",wordRepetitions:"Too many repetitions",wordSequences:"Your password contains sequences",errorList:"Errors:",veryWeak:"Very Weak",weak:"Weak",normal:"Normal",medium:"Medium",strong:"Strong",veryStrong:"Very Strong"},t.t=function(r){var e="";return(e=o?o.t(r):t.fallback[r])===r?"":e}}(window.i18next);var r,c={};try{!s&&module&&module.exports&&(s=require("jquery"),r=require("jsdom").jsdom,s=s(r().defaultView))}catch(r){}!function(i){"use strict";var r={};c.forbiddenSequences=["**********","abcdefghijklmnopqrstuvwxyz","qwertyuiop","asdfghjkl","zxcvbnm","!@#$%^&*()_+"],r.wordNotEmail=function(r,e,o){return e.match(/^([\w!#$%&'*+\-/=?^`{|}~]+\.)*[\w!#$%&'*+\-/=?^`{|}~]+@((((([a-z0-9]{1}[a-z0-9-]{0,62}[a-z0-9]{1})|[a-z])\.)+[a-z]{2,6})|(\d{1,3}\.){3}\d{1,3}(:\d{1,5})?)$/i)?o:0},r.wordMinLength=function(r,e,o){var s=e.length,e=Math.pow(s,r.rules.raisePower);return s<r.common.minChar&&(e+=o),e},r.wordMaxLength=function(r,e,o){var s=e.length,e=Math.pow(s,r.rules.raisePower);return s>r.common.maxChar?o:e},r.wordInvalidChar=function(r,e,o){return r.common.invalidCharsRegExp.test(e)?o:0},r.wordMinLengthStaticScore=function(r,e,o){return e.length<r.common.minChar?0:o},r.wordMaxLengthStaticScore=function(r,e,o){return e.length>r.common.maxChar?0:o},r.wordSimilarToUsername=function(r,e,o){r=i(r.common.usernameField).val();return r&&e.toLowerCase().match(r.replace(/[-[\]/{}()*+=?:.\\^$|!,]/g,"\\$&").toLowerCase())?o:0},r.wordTwoCharacterClasses=function(r,e,o){r=new RegExp("(."+r.rules.specialCharClass+")","u");if(e.match(/(\p{Ll}.*\p{Lu})|(\p{Lu}.*\p{Ll})/u)||e.match(/(\p{Letter})/u)&&e.match(/([0-9])/)||e.match(r)&&e.match(/[\p{Letter}0-9_]/u))return o},r.wordRepetitions=function(r,e,o){return e.match(/(.)\1\1/)?o:0},r.wordSequences=function(r,o,e){var s,t=!1;return 2<o.length&&(i.each(c.forbiddenSequences,function(r,e){t||(e=[e,e.split("").reverse().join("")],i.each(e,function(r,e){for(s=0;s<o.length-2;s+=1)-1<e.indexOf(o.toLowerCase().substring(s,s+3))&&(t=!0)}))}),t)?e:0},r.wordLowercase=function(r,e,o){return e.match(/\p{Ll}/u)&&o},r.wordUppercase=function(r,e,o){return e.match(/\p{Lu}/u)&&o},r.wordOneNumber=function(r,e,o){return e.match(/\d+/)&&o},r.wordThreeNumbers=function(r,e,o){return e.match(/(.*[0-9].*[0-9].*[0-9])/)&&o},r.wordOneSpecialChar=function(r,e,o){r=new RegExp(r.rules.specialCharClass,"u");return e.match(r)&&o},r.wordTwoSpecialChar=function(r,e,o){r=new RegExp("(.*"+r.rules.specialCharClass+".*"+r.rules.specialCharClass+")","u");return e.match(r)&&o},r.wordUpperLowerCombo=function(r,e,o){return e.match(/(\p{Ll}.*\p{Lu})|(\p{Lu}.*\p{Ll})/u)&&o},r.wordLetterNumberCombo=function(r,e,o){return e.match(/([\p{Letter}])/u)&&e.match(/([0-9])/)&&o},r.wordLetterNumberCharCombo=function(r,e,o){r=new RegExp("([\p{Letter}0-9].*"+r.rules.specialCharClass+")|("+r.rules.specialCharClass+".*[\p{Letter}0-9])","u");return e.match(r)&&o},r.wordIsACommonPassword=function(r,e,o){return 0<=i.inArray(e,r.rules.commonPasswords)?o:0},c.validation=r,c.executeRules=function(t,n){var a=0;return i.each(t.rules.activated,function(r,e){var o,s;e&&(o=t.rules.scores[r],"function"==typeof(e="function"!=typeof(e=c.validation[r])?t.rules.extra[r]:e)&&((o=e(t,n,o))&&(a+=o),(o<0||!i.isNumeric(o)&&!o)&&0<(s=t.ui.spanError(t,r)).length&&t.instances.errors.push(s)))}),a}}(s);try{module&&module.exports&&(module.exports=c)}catch(r){}var u={common:{}};u.common.minChar=6,u.common.maxChar=20,u.common.usernameField="#username",u.common.invalidCharsRegExp=new RegExp(/[\s,'"]/),u.common.userInputs=[],u.common.onLoad=void 0,u.common.onKeyUp=void 0,u.common.onScore=void 0,u.common.zxcvbn=!1,u.common.zxcvbnTerms=[],u.common.events=["keyup","change","paste"],u.common.debug=!1,u.rules={},u.rules.extra={},u.rules.scores={wordNotEmail:-100,wordMinLength:-50,wordMaxLength:-50,wordInvalidChar:-100,wordSimilarToUsername:-100,wordSequences:-20,wordTwoCharacterClasses:2,wordRepetitions:-25,wordLowercase:1,wordUppercase:3,wordOneNumber:3,wordThreeNumbers:5,wordOneSpecialChar:3,wordTwoSpecialChar:5,wordUpperLowerCombo:2,wordLetterNumberCombo:2,wordLetterNumberCharCombo:2,wordIsACommonPassword:-100},u.rules.activated={wordNotEmail:!0,wordMinLength:!0,wordMaxLength:!1,wordInvalidChar:!1,wordSimilarToUsername:!0,wordSequences:!0,wordTwoCharacterClasses:!0,wordRepetitions:!0,wordLowercase:!0,wordUppercase:!0,wordOneNumber:!0,wordThreeNumbers:!0,wordOneSpecialChar:!0,wordTwoSpecialChar:!0,wordUpperLowerCombo:!0,wordLetterNumberCombo:!0,wordLetterNumberCharCombo:!0,wordIsACommonPassword:!0},u.rules.raisePower=1.4,u.rules.specialCharClass="[!,@,#,$,%,^,&,*,?,_,~]",u.rules.commonPasswords=["123456","password","12345678","qwerty","123456789","12345","1234","111111","1234567","dragon","123123","baseball","abc123","football","monkey","letmein","696969","shadow","master","666666","qwertyuiop","123321","mustang","1234567890","michael","654321","pussy","superman","1qaz2wsx","7777777","fuckyou","121212","000000","qazwsx","123qwe","killer","trustno1","jordan","jennifer","zxcvbnm","asdfgh","hunter","buster","soccer","harley","batman","andrew","tigger","sunshine","iloveyou","fuckme","2000","charlie","robert","thomas","hockey","ranger","daniel","starwars","klaster","112233","george","asshole","computer","michelle","jessica","pepper","1111","zxcvbn","555555","11111111","131313","freedom","777777","pass","fuck","maggie","159753","aaaaaa","ginger","princess","joshua","cheese","amanda","summer","love","ashley","6969","nicole","chelsea","biteme","matthew","access","yankees","987654321","dallas","austin","thunder","taylor","matrix"],u.ui={},u.ui.bootstrap2=!1,u.ui.bootstrap3=!1,u.ui.colorClasses=["danger","danger","danger","warning","warning","success"],u.ui.showProgressBar=!0,u.ui.progressBarEmptyPercentage=1,u.ui.progressBarMinWidth=1,u.ui.progressBarMinPercentage=1,u.ui.progressExtraCssClasses="",u.ui.progressBarExtraCssClasses="",u.ui.showPopover=!1,u.ui.popoverPlacement="bottom",u.ui.showStatus=!1,u.ui.spanError=function(r,e){"use strict";e=r.i18n.t(e);return e?'<span style="color: #d52929">'+e+"</span>":""},u.ui.popoverError=function(r){"use strict";var e=r.instances.errors,o="<div>"+r.i18n.t("errorList")+'<ul class="error-list" style="margin-bottom: 0;">';return s.each(e,function(r,e){o+="<li>"+e+"</li>"}),o+="</ul></div>"},u.ui.showVerdicts=!0,u.ui.showVerdictsInsideProgressBar=!1,u.ui.useVerdictCssClass=!1,u.ui.showErrors=!1,u.ui.showScore=!1,u.ui.container=void 0,u.ui.viewports={progress:void 0,verdict:void 0,errors:void 0,score:void 0},u.ui.scores=[0,14,26,38,50],u.i18n={},u.i18n.t=t.t;var d={};!function(n){"use strict";var a=["error","warning","success"],o=["veryWeak","weak","normal","medium","strong","veryStrong"];d.getContainer=function(r,e){r=n(r.ui.container);return r=!r||1!==r.length?e.parent():r},d.findElement=function(r,e,o){return(e?r.find(e):r).find(o)},d.getUIElements=function(r,e){var o;return r.instances.viewports||(o=d.getContainer(r,e),(e={}).$progressbar=d.findElement(o,r.ui.viewports.progress,"div.progress"),r.ui.showVerdictsInsideProgressBar&&(e.$verdict=e.$progressbar.find("span.password-verdict")),r.ui.showPopover||(r.ui.showVerdictsInsideProgressBar||(e.$verdict=d.findElement(o,r.ui.viewports.verdict,"span.password-verdict")),e.$errors=d.findElement(o,r.ui.viewports.errors,"ul.error-list")),e.$score=d.findElement(o,r.ui.viewports.score,"span.password-score"),r.instances.viewports=e)},d.initHelper=function(r,e,o,s){r=d.getContainer(r,e);s?r.find(s).append(o):n(o).insertAfter(e)},d.initVerdict=function(r,e){d.initHelper(r,e,'<span class="password-verdict"></span>',r.ui.viewports.verdict)},d.initErrorList=function(r,e){d.initHelper(r,e,'<ul class="error-list"></ul>',r.ui.viewports.errors)},d.initScore=function(r,e){d.initHelper(r,e,'<span class="password-score"></span>',r.ui.viewports.score)},d.initUI=function(r,e){r.ui.showPopover?d.initPopover(r,e):(r.ui.showErrors&&d.initErrorList(r,e),r.ui.showVerdicts&&!r.ui.showVerdictsInsideProgressBar&&d.initVerdict(r,e)),r.ui.showProgressBar&&d.initProgressBar(r,e),r.ui.showScore&&d.initScore(r,e)},d.updateVerdict=function(r,e,o,s){e=d.getUIElements(r,e).$verdict;e.removeClass(r.ui.colorClasses.join(" ")),-1<o&&e.addClass(r.ui.colorClasses[o]),r.ui.showVerdictsInsideProgressBar&&e.css("white-space","nowrap"),e.html(s)},d.updateErrors=function(r,e,o){var e=d.getUIElements(r,e).$errors,s="";o||n.each(r.instances.errors,function(r,e){s+="<li>"+e+"</li>"}),e.html(s)},d.updateScore=function(r,e,o,s){r=d.getUIElements(r,e).$score,e="";s||(e=o.toFixed(2)),r.html(e)},d.updateFieldStatus=function(o,r,e,s){var t=r;o.ui.bootstrap2?t=r.parents(".control-group").first():o.ui.bootstrap3&&(t=r.parents(".form-group").first()),n.each(a,function(r,e){e=d.cssClassesForBS(o,e),t.removeClass(e)}),s||(e=a[Math.floor(e/2)],e=d.cssClassesForBS(o,e),t.addClass(e))},d.cssClassesForBS=function(r,e){return r.ui.bootstrap3?e="has-"+e:r.ui.bootstrap2||(e="border-"+(e="error"===e?"danger":e)),e},d.getVerdictAndCssClass=function(r,e){return void 0===e?["",0]:(e=e<=r.ui.scores[0]?0:e<r.ui.scores[1]?1:e<r.ui.scores[2]?2:e<r.ui.scores[3]?3:e<r.ui.scores[4]?4:5,[r.i18n.t(o[e]),e])},d.updateUI=function(r,e,o){var s,t=d.getVerdictAndCssClass(r,o),n=0===o?"":t[0];t=t[1],s=r.ui.useVerdictCssClass?t:-1,r.ui.showProgressBar&&d.showProgressBar(r,e,o,t,s,n),r.ui.showStatus&&d.updateFieldStatus(r,e,t,void 0===o),r.ui.showPopover?d.updatePopover(r,e,n,void 0===o):(r.ui.showVerdicts&&!r.ui.showVerdictsInsideProgressBar&&d.updateVerdict(r,e,s,n),r.ui.showErrors&&d.updateErrors(r,e,void 0===o)),r.ui.showScore&&d.updateScore(r,e,o,void 0===o)}}(s),function(a){"use strict";d.percentage=function(r,e,o){o=Math.floor(100*e/o),r=r.ui.progressBarMinPercentage;return o=100<(o=o<=r?r:o)?100:o},d.initProgressBar=function(r,e){var o=d.getContainer(r,e),s='<div class="progress ';r.ui.bootstrap2?s+=r.ui.progressBarExtraCssClasses+'"><div class="':s+=r.ui.progressExtraCssClasses+'"><div class="'+r.ui.progressBarExtraCssClasses+" progress-",s+='bar">',r.ui.showVerdictsInsideProgressBar&&(s+='<span class="password-verdict"></span>'),s+="</div></div>",r.ui.viewports.progress?o.find(r.ui.viewports.progress).append(s):a(s).insertAfter(e)},d.showProgressBar=function(r,e,o,s,t,n){o=void 0===o?r.ui.progressBarEmptyPercentage:d.percentage(r,o,r.ui.scores[4]);d.updateProgressBar(r,e,s,o),r.ui.showVerdictsInsideProgressBar&&d.updateVerdict(r,e,t,n)},d.updateProgressBar=function(o,r,e,s){var r=d.getUIElements(o,r).$progressbar,t=r.find(".progress-bar"),n="progress-";o.ui.bootstrap2&&(t=r.find(".bar"),n=""),a.each(o.ui.colorClasses,function(r,e){o.ui.bootstrap2||o.ui.bootstrap3?t.removeClass(n+"bar-"+e):t.removeClass("bg-"+e)}),o.ui.bootstrap2||o.ui.bootstrap3?t.addClass(n+"bar-"+o.ui.colorClasses[e]):t.addClass("bg-"+o.ui.colorClasses[e]),0<s?t.css("min-width",o.ui.progressBarMinWidth+"px"):t.css("min-width",""),t.css("width",s+"%")}}(s),function(){"use strict";d.initPopover=function(r,e){try{e.popover("destroy")}catch(r){e.popover("dispose")}e.popover({html:!0,placement:r.ui.popoverPlacement,trigger:"manual",content:" "})},d.updatePopover=function(r,e,o,s){var t=e.data("bs.popover"),n="",a=!0,i=!1;r.ui.showVerdicts&&!r.ui.showVerdictsInsideProgressBar&&0<o.length&&(n='<h5><span class="password-verdict">'+o+"</span></h5>",a=!1),r.ui.showErrors&&(0<r.instances.errors.length&&(a=!1),n+=r.ui.popoverError(r)),a||s?e.popover("hide"):(r.ui.bootstrap2?t=e.data("popover"):t||(t=bootstrap.Popover.getInstance(e[0]),i=!0),(i?$(t.tip).is(":visible"):t.$arrow&&0<t.$arrow.parents("body").length)?(i?$(t.tip).find(".popover-body"):e.find("+ .popover .popover-content")).html(n):(r.ui.bootstrap2||r.ui.bootstrap3?t.options.content=n:i?t._config.content=n:t.config.content=n,e.popover("show")))}}();var p={};!function(a){"use strict";var o,n=function(r){var o,e,s=a(r.target),t=s.data("pwstrength-bootstrap"),n=s.val();void 0!==t&&(t.instances.errors=[],0===n.length?e=void 0:(e=t.common.zxcvbn?(o=[],a.each(t.common.userInputs.concat([t.common.usernameField]),function(r,e){e=a(e).val();e&&o.push(e)}),o=o.concat(t.common.zxcvbnTerms),e=zxcvbn(n,o).guesses,Math.log(e)*Math.LOG2E):c.executeRules(t,n),"function"==typeof t.common.onScore&&(e=t.common.onScore(t,n,e))),d.updateUI(t,s,e),s=(n=d.getVerdictAndCssClass(t,e))[1],n=n[0],t.common.debug&&console.log(e+" - "+n),"function"==typeof t.common.onKeyUp&&t.common.onKeyUp(r,{score:e,verdictText:n,verdictLevel:s}))},i=function(r){var e=a(r.target),o=e.val(),s=0,t=function(){e.val()!==o?n(r):s<3&&(s+=1,setTimeout(t,100))};setTimeout(t,100)};p.init=function(t){return this.each(function(r,e){var o=a.extend(!0,{},u),o=a.extend(!0,o,t),s=a(e);o.instances={},s.data("pwstrength-bootstrap",o),a.each(o.common.events,function(r,e){s.on(e,"paste"===e?i:n)}),d.initUI(o,s),s.trigger("keyup"),"function"==typeof o.common.onLoad&&o.common.onLoad()}),this},p.destroy=function(){return this.each(function(r,e){var o=a(e),e=o.data("pwstrength-bootstrap"),e=d.getUIElements(e,o);e.$progressbar.remove(),e.$verdict.remove(),e.$errors.remove(),o.removeData("pwstrength-bootstrap")}),this},p.forceUpdate=function(){return this.each(function(r,e){n({target:e})}),this},p.addRule=function(o,s,t,n){return this.each(function(r,e){e=a(e).data("pwstrength-bootstrap");e.rules.activated[o]=n,e.rules.scores[o]=t,e.rules.extra[o]=s}),this},o=function(o,s,t){this.each(function(r,e){a(e).data("pwstrength-bootstrap").rules[s][o]=t})},p.changeScore=function(r,e){return o.call(this,r,"scores",e),this},p.ruleActive=function(r,e){return o.call(this,r,"activated",e),this},p.ruleIsMet=function(t){var n=0;return"wordMinLength"===t?t="wordMinLengthStaticScore":"wordMaxLength"===t&&(t="wordMaxLengthStaticScore"),this.each(function(r,e){var o=a(e).data("pwstrength-bootstrap"),s=c.validation[t];"function"==typeof(s="function"!=typeof s?o.rules.extra[t]:s)&&(e=s(o,a(e).val(),1),a.isNumeric(e)&&(n+=e))}),n===this.length},a.fn.pwstrength=function(r){var e;return p[r]?e=p[r].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof r&&r?a.error("Method "+r+" does not exist on jQuery.pwstrength-bootstrap"):e=p.init.apply(this,arguments),e}}(s)}(jQuery);
//# sourceMappingURL=pwstrength-bootstrap.min.map
