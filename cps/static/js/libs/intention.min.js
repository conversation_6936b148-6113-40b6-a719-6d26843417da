/*! intention.js v0.9.9 
* http://intentionjs.com/ 
* 
* intention.js 
* 
* Copyright 2008, 2013
* Dowjones and other contributors. 
* Released under the MIT license. 
**/ !function(a,b){"use strict";"function"==typeof define&&define.amd?define("intention",["jquery","underscore"],b):a.Intention=b(a.jQ<PERSON>y,a._)}(this,function(a,b){"use strict";var c=function(c){var d=b.extend(this,c,{_listeners:{},contexts:[],elms:a(),axes:{},priority:[]});return d};return c.prototype={responsive:function d(a,c){var e,f="abcdefghijklmnopqrstuvwxyz0123456789",g=f.length,h="";for(e=0;5>e;e++)h+=f[Math.floor(Math.random()*g)];var i={matcher:function(a,b){return a===b.name},measure:b.identity,ID:h};if(b.isObject(c)===!1&&(c={}),b.isArray(a)&&b.isArray(a[0].contexts))return b.each(a,function(a){d.apply(this,a)},this),void 0;b.isArray(a)===!1&&b.isObject(a)?c=a:c.contexts=a,c=b.extend({},i,c),this.on("_"+c.ID+":",b.bind(function(a){this.axes=this._contextualize(c.ID,a.context,this.axes),this._respond(this.axes,this.elms)},this));var j={ID:c.ID,current:null,contexts:c.contexts,respond:b.bind(this._responder(c.ID,c.contexts,c.matcher,c.measure),this)};return this.axes[c.ID]=j,this.axes.__keys__=this.priority,this.priority.unshift(c.ID),j},elements:function(c){return c||(c=document),a("[data-intent],[intent],[data-in],[in]",c).each(b.bind(function(b,c){this.add(a(c))},this)),this},add:function(c,d){var e;return d||(d={}),c.each(b.bind(function(c,f){var g=!1;this.elms.each(function(a,b){return f===b.elm?(g=!0,!1):!0}),g===!1&&(e=this._fillSpec(b.extend(d,this._attrsToSpec(f.attributes,this.axes))),this._makeChanges(a(f),e,this.axes),this.elms.push({elm:f,spec:e}))},this)),this},remove:function(a){var b=this.elms;return a.each(function(a,c){b.each(function(a,d){return c===d.elm?(b.splice(a,1),!1):!0})}),this},is:function(a){var c=this.axes;return b.some(c.__keys__,function(b){return a===c[b].current})},current:function(a){return this.axes.hasOwnProperty(a)?this.axes[a].current:!1},on:function(a,b){var c=a.split(" "),d=0;for(d;d<c.length;d++)void 0===this._listeners[c[d]]&&(this._listeners[c[d]]=[]),this._listeners[c[d]].push(b);return this},off:function(a,c){if(b.isArray(this._listeners[a])){var d=this._listeners[a],e=0;for(e;e<d.length;e++)if(d[e]===c){d.splice(e,1);break}}return this},_responder:function(a,c,d,e){var f;return function(){var g=e.apply(this,arguments);return b.every(c,function(c){return d(g,c)?void 0===f||c.name!==f.name?(f=c,this._emitter({_type:"_"+a+":",context:f.name},f,this)._emitter({_type:a+":",context:f.name},f,this)._emitter(b.extend({},{_type:a+":"+f.name},f),f,this)._emitter(b.extend({},{_type:f.name},f),f,this),!1):!1:!0},this),this}},_emitter:function(a){if("string"==typeof a&&(a={_type:a}),a.target||(a.target=this),!a._type)throw new Error(a._type+" is not a supported event.");if(b.isArray(this._listeners[a._type])){var c,d=this._listeners[a._type];for(c=0;c<d.length;c++)d[c].apply(this,arguments)}return this},_fillSpec:function(a){var c=function(c){b.each(a,function(a,d){b.each(a,function(a,b){c(a,b,d)})})},d={};return c(function(a){b.isObject(a)&&b.each(a,function(a,b){d[b]=""})}),c(function(c,e,f){b.isObject(c)&&(a[f][e]=b.extend({},d,c))}),a},_assocAxis:function(a,c){var d=!1;return b.every(c.__keys__,function(e){return d===!1?(b.every(c[e].contexts,function(b){return b.name===a?(d=e,!1):!0}),!0):!1}),d},_makeSpec:function(a,b,c,d,e){var f;return void 0!==e[a]?(f=e[a],void 0===f[b]&&(f[b]={})):(f={},f[b]={},e[a]=f),f[b][c]=d,e},_attrsToSpec:function(a,c){var d={},e=new RegExp("^(data-)?(in|intent)-(([a-zA-Z0-9][a-zA-Z0-9]*:)?([a-zA-Z0-9]*))-([A-Za-z:-]+)"),f=new RegExp("^(data-)?(in|intent)-([a-zA-Z0-9][_a-zA-Z0-9]*):$");return b.each(a,function(a){var g,h=a.name.match(e);if(null!==h){if(h=h.slice(-3),g=h[0],void 0===h[0]||""===h[0]){if(h[0]=this._assocAxis(h[1],c),h[0]===!1)return}else h[0]=h[0].replace(/:$/,"");h.push(a.value),h.push(d),d=this._makeSpec.apply(this,h)}else f.test(a.name)&&(g=a.name.match(f)[3],b.each(c[g].contexts,function(b){this._makeSpec(g,b.name,"class",b.name+" "+a.value,d)},this))},this),d},_contextSpec:function(a,b){return b.hasOwnProperty(a.axis)&&b[a.axis].hasOwnProperty(a.ctx)?b[a.axis][a.ctx]:{}},_resolveSpecs:function(c,d){var e={},f=["append","prepend","before","after"];return b.each(c,function(c){b.each(this._contextSpec(c,d),function(c,d){"class"===d?(e[d]||(e[d]=[]),e[d]=b.union(e[d],c.split(" "))):void 0!==e.move&&""!==e.move.value||-1===a.inArray(d,f)?(void 0===e[d]||""===e[d])&&(e[d]=c):e.move={value:c,placement:d}},this)},this),e},_currentContexts:function(a){var c=[];return b.each(a.__keys__,function(b){return null!==a[b].current?(c.push({ctx:a[b].current,axis:b}),void 0):void 0}),c},_removeClasses:function(a,c){var d=[];return b.each(c.__keys__,function(e){var f=c[e];b.each(f.contexts,function(c){if(c.name!==f.current){var e,g=this._contextSpec({axis:f.ID,ctx:c.name},a);void 0!==g&&void 0!==g["class"]&&(e=g["class"].split(" "),void 0!==e&&(d=b.union(d,e)))}},this)},this),d},_contextConfig:function(a,b){return this._resolveSpecs(this._currentContexts(b),a,b)},_makeChanges:function(c,d,e){if(b.isEmpty(e)===!1){var f=this._contextConfig(d,e);b.each(f,function(f,g){if("move"===g)(d.__placement__!==f.placement||d.__move__!==f.value)&&(a(f.value)[f.placement](c),d.__placement__=f.placement,d.__move__=f.value);else if("class"===g){var h=c.attr("class")||"";h=b.union(f,b.difference(h.split(" "),this._removeClasses(d,e))),c.attr("class",h.join(" "))}else c.attr(g,f)},this)}return c},_respond:function(c,d){d.each(b.bind(function(b,d){var e=a(d.elm);this._makeChanges(e,d.spec,c),e.trigger("intent",this)},this))},_contextualize:function(a,b,c){return c[a].current=b,c},_axis_test_pattern:new RegExp("^_[a-zA-Z0-9]"),_axis_match_pattern:new RegExp("^_([a-zA-Z0-9][_a-zA-Z0-9]*)"),_trim_pattern:new RegExp("^s+|s+$","g")},c});
