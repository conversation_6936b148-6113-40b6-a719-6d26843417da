function gi(){}
function ci(){}
function cd(){}
function cj(){}
function dj(){}
function ub(){}
function Mb(){}
function Mn(){}
function Gn(){}
function Yn(){}
function Xc(){}
function Hc(){}
function ik(){}
function xk(){}
function Ak(){}
function Pp(){}
function ps(){}
function Cx(){}
function LA(){}
function OA(){}
function TA(){}
function Tp(a){}
function Ip(a){}
function Pi(a){xi()}
function Xi(a,b){a.a=b}
function Xn(a,b){a.c=b}
function Xr(a,b){a.c=b}
function Wr(a,b){a.b=b}
function Db(a,b){a.d=b}
function Qj(){this.a=0}
function nt(a){this.a=a}
function Mc(a){this.a=a}
function Oc(a){this.a=a}
function ji(a){this.a=a}
function zr(a){this.a=a}
function ss(a){this.a=a}
function sv(a){this.a=a}
function Sv(a){this.a=a}
function Yv(a){this.a=a}
function Gv(a){this.c=a}
function dx(a){this.c=a}
function rn(a){this.b=a}
function rz(a){this.a=a}
function aw(a){this.a=a}
function fw(a){this.a=a}
function Hy(a){this.a=a}
function mA(a){this.a=a}
function JA(a){this.a=a}
function Jw(){tw(this)}
function Jx(){fv(this)}
function Wp(){Tp(this)}
function yz(){tz(this)}
function vy(){return}
function Cz(a){return a}
function Tz(a){Lz();Jz=a}
function Qi(a){xi();return}
function Gq(){yq();new Wq}
function Wi(){Wi=ci;new Jx}
function xi(){xi=ci;Mi()}
function Bs(){Bs=ci;Bs()}
function Ib(){Ib=ci;bj()}
function Ob(){this.a=lc()}
function ot(a){this.a=Fp(a)}
function Fo(){this.g=new Wq}
function wp(){vp(this,null)}
function Kj(){Dj();Ej(this)}
function dt(){ac.call(this)}
function Ft(){ac.call(this)}
function Hr(){Dr.call(this)}
function Ir(){Dr.call(this)}
function Tr(){Dr.call(this)}
function vs(){ac.call(this)}
function xs(){vs.call(this)}
function zs(){ac.call(this)}
function it(){ac.call(this)}
function kt(){ac.call(this)}
function tu(){ac.call(this)}
function Hx(){ac.call(this)}
function Wx(){Wx=ci;Vx=Yx()}
function oy(){ac.call(this)}
function oi(b,a){b.set(a)}
function hd(b,a){b.src=a}
function fd(b,a){b.width=a}
function ed(b,a){b.height=a}
function Pj(a,b){a.a=b.a}
function Pq(a,b){vw(a.b,b)}
function Mz(a){Lz();vw(Kz,a)}
function Sc(a){Rc();Qc.eb(a)}
function Ic(a){return a.db()}
function Jk(a){return btoa(a)}
function qx(a){Uy(a);this.a=a}
function Yr(){this.a=new Jw}
function Wq(){this.b=new Jw}
function qA(){this.d=new Jw}
function Dr(){this.e=new Xm}
function ec(){ec=ci;dc=new ub}
function zc(){zc=ci;yc=new Hc}
function Gk(){Gk=ci;Fk=new Jx}
function Ax(){Ax=ci;zx=new Cx}
function ru(){ru=ci;qu=new ps}
function Ek(){Ek=ci;ru();Dk=qu}
function bc(a){_b.call(this,a)}
function os(a){_b.call(this,a)}
function ws(a){bc.call(this,a)}
function As(a){bc.call(this,a)}
function Ht(a){bc.call(this,a)}
function jt(a){bc.call(this,a)}
function lt(a){bc.call(this,a)}
function Et(a){bc.call(this,a)}
function Gt(a){cc.call(this,a)}
function Ot(a){jt.call(this,a)}
function ys(a){ws.call(this,a)}
function pu(a){ws.call(this,a)}
function uu(a){bc.call(this,a)}
function Iy(a){Hy.call(this,a)}
function py(){bc.call(this,gC)}
function ku(){ss.call(this,'')}
function nu(){ss.call(this,'')}
function pc(){pc=ci;!!(Rc(),Qc)}
function hz(){hz=ci;ez={};gz={}}
function uz(a){postMessage(a)}
function At(a){return a<0?-a:a}
function lj(a,b){return a.c-b.c}
function Bt(a,b){return a>b?a:b}
function Ct(a,b){return a<b?a:b}
function kd(a,b){return Vs(a,b)}
function Ns(a){Ms(a);return a.k}
function $x(){Wx();return new Vx}
function Yh(){Wh==null&&(Wh=[])}
function Op(a,b,c){Kl(c,a.b[b])}
function By(a,b,c){a.splice(b,c)}
function Hi(a,b){a.__listener=b}
function Oj(a,b){a.a=b<<24>>24}
function Rj(a){this.a=a<<24>>24}
function Kb(a){Db(this,(xi(),a))}
function Kp(){Ip(this);this.a=0}
function Jp(){Ip(this);this.a=0}
function Ci(a){xi();Fi();Oi(a,DB)}
function up(b,a){b.array.push(a)}
function jj(b,a){b.responseType=a}
function Zl(a,b){a.d.e[a.c+a.a]=b}
function $l(a,b){a.d.e[a.c+a.b]=b}
function am(a,b){a.d.e[a.c+a.e]=b}
function qn(a,b,c){a.b.c[b+a.a]=c}
function ol(a,b){return jl(a.a,b)}
function Kh(a,b){return Gh(a,b)>0}
function Nh(a,b){return Gh(a,b)<0}
function Oh(a,b){return Gh(a,b)<=0}
function Lh(a,b){return Gh(a,b)>=0}
function gv(a){return a.a.c+a.b.c}
function Dc(a){return !!a.a||!!a.f}
function cl(a,b){this.b=a;this.a=b}
function Bn(a,b){this.b=a;this.a=b}
function yr(a,b){this.b=a;this.a=b}
function al(a,b){this.a=a;this.b=b}
function bq(a,b){this.a=a;this.b=b}
function pw(a,b){this.a=a;this.b=b}
function oA(a,b){this.a=a;this.b=b}
function wA(a,b){this.a=a;this.b=b}
function yA(a,b){this.a=a;this.b=b}
function uA(a,b){this.i=a;this.e=b}
function Ro(){this.b=0;this.a=null}
function ee(a){return typeof a===bB}
function fe(a){return typeof a===cB}
function ie(a){return typeof a===dB}
function Mh(a){return typeof a===cB}
function Xt(a,b){return Uy(a),a===b}
function mt(a,b){return pt(a.a,b.a)}
function ay(a,b){return a.a.get(b)}
function bu(a,b){return a.substr(b)}
function Qm(a){return a.ymax-a.ymin}
function Wm(a){return a.xmax-a.xmin}
function vu(a,b){return Vt(a.a,b.a)}
function zi(a,b){xi();Fi();Oi(a,b)}
function zy(a,b,c){a.splice(b,0,c)}
function lu(a,b){a.a+=''+b;return a}
function mu(a,b){a.a+=''+b;return a}
function Px(a){this.a=$x();this.b=a}
function dy(a){this.a=$x();this.b=a}
function ke(a){return a==null?null:a}
function iu(a){return ju(a,a.length)}
function Yt(a,b){return a.indexOf(b)}
function vz(a,b){postMessage(a,[b])}
function El(a,b){a.c=b;a.b=sl(a,a.k)}
function Tn(a,b){a.a=a.a+(b<<16>>16)}
function Un(a,b){a.a=a.a-(b<<16>>16)}
function Wn(a,b){a.b=a.b-(b<<16>>16)}
function Vn(a,b){a.b=a.b+(b<<16>>16)}
function Bi(a,b){xi();a.__listener=b}
function no(a,b){return kq(a.d,b)!=0}
function Lu(a){return !a?null:a.Vb()}
function gt(a){return Xt(cB,typeof a)}
function Zt(a){return Xt(dB,typeof a)}
function cu(a,b){return a.substr(0,b)}
function az(a,b){return parseInt(a,b)}
function gd(a,b){return a.contains(b)}
function wd(a){return xd(a.l,a.m,a.h)}
function wc(a){$wnd.clearTimeout(a)}
function $n(){$n=ci;Zn=new nt(-1)}
function xc(){mc!=0&&(mc=0);oc=-1}
function Fi(){if(!Di){Ni();Di=true}}
function sr(a){Wq.call(this);this.a=a}
function qs(){os.call(this,'UTF-8')}
function Ky(){Hy.call(this,'UTF-8')}
function Gl(){tl.call(this,3,3,3,true)}
function tw(a){a.a=od(pg,$A,1,0,5,1)}
function Pb(a){a.g=od(rg,$A,42,0,0,1)}
function ou(a){ss.call(this,(Uy(a),a))}
function ry(a){return a!=null?Ab(a):0}
function pt(a,b){return a<b?-1:a>b?1:0}
function Dt(a,b){return Gh(a,b)<0?a:b}
function Tt(a,b){return a.charCodeAt(b)}
function Dh(a){return a.backingJsObject}
function Vq(a){ar(true);a.b.a.length=0}
function Gw(a,b){ox(a.a,a.a.length,b)}
function Up(a,b){a.a=b;a.c=0;return a}
function nq(a,b){a.k=b;lq(a);return a}
function vp(b,a){b.array=a?a.array:[]}
function _y(a,b){return a==b?0:a<b?-1:1}
function xd(a,b,c){return {l:a,m:b,h:c}}
function Nb(d,a,b,c){d.drawImage(a,b,c)}
function My(a){if(!a){throw Dh(new it)}}
function Yy(a){if(!a){throw Dh(new kt)}}
function $y(a){if(!a){throw Dh(new dt)}}
function Qy(a){if(!a){throw Dh(new zs)}}
function Sy(a){if(!a){throw Dh(new oy)}}
function mz(a){if(!a.c)return;a.b=true}
function dz(a){return a.$H||(a.$H=++cz)}
function jq(a){return iq(a,DB+(a.a>>1))}
function pi(a){return new Int16Array(a)}
function qi(a){return new Int32Array(a)}
function ti(a){return new Uint8Array(a)}
function ui(a){return new Uint8Array(a)}
function de(a,b){return a!=null&&Zd(a,b)}
function $t(a,b){return a.lastIndexOf(b)}
function ii(c,a,b){return a.replace(c,b)}
function Vk(a,b){return av(a.e,b)!=null}
function _l(a,b,c){a.c=(sl(a.d,b)+c)*ql}
function NA(a,b,c){a.c=b;a.d=c;return a}
function dn(a,b){a.c=new jk(b);return a}
function Sj(a,b){b-a.a.length>0&&Tj(a,b)}
function Sk(a,b){return $d(av(a.e,b),39)}
function qk(a,b){return $d(av(a.b,b),21)}
function Hw(a){return xy(a.a,a.a.length)}
function Ri(a){a.preventDefault();Si(a)}
function Rz(a){Lz();if(Gz!=a){Gz=a;Nz()}}
function Qz(a){Lz();$wnd.DJVU_CONTEXT=a}
function Lz(){Lz=ci;Iz=new Xm;Kz=new Jw}
function Jl(){Jl=ci;Il=new Nl(-1,-1,-1)}
function Vj(){this.a=od(me,UB,8,32,15,1)}
function ac(){Pb(this);Qb(this);this.bb()}
function Oo(){Fo.call(this);this.a=new Wq}
function Nr(){Dr.call(this);this.d=new Wq}
function ns(){Xm.call(this);this.b=new wp}
function hs(a,b){a.b=cs(b);a.c=a.b.buffer}
function sl(a,b){return b*a.rb()+a.border}
function il(a,b){return jl(a,sk(a.c,b).b)}
function si(c,a,b){return c.subarray(a,b)}
function ic(a){return a==null?null:a.name}
function gA(a){return !a.i?0:a.i.a.length}
function Vc(a){Rc();return parseInt(a)||-1}
function Es(a,b){Bs();return a==b?0:a?1:-1}
function Ds(a){Bs();return Xt(bB,typeof a)}
function Wt(a,b,c,d){return iu(d.Yb(a,b,c))}
function bz(b,c,d){try{b[c]=d}catch(a){}}
function Ez(a,b){this.order=a;this.data=b}
function Ml(){Jl();this.f=this.g=this.i=-51}
function Gx(a){var b;b=a[HC]|0;a[HC]=b+1}
function Qo(a,b){a.b=b;a.a=new Gl;return a}
function be(a){$y(a==null||ie(a));return a}
function Ms(a){if(a.k!=null){return}Zs(a)}
function nz(a){Gc((zc(),yc),a);return false}
function Gc(a,b){a.a=Jc(a.a,[b,true]);Ec(a)}
function Zj(a,b){a.d=b;a.c=0;a.a=eB;return a}
function he(a,b){return a&&b&&a instanceof b}
function _t(a,b,c){return a.lastIndexOf(b,c)}
function qc(a,b,c){return a.apply(b,c);var d}
function hc(a){return a==null?null:a.message}
function kj(){return new $wnd.XMLHttpRequest}
function Zu(a,b){return $u(b,a.b)||$u(b,a.a)}
function Aw(a,b){return Bw(a,b,a.a.length-1)}
function Ny(a,b){if(!a){throw Dh(new jt(b))}}
function Ry(a,b){if(!a){throw Dh(new As(b))}}
function ar(a){if(!a){throw Dh(new ys('0'))}}
function us(){bc.call(this,'divide by zero')}
function QA(a){PA.call(this,a.a,a.b,a.c,a.d)}
function wm(){om();tl.call(this,0,1,2,false)}
function Ay(a,b,c){yy(c,0,a,b,c.length,false)}
function Tq(a,b){_q(b,a.b.a.length);Cw(a.b,b)}
function ox(a,b,c){Oy(b,a.length);mx(a,0,b,c)}
function Bx(a,b){return Uy(a),Fs(a,(Uy(b),b))}
function $d(a,b){$y(a==null||Zd(a,b));return a}
function vw(a,b){a.a[a.a.length]=b;return true}
function io(a,b){a.U[0]=a.U[1]=a.U[2]=b;a.V=0}
function Om(a){a.xmin=a.xmax=a.ymin=a.ymax=0}
function Jn(a){a.j=a.e=a.a=null;a.i=a.g=0;Ek()}
function bj(){bj=ci;aj=new dj;aj?new cj:aj}
function zt(){zt=ci;yt=od(jg,$A,18,256,0,1)}
function ft(a,b){return ht((Uy(a),a),(Uy(b),b))}
function Ut(a,b){return _y((Uy(a),a),(Uy(b),b))}
function Cs(a,b){return Es((Uy(a),a),(Uy(b),b))}
function Sh(a,b){return Hh(Od(Mh(a)?Uh(a):a,b))}
function Hp(a){return fe(a)?le((Uy(a),a)):a.Hb()}
function uw(a,b,c){Wy(b,a.a.length);zy(a.a,b,c)}
function yw(a,b){Ty(b,a.a.length);return a.a[b]}
function um(a,b,c,d){a.k=c;a.i=d;a.e=b;return a}
function tq(a,b){a.c=b.c;a.b=b.b;a.d=b.d;a.a=b.a}
function Fl(a,b){if(b!=a.k){a.k=b;a.b=sl(a,a.k)}}
function Vy(a,b){if(a==null){throw Dh(new Ht(b))}}
function _q(a,b){if(a<0||a>=b){throw Dh(new xs)}}
function Uq(a,b,c){_q(c,a.b.a.length);Fw(a.b,c,b)}
function wj(a,b,c){this.b=a;this.c=b;this.a=c}
function ky(a,b,c){this.a=a;this.b=b;this.c=c}
function nl(){el();this.b=new Jx;this.c=new uk}
function Ao(){$n();mo.call(this);this.e=new Qj}
function Fz(a,b,c){Ez.call(this,a,b);this.data2=c}
function Bu(a){jt.call(this,a==null?jB:(Uy(a),a))}
function Cu(a){jt.call(this,a==null?jB:(Uy(a),a))}
function Lw(a){tw(this);Ay(this.a,0,a.toArray())}
function qd(a){return Array.isArray(a)&&a.ac===gi}
function ce(a){return !Array.isArray(a)&&a.ac===gi}
function ge(a){return a!=null&&je(a)&&!(a.ac===gi)}
function Yu(a,b){return ie(b)?bv(a,b):!!Mx(a.a,b)}
function Id(a,b){return xd(a.l&b.l,a.m&b.m,a.h&b.h)}
function Nd(a,b){return xd(a.l|b.l,a.m|b.m,a.h|b.h)}
function tk(a,b){return dq(a.c,$d(Rq(a.d,b),21).b)}
function _x(a,b){return !(a.a.get(b)===undefined)}
function _u(a,b){return ie(b)?av(a,b):Lu(Mx(a.a,b))}
function md(a,b,c,d,e,f){return nd(a,b,c,d,e,0,f)}
function Sq(a,b,c){_q(c,a.b.a.length+1);uw(a.b,c,b)}
function Jc(a,b){!a&&(a=[]);a[a.length]=b;return a}
function Qs(a,b){var c;c=new Os;c.g=a;c.d=b;return c}
function Rs(a,b,c){var d;d=Qs(a,b);bt(c,d);return d}
function zo(a,b,c,d){a.c=c;a.d=new rq(b);a.a=d;a.b=0}
function Qq(a,b){return _q(b,a.b.a.length),yw(a.b,b)}
function Rq(a,b){_q(b,a.b.a.length);return yw(a.b,b)}
function xy(a,b){var c;c=a.slice(0,b);return td(c,a)}
function rd(a,b,c){Qy(c==null||jd(a,c));return a[b]=c}
function Uy(a){if(a==null){throw Dh(new Ft)}return a}
function Fx(a,b){if(b[HC]!=a[HC]){throw Dh(new Hx)}}
function xz(a,b){vz(new Fz('page-text',''+a,b),b.c)}
function cv(a,b,c){return ie(b)?dv(a,b,c):Nx(a.a,b,c)}
function gk(a,b){var c;c=a.c+b;Nh(c,a.a)&&(a.a=Vh(c))}
function Ts(a,b){var c;c=Qs('',a);c.j=b;c.f=1;return c}
function fl(a){var b;b=a.a;!b&&(a.a=b=new Yr);return b}
function Bq(a){switch(a){case 1:case 2:case 3:return;}}
function Eq(a){switch(a){case 4:case 2:case 5:return;}}
function _b(a){Pb(this);this.f=a;Qb(this);this.bb()}
function rq(a){fq();qq.call(this);this.k=a;lq(this)}
function Hq(a){return a==null||a.length==0?-1:Iq(a,$B)}
function je(a){return typeof a===XA||typeof a===aB}
function Tm(a){return a.xmin>=a.xmax||a.ymin>=a.ymax}
function Xm(){this.xmin=this.xmax=this.ymin=this.ymax=0}
function uk(){this.b=new Jx;this.a=new Wq;this.d=new Wq}
function Pt(a,b,c){this.a=_A;this.d=a;this.b=b;this.c=c}
function $j(a,b){a.d=Vp(new Wp,b);a.c=0;a.a=eB;return a}
function Kl(a,b){Zl(a,b.sb());$l(a,b.tb());am(a,b.ub())}
function Az(a){Rz(a.page);Sz(a.tileRange,a.subsample)}
function Vh(a){if(Mh(a)){return a|0}return a.l|a.m<<22}
function Lx(a,b){var c;c=a.a.get(b);return c==null?[]:c}
function Ix(a,b){return ke(a)===ke(b)||a!=null&&wb(a,b)}
function qy(a,b){return ke(a)===ke(b)||a!=null&&wb(a,b)}
function Tb(a,b){a.backingJsObject=b;b!=null&&bz(b,fB,a)}
function sA(a,b){a.a=b;uz(new Fz('page-info',''+a.e,b))}
function bv(a,b){return b==null?!!Mx(a.a,null):_x(a.b,b)}
function eu(a){return String.fromCharCode.apply(null,a)}
function Is(a){return /\d/.test(String.fromCharCode(a))}
function xj(){vj();return sd(kd(Qe,1),$A,65,0,[uj,tj])}
function hx(a,b){Py(b);return ix(a,od(me,UB,8,b,15,1),b)}
function Nl(a,b,c){Jl();this.wb(a);this.xb(b);this.yb(c)}
function Xp(a){Tp(this);this.a=a.a;this.c=a.c;this.b=a.b}
function PA(a,b,c,d){this.a=a;this.b=b;this.c=c;this.d=d}
function uq(a,b,c,d){this.c=a;this.b=b;this.d=c;this.a=d}
function Kv(a,b){Gv.call(this,a);Wy(b,a.size());this.a=b}
function ae(a){$y(a==null||je(a)&&!(a.ac===gi));return a}
function gl(a,b){var c,d;c=sk(a.c,b).b;d=hl(a,c);return d}
function Ub(a,b){var c;c=Ns(a.$b);return b==null?c:c+': '+b}
function av(a,b){return b==null?Lu(Mx(a.a,null)):ay(a.b,b)}
function Fh(a,b){return Hh(Id(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b))}
function Rh(a,b){return Hh(Nd(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b))}
function Fu(a,b){return b===a?'(this Map)':b==null?jB:fi(b)}
function Vt(a,b){return Ut(a.toLowerCase(),b.toLowerCase())}
function Ep(a){return Xt(cB,typeof a)||a instanceof Number}
function le(a){return Math.max(Math.min(a,eB),-2147483648)|0}
function el(){el=ci;dl=sd(kd(me,1),UB,8,15,[65,84,38,84])}
function hi(){$wnd.setTimeout(WA(ej));Ai();new yz}
function vc(a){pc();$wnd.setTimeout(function(){throw a},0)}
function Rx(a){this.e=a;this.b=this.e.a.entries();this.a=[]}
function Mo(a){a.d=a.b=0;Vq(a.a);Eo(a,null,false);Vq(a.g)}
function dv(a,b,c){return b==null?Nx(a.a,null,c):by(a.b,b,c)}
function Xs(a){if(a.Pb()){return null}var b=a.j;return _h[b]}
function di(a){function b(){}
;b.prototype=a||{};return new b}
function hk(a,b){var c;c=a.d.nb(b);a.c=Vh(Eh(a.c,c));return c}
function Ko(a,b,c){var d;Mo(a);d=new Ao;zo(d,b,c,a);return d}
function Ew(a,b,c){var d;Xy(b,c,a.a.length);d=c-b;By(a.a,b,d)}
function fA(a,b){var c;c=$d(yw(a.i,b),32);return c.b?c.d:null}
function Vs(a,b){var c=a.a=a.a||[];return c[b]||(c[b]=a.Kb(b))}
function Zy(a,b){if(a>b||a<0){throw Dh(new pu(nB+a+oB+b))}}
function ej(){var a;a=hj();if(!Xt(PB,a)){throw Dh(new gj(a))}}
function Rc(){Rc=ci;var a,b;b=!Wc();a=new cd;Qc=b?new Xc:a}
function kz(){if(fz==256){ez=gz;gz={};fz=0}++fz}
function Pz(a){Lz();a.xmin=a.xmax=a.ymin=a.ymax=0;Um(a,a,Iz)}
function is(){bs();this.a=new ns;hs(this,od(me,UB,8,0,15,1))}
function Ej(a){a.c=od(Se,WB,19,300,0,1);a.g=od(me,UB,8,1,15,1)}
function Uz(){Uz=ci;new qx(sd(kd(vg,1),$A,2,6,['loading',FB]))}
function ni(){ni=ci;new RegExp('%5B','g');new RegExp('%5D','g')}
function Ti(a){var b;b=a.currentTarget||$wnd;b[NB]=a.type;Si(a)}
function Gi(a){var b=a.__listener;return !ge(b)&&de(b,70)?b:null}
function sk(a,b){return b<a.d.b.a.length?$d(Rq(a.d,b),21):null}
function cx(a){Sy(a.a<a.c.a.length);a.b=a.a++;return a.c.a[a.b]}
function nn(a,b){if(a.b[b]==0)return null;a.a.a=b*16;return a.a}
function xr(a,b){for(;;b++){if(!Js(a.charCodeAt(b))){return b}}}
function Vi(a,b){for(var c in a){a.hasOwnProperty(c)&&b(c,a[c])}}
function Mx(a,b){var c;return Kx(b,Lx(a,b==null?0:(c=Ab(b),c|0)))}
function Ss(a,b,c,d){var e;e=Qs(a,b);bt(c,e);e.f=d?8:0;return e}
function Cq(a){switch(a){case 1:case 2:case 3:case 4:return;}}
function Fq(a){switch(a){case -4:case -3:case -2:case -1:return;}}
function Wb(b){if(!('stack' in b)){try{throw b}catch(a){}}return b}
function rv(a,b){if(de(b,34)){return Du(a.a,$d(b,34))}return false}
function fs(a,b){var c;c=Yj(b);if(c){return gs(a,c)}ds(a,b);return a}
function tc(a,b,c){var d;d=rc();try{return qc(a,b,c)}finally{uc(d)}}
function wy(a,b,c,d){Array.prototype.splice.apply(a,[b,c].concat(d))}
function fj(a,b){Pb(this);this.e=b;this.f=a;Qb(this);this.bb()}
function Mv(a,b,c){Xy(b,c,a.size());this.c=a;this.a=b;this.b=c-b}
function gc(a){ec();cc.call(this,a);this.a='';this.b=a;this.a=''}
function ur(a){var b;sr.call(this,'toplevel');b=new Kp;wr(this,a,b)}
function Hj(a){var b,c;c=1;while(c<KB){b=jq(a.i);c=c<<1|b}return c-KB}
function vd(a){var b,c,d;b=a&wB;c=a>>22&wB;d=a<0?xB:0;return xd(b,c,d)}
function au(a,b,c){var d;c=hu(c);d=new RegExp(b);return a.replace(d,c)}
function fv(a){var b;a.a=new Px(a);a.b=new dy(a);b=a[HC]|0;a[HC]=b+1}
function Fv(a){Yy(a.b!=-1);a.c.removeAtIndex(a.b);a.a=a.b;a.b=-1}
function Ll(a,b){a.d.e[a.c+a.a]=b;a.d.e[a.c+a.e]=b;a.d.e[a.c+a.b]=b}
function Fw(a,b,c){var d;d=(Ty(b,a.a.length),a.a[b]);a.a[b]=c;return d}
function Bw(a,b,c){for(;c>=0;--c){if(qy(b,a.a[c])){return c}}return -1}
function sy(a,b){!a.a?(a.a=new ou(a.d)):mu(a.a,a.b);lu(a.a,b);return a}
function Qb(a){if(a.j){a.backingJsObject!==gB&&a.bb();a.g=null}return a}
function oo(a,b,c){var d;Oj(a.e,b[c]);d=kq(a.d,a.e);b[c]=a.e.a;return d}
function Cw(a,b){var c;c=(Ty(b,a.a.length),a.a[b]);By(a.a,b,1);return c}
function ev(a,b){return ie(b)?b==null?Ox(a.a,null):cy(a.b,b):Ox(a.a,b)}
function mq(a,b){return Nh(Fh(ZB,b),65280)?a.j[255&b>>8]:a.j[255&b]+8}
function Zm(a){Ym.call(this,a.xmin,a.ymin,a.xmax-a.xmin,a.ymax-a.ymin)}
function Ym(a,b,c,d){this.xmin=a;this.ymin=b;this.xmax=a+c;this.ymax=b+d}
function tl(a,b,c,d){this.n=new Jx;this.j=d;this.o=a;this.g=b;this.d=c}
function fy(a){this.d=a;this.b=this.d.a.entries();this.a=this.b.next()}
function ty(a,b){this.b=', ';this.d=a;this.e=b;this.c=this.d+(''+this.e)}
function gn(){bn();this.a=od(me,UB,8,4,15,1);this.b=od(me,UB,8,4,15,1)}
function sc(b){pc();return function(){return tc(b,this,arguments);var a}}
function lc(){if(Date.now){return Date.now()}return (new Date).getTime()}
function Cy(){if(Date.now){return Date.now()}return (new Date).getTime()}
function Py(a){if(a<0){throw Dh(new Et('Negative array size: '+a))}}
function Wy(a,b){if(a<0||a>b){throw Dh(new ws('Index: '+a+', Size: '+b))}}
function Ty(a,b){if(a<0||a>=b){throw Dh(new ws('Index: '+a+', Size: '+b))}}
function en(a){var b;if(!cn(a)){throw Dh(new py)}b=a.d;a.d=null;return b}
function ak(a){var b;if(a.c>=a.a)return -1;b=a.d.kb();b>=0&&++a.c;return b}
function dA(a,b){var c;c=$d(av(a.d,b),49);!c&&dv(a.d,b,c=new qA);return c}
function ix(a,b,c){var d,e;e=a.length;d=c<e?c:e;yy(a,0,b,0,d,true);return b}
function mx(a,b,c,d){var e;d=(Ax(),!d?zx:d);e=a.slice(b,c);nx(e,a,b,c,-b,d)}
function od(a,b,c,d,e,f){var g;g=pd(e,d);e!=10&&sd(kd(a,f),b,c,e,g);return g}
function Cr(a){switch(a){case 1:case 2:case 3:case 4:case 5:case 6:return;}}
function Kw(a){tw(this);Ny(a>=0,'Initial capacity must not be negative')}
function ij(c,a){var b=c;c.onreadystatechange=WA(function(){a.hb(b)})}
function $i(a,b){!!a.a&&((xi(),a.d)[NB]='',undefined);hd((xi(),a.d),b.a)}
function Sz(a,b){Lz();if(!Pm(Iz,a)||Hz!=b){Om(Iz);Um(Iz,Iz,a);Hz=b;Nz()}}
function uc(a){a&&Bc((zc(),yc));--mc;if(a){if(oc!=-1){wc(oc);oc=-1}}}
function Xj(a,b){var c,d;d=new jk(a);c=d.c+b;Nh(c,d.a)&&(d.a=Vh(c));return d}
function Vp(a,b){a.a=eA(Sp,b,null);!a.a&&eA(Sp,b,new bq(a,b));a.c=0;return a}
function fk(a,b){var c,d;c=ek(Xj(a,b));d=a.d.nb(b);a.c=Vh(Eh(a.c,d));return c}
function xl(a,b){var c;c=b>=0?a.e[b*ql+3]:0;return (c*(a.a-1)+(a.a-2))/255|0}
function ck(a){var b,c;c=ak(a);if(c<0){return c}b=ak(a);return b>=0?c<<8|b:-1}
function dk(a){var b,c;c=ck(a);if(c<0){return c}b=ak(a);return b>=0?c<<8|b:-1}
function Si(a){var b;b=Ui(a);if(!b){return}yi(a,b.nodeType!=1?null:b,Gi(b))}
function Qh(a){var b;if(Mh(a)){b=0-a;if(!isNaN(b)){return b}}return Hh(Md(a))}
function zw(a,b,c){for(;c<a.a.length;++c){if(qy(b,a.a[c])){return c}}return -1}
function jy(a){if(a.a.d!=a.c){return ay(a.a,a.b.value[0])}return a.b.value[1]}
function AA(a,b){if(b.b==12)return $d(_u(a.g,b),51);return $d(_u(a.a,b),51)}
function tA(a,b){!b&&(b=new is);a.g=b;b.b.length>0&&(a.i.n=true);xz(a.e,b)}
function rl(a,b,c){a.e=ti(b*c*ql);a.f=a.e.buffer;a.dataWidth=b;a.dataHeight=c}
function Bl(a,b,c){(b>=a.border||b<a.b)&&(a.e[b*ql+3]=255*c/(a.a-1)|0,undefined)}
function td(a,b){ld(b)!=10&&sd(yb(b),b._b,b.__elementTypeId$,ld(b),a);return a}
function rA(a,b){var c;c=a.f-b.f;c==0&&(c=At(a.i.g-b.e)-At(a.i.g-a.e));return -c}
function cs(a){var b,c;c=ti(a.length);for(b=0;b<a.length;b++){c[b]=a[b]}return c}
function Lq(a){var b;for(b=0;++b<vq.length;){if(Xt(vq[b],a)){return b}}return 0}
function Mq(a){var b;for(b=0;++b<wq.length;){if(Xt(wq[b],a)){return b}}return 0}
function Yj(a){var b;b=null;(a.b==null||a.b.length!=4)&&(b=dn(new gn,a));return b}
function Nz(){var a,b;for(b=new dx(Kz);b.a<b.c.a.length;){a=$d(cx(b),106);a.Qb()}}
function Ac(a){var b,c;if(a.b){c=null;do{b=a.b;a.b=null;c=Kc(b,c)}while(a.b);a.b=c}}
function Bc(a){var b,c;if(a.c){c=null;do{b=a.c;a.c=null;c=Kc(b,c)}while(a.c);a.c=c}}
function zv(a){var b;Fx(a.e,a);Sy(a.b);a.c=a.a;b=$d(a.a.Sb(),34);a.b=yv(a);return b}
function bt(a,b){var c;if(!a){return}b.j=a;var d=Xs(b);if(!d){_h[a]=[b];return}d.$b=b}
function Or(a,b,c,d){return (b[0]-a[0])*(d[1]-c[1])-(b[1]-a[1])*(d[0]-c[0])==0}
function jn(a){return a.length>0?(Wt(a,0,a.length,(Gy(),Fy))+qC).substr(0,4):qC}
function ld(a){return a.__elementTypeCategory$==null?10:a.__elementTypeCategory$}
function Yi(){Wi();Xi(this,new _i(this));(xi(),this.d).className='gwt-Image'}
function Vd(){Vd=ci;Rd=xd(wB,wB,524287);Sd=xd(0,0,yB);Td=vd(1);vd(2);Ud=vd(0)}
function Gy(){Gy=ci;Fy=new Ky;Ey=new Iy('ISO-LATIN-1');Dy=new Iy('ISO-8859-1')}
function vj(){vj=ci;uj=new wj('Default',0,'');tj=new wj('ArrayBuffer',1,'arraybuffer')}
function tz(c){var b=c;addEventListener('message',function(a){b.Zb(a.data)},false)}
function Ch(a){var b;if(de(a,7)){return a}b=a&&a[fB];if(!b){b=new gc(a);Sc(b)}return b}
function _d(a){var b;$y(a==null||Array.isArray(a)&&(b=ld(a),!(b>=14&&b<=16)));return a}
function Av(a){var b;Yy(!!a.c);Fx(a.e,a);a.c.Tb();a.c=null;a.b=yv(a);b=a.e[HC];a[HC]=b}
function Uc(a){var b=/function(?:\s+([\w$]+))?\s*\(/;var c=b.exec(a);return c&&c[1]||YA}
function Ui(a){var b;b=a.currentTarget||$wnd;while(!!b&&!Gi(b)){b=b.parentNode}return b}
function vt(a){var b,c;if(a==0){return 32}else{c=0;for(b=1;(b&a)==0;b<<=1){++c}return c}}
function Ij(a,b){var c;a.i=nq(new qq,b);for(c=0;c<a.c.length;){a.c[c++]=new Qj}return a}
function yk(a,b){var c;c=$t(a,fu(47));this.b=c!=-1?a.substr(c):a;this.a=(b&63)<<24>>24}
function bm(a,b){Jl();Ml.call(this);this.d=a;this.c=b*ql;this.a=a.d;this.b=a.g;this.e=a.o}
function pn(){mn();this.c=new Int16Array(EB);this.b=new Int8Array(64);this.a=new rn(this)}
function FA(a,b,c){if(b.b==12){cv(a.g,new QA(b),c)}else{GA(a);cv(a.a,new QA(b),c)}}
function wz(a,b){if(b==a.b||b!=null&&Xt(b,a.b))return;a.b=b;uz(new Ez('status-set',b))}
function Cb(a,b){var c=a.parentNode;if(!c){return}c.insertBefore(b,a);c.removeChild(a)}
function rs(a,b){var c;c=a.a.length;b<c?(a.a=cu(a.a,b)):b>c&&(a.a+=iu(od(ne,UB,8,b-c,15,1)))}
function Dq(a,b){b=du(b);if(b.length==0){a.a=null}else if(!Xt(b,a.a)){a.a=b;zq(new ur(a.a))}}
function Cc(a){var b;if(a.a){b=a.a;a.a=null;!a.f&&(a.f=[]);Kc(b,a.f)}!!a.f&&(a.f=Fc(a.f))}
function Hh(a){var b;b=a.h;if(b==0){return a.l+a.m*AB}if(b==xB){return a.l+a.m*AB-zB}return a}
function Jr(a,b,c){var d;d=sd(kd(oe,1),YB,8,15,[b,c]);Pq(a.d,d);return a.d.b.a.length}
function yi(a,b,c){xi();var d;d=vi;vi=a;b==wi&&Ei(a.type)==8192&&(wi=null);c.$(a);vi=d}
function $h(a,b){typeof window===XA&&typeof window['$gwt']===XA&&(window['$gwt'][a]=b)}
function Xh(){Yh();var a=Wh;for(var b=0;b<arguments.length;b++){a.push(arguments[b])}}
function Dj(){Dj=ci;var a;Cj=od(me,UB,8,256,15,1);for(a=0;a<Cj.length;a++){Cj[a]=a<<24>>24}}
function Yl(a,b,c,d){a.d.e[a.c+a.a]=b<<24>>24;a.d.e[a.c+a.b]=c<<24>>24;a.d.e[a.c+a.e]=d<<24>>24}
function Hd(a,b){var c,d,e;c=a.l+b.l;d=a.m+b.m+(c>>22);e=a.h+b.h+(d>>22);return xd(c&wB,d&wB,e&xB)}
function Qd(a,b){var c,d,e;c=a.l-b.l;d=a.m-b.m+(c>>22);e=a.h-b.h+(d>>22);return xd(c&wB,d&wB,e&xB)}
function Uh(a){var b,c,d,e;e=a;d=0;if(e<0){e+=zB;d=xB}c=le(e/AB);b=le(e-c*AB);return xd(b,c,d)}
function xx(a){var b,c,d;d=0;for(c=a.Eb();c.Rb();){b=c.Sb();d=d+(b!=null?Ab(b):0);d=d|0}return d}
function gp(a,b){var c,d;for(c=0,d=a.size();c<d;++c){if(qy(b,a.getAtIndex(c))){return c}}return -1}
function Gj(a,b,c){var d,e,f;f=1;e=1<<c;--b;while(f<e){d=kq(a.i,a.c[b+f]);f=f<<1|d}return f-e}
function Dw(a,b){var c;c=zw(a,b,0);if(c==-1){return false}Ty(c,a.a.length);By(a.a,c,1);return true}
function cy(a,b){var c;c=a.a.get(b);if(c===undefined){++a.d}else{a.a[JC](b);--a.c;Gx(a.b)}return c}
function Br(a,b){var c;c=a.Ib();c.xmin=b.xmin;c.xmax=b.xmax;c.ymin=b.ymin;c.ymax=b.ymax;return a}
function yx(a){var b,c,d;d=1;for(c=a.Eb();c.Rb();){b=c.Sb();d=31*d+(b!=null?Ab(b):0);d=d|0}return d}
function Kx(a,b){var c,d,e;for(d=0,e=b.length;d<e;++d){c=b[d];if(Ix(a,c.Ub())){return c}}return null}
function ri(a,b,c){var d,e;if(b){a.set(b,c);return}e=null.length;for(d=0;d<e;++d){a[c++]=null[d]}}
function xp(a){var b,c;vp(this,null);for(c=new dx(a);c.a<c.c.a.length;){b=cx(c);this.array.push(b)}}
function Ec(a){if(!a.i){a.i=true;!a.e&&(a.e=new Mc(a));Lc(a.e,1);!a.g&&(a.g=new Oc(a));Lc(a.g,50)}}
function yv(a){if(a.a.Rb()){return true}if(a.a!=a.d){return false}a.a=new Rx(a.e.a);return a.a.Rb()}
function Jh(a){if(BB<a&&a<zB){return a<0?$wnd.Math.ceil(a):$wnd.Math.floor(a)}return Hh(Kd(a))}
function Gs(a){if(Xt(typeof a,dB)){return true}return a!=null&&a.$implements__java_lang_CharSequence}
function oz(a){if(gA(a.f)==0)return;if(!a.c){Gc((zc(),yc),a);a.d=(ru(),Jh(Cy()));a.b=false}a.c=true}
function ht(a,b){if(a<b){return -1}if(a>b){return 1}if(a==b){return 0}return isNaN(a)?isNaN(b)?0:1:-1}
function Js(a){switch(a){case 10:case 9:case 12:case 13:case 32:return true;default:return false;}}
function kq(a,b){var c,d;c=255&b.a;d=a.a+a.o[c];if(Oh(d,a.i)){a.a=d;return c&1}else{return hq(a,b,d)}}
function Md(a){var b,c,d;b=~a.l+1&wB;c=~a.m+(b==0?1:0)&wB;d=~a.h+(b==0&&c==0?1:0)&xB;return xd(b,c,d)}
function Dd(a){var b,c,d;b=~a.l+1&wB;c=~a.m+(b==0?1:0)&wB;d=~a.h+(b==0&&c==0?1:0)&xB;a.l=b;a.m=c;a.h=d}
function Ed(a){var b,c;c=ut(a.h);if(c==32){b=ut(a.m);return b==32?ut(a.l)+32:b+20-10}else{return c-12}}
function eA(a,b,c){var d;d=dA(a,b);d.c||aA(a,b);!d.a&&!!c&&vw(d.d,c);Dw(a.e,d);uw(a.e,0,d);return d.a}
function ao(a,b,c){var d;d=a.J.b.a.length;Pq(a.J,new nt(b));lo(a,b,new nt(d));Pq(a.K,wl(c.a));return d}
function lo(a,b,c){var d;d=a.T.b.a.length;if(d<=b){while(d++<b){Pq(a.T,Zn)}Pq(a.T,c)}else{Uq(a.T,c,b)}}
function Hk(a,b,c,d){var e;if(c){c.ob(d);e=Zk(a,b,c);if(!!e&&e!=c){throw Dh(new lt('Duplicate '+b))}}}
function gu(){try{return yu('UTF-8')}catch(a){a=Ch(a);if(de(a,74)){throw Dh(new qs)}else throw Dh(a)}}
function Lc(b,c){zc();function d(){var a=WA(Ic)(b);a&&$wnd.setTimeout(d,c)}
$wnd.setTimeout(d,c)}
function Gb(a,b){a.b&&(xi(),a.d.__listener=null,undefined);!!a.d&&Cb(a.d,b);a.d=b;a.b&&(xi(),Hi(a.d,a))}
function cc(a){Pb(this);Qb(this);this.backingJsObject=a;a!=null&&bz(a,fB,this);this.f=a==null?jB:fi(a)}
function Os(){++Ls;this.k=null;this.i=null;this.g=null;this.d=null;this.b=null;this.j=null;this.a=null}
function Bv(a){var b;this.e=a;this.d=new fy(this.e.b);this.a=this.d;this.b=yv(this);b=a[HC];this[HC]=b}
function Bz(){this.page=(Lz(),Lz(),Gz);this.tileRange=new Xm;Pz(this.tileRange);this.subsample=(null,Hz)}
function jA(a){var b;b=(Lz(),Lz(),Gz);if(!a.i){a.g=b;return}if(a.g!=b){a.g=b;kA(a);Gw(a.j,null)}oz(a.a.a)}
function Ad(a,b,c,d,e){var f;f=Pd(a,b);c&&Dd(f);if(e){a=Cd(a,b);d?(ud=Md(a)):(ud=xd(a.l,a.m,a.h))}return f}
function _k(a,b,c,d){var e;for(e=1;e<16;e++){if(((a+e-1)/e|0)==c&&((b+e-1)/e|0)==d){return e}}return 16}
function $u(a,b){var c,d;for(d=b.Eb();d.Rb();){c=$d(d.Sb(),34);if(Ix(a,c.Vb())){return true}}return false}
function To(a,b){var c,d;Uy(b);for(d=b.Eb();d.Rb();){c=d.Sb();if(!a.contains(c)){return false}}return true}
function xw(a,b){var c,d;c=b.toArray();d=c.length;if(d==0){return false}Ay(a.a,a.a.length,c);return true}
function jx(a){var b,c,d,e;e=1;for(c=0,d=a.length;c<d;++c){b=a[c];e=31*e+(b!=null?Ab(b):0);e=e|0}return e}
function Sb(a){var b,c,d,e;for(b=(a.g==null&&(a.g=(Rc(),e=Qc.fb(a),Tc(e))),a.g),c=0,d=b.length;c<d;++c);}
function oq(a,b){var c;for(c=0;c<256;c++){a.o[c]=b[c].c;a.n[c]=b[c].b;Oj(a.q[c],b[c].d);Oj(a.g[c],b[c].a)}}
function eo(a,b){var c;Dl(b,3);c=b.k-1;so(a,b,b.i,c,(c+2)*b.c+b.border,(c+1)*b.c+b.border,c*b.c+b.border)}
function bk(a,b){var c;if(b.length==0)return 0;c=a.d.lb(b);c=Ct(c,a.a-a.c);if(c>0){a.c+=c;return c}return -1}
function Fb(a,b){var c;switch(xi(),Ei(b.type)){case 16:case 32:c=b.relatedTarget;if(!!c&&gd(a.d,c)){return}}}
function Gh(a,b){var c;if(Mh(a)&&Mh(b)){c=a-b;if(!isNaN(c)){return c}}return Jd(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b)}
function Zb(a){var b;if(a!=null){b=$d(a[fB],7);if(b){return b}}return he(a,$wnd.TypeError)?new Gt(a):new cc(a)}
function Yz(a){var b;b=$d(yw(a.i,a.g),32);wz(a.a,b.b?null:'loading');if(b.b)return false;Wz(a,0);return Zz(a,b)}
function Co(a){var b,c,d;d=0;for(c=new dx(a.g.b);c.a<c.c.a.length;){b=$d(cx(c),52);d+=b.a.e.byteLength}return d}
function sd(a,b,c,d,e){e.$b=a;e._b=b;e.ac=gi;e.__elementTypeId$=c;e.__elementTypeCategory$=d;return e}
function Mm(a,b,c,d){Gm();this.n=a;this.k=b;this.o=null;this.c=null;this.b=c;this.a=d;this.o=null;this.c=null}
function cm(a,b,c){Jl();Ml.call(this);this.d=a;this.c=(b*a.i+a.border+c)*ql;this.a=a.d;this.b=a.g;this.e=a.o}
function Uj(a,b,c){if(0>b.length||c<0||c-b.length>0){throw Dh(new vs)}Sj(a,a.b+c);su(b,0,a.a,a.b,c);a.b+=c}
function Oy(a,b){if(0>a){throw Dh(new jt('fromIndex: 0 > toIndex: '+a))}if(a>b){throw Dh(new ys(nB+a+oB+b))}}
function Cl(a,b){if(b<2||b>256){throw Dh(new jt('(GBitmap::set_grays) Illegal number of gray levels'))}a.a=b}
function yb(a){return ie(a)?vg:fe(a)?cg:ee(a)?_f:ce(a)?a.$b:qd(a)?a.$b:a.$b||Array.isArray(a)&&kd(ve,1)||ve}
function gj(a){fj.call(this,QB+a+RB+SB==null?jB:fi(QB+a+RB+SB),de(QB+a+RB+SB,7)?$d(QB+a+RB+SB,7):null)}
function Eh(a,b){var c;if(Mh(a)&&Mh(b)){c=a+b;if(BB<c&&c<zB){return c}}return Hh(Hd(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b))}
function Ph(a,b){var c;if(Mh(a)&&Mh(b)){c=a*b;if(BB<c&&c<zB){return c}}return Hh(Ld(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b))}
function Th(a,b){var c;if(Mh(a)&&Mh(b)){c=a-b;if(BB<c&&c<zB){return c}}return Hh(Qd(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b))}
function pm(a,b){var c,d;if(b>0.999&&b<1.001){return}c=ym(b);for(d=0;d<a.e.length;d++){a.e[d]=c[a.e[d]]<<24>>24}}
function Dl(a,b){var c;if(a.border<b){if(a.e){c=zl(new Gl,a,b);El(a,c.c);a.e=c.e;c.e=null}a.border=b;a.b=sl(a,a.k)}}
function Do(a,b){var c;if(b>=a.f){c=$d(Qq(a.g,b-a.f),52)}else if(a.e){c=Do(a.e,b)}else{throw Dh(new lt(uC))}return c}
function Uo(a,b){var c,d,e;Uy(b);c=false;for(d=a.Eb();d.Rb();){e=d.Sb();if(b.contains(e)){d.Tb();c=true}}return c}
function ju(a,b){var c,d,e;Zy(b,a.length);e='';for(d=0;d<b;){c=d+10000<b?d+10000:b;e+=eu(a.slice(d,c));d=c}return e}
function ww(a,b,c){var d,e;Wy(b,a.a.length);d=c.toArray();e=d.length;if(e==0){return false}Ay(a.a,b,d);return true}
function by(a,b,c){var d;d=a.a.get(b);a.a.set(b,c===undefined?null:c);if(d===undefined){++a.c;Gx(a.b)}else{++a.d}return d}
function qo(a,b,c){var d,e;d=bo(a,0,tC,a.i,0);e=bo(a,0,tC,a.j,0);if(d!=(mB&d)||e!=(mB&e)){throw Dh(new lt(uC))}yl(b,e,d,c)}
function Tj(a,b){var c,d;d=a.a.length;c=d<<1;c-b<0&&(c=b);if(c<0){if(b<0)throw Dh(new lt('overflow!'));c=eB}a.a=hx(a.a,c)}
function xt(a){var b,c;if(a>-129&&a<128){b=a+128;c=(zt(),yt)[b];!c&&(c=yt[b]=new nt(a));return c}return new nt(a)}
function Lb(){Ib();var a;!Jb&&(Jb=new Mb);a=$doc.createElement('canvas');if(!a.getContext){return null}return new Kb(a)}
function fi(a){if(Array.isArray(a)&&a.ac===gi){return Ns(yb(a))+'@'+(Ab(a)>>>0).toString(16)}return a.toString()}
function ai(){_h={};!Array.isArray&&(Array.isArray=function(a){return Object.prototype.toString.call(a)==='[object Array]'})}
function fq(){fq=ci;var a,b;eq=od(me,UB,8,256,15,1);for(a=0;a<256;a++){eq[a]=0;for(b=a;(b&128)>0;b<<=1){++eq[a]}}}
function Bm(a){var b,c;c=nm[a];if(c==null){c=od(oe,YB,8,a,15,1);for(b=0;b<a;b++){c[b]=lB-((b<<16)/a|0)}nm[a]=c}return c}
function Jo(a,b){var c;if(b.c>=a.f+a.g.b.a.length){throw Dh(new jt('JB2Image bad shape'))}c=a.a.b.a.length;Pq(a.a,b);return c}
function Gr(a,b,c,d,e){var f;a.a=b<d;a.b=c<e;f=a.e;f.xmin=a.a?b:d;f.xmax=(a.a?d:b)+1;f.ymin=a.b?c:e;f.ymax=(a.b?e:c)+1;return a}
function lx(a,b,c,d,e,f,g,h){var i;i=c;while(f<g){i>=d||b<c&&h.Xb(a[b],a[i])<=0?rd(e,f++,a[b++]):rd(e,f++,a[i++])}}
function kx(a,b,c,d){var e,f,g;for(e=b+1;e<c;++e){for(f=e;f>b&&d.Xb(a[f-1],a[f])>0;--f){g=a[f];rd(a,f,a[f-1]);rd(a,f-1,g)}}}
function Sr(a,b,c,d,e,f){var g,h;g=(a-c)*(e-c)+(b-d)*(f-d);h=(a-e)*(e-c)+(b-f)*(f-d);return (g<0?-1:g>0?1:0)*(h<0?-1:h>0?1:0)<=0}
function bn(){bn=ci;_m=sd(kd(vg,1),$A,2,6,['FORM','LIST','PROP','CAT ']);an=sd(kd(vg,1),$A,2,6,['FOR','LIS','CAT'])}
function Fs(a,b){Bs();return ie(a)?Ut(a,be(b)):fe(a)?ft(a,($y(b==null||fe(b)),b)):ee(a)?Cs(a,($y(b==null||ee(b)),b)):a.ib(b)}
function zd(a,b){if(a.h==yB&&a.m==0&&a.l==0){b&&(ud=xd(0,0,0));return wd((Vd(),Td))}b&&(ud=xd(a.l,a.m,a.h));return xd(0,0,0)}
function yl(a,b,c,d){var e;a.e=null;a.a=2;Fl(a,b);a.i=c;a.border=d;El(a,a.i+a.border);e=sl(a,a.k)*ql;e>0&&rl(a,a.c,a.k+3);return a}
function hl(a,b){var c,d,e;d=null;c=jl(a,b);if(!de(c.d,22)||!!$d(c.d,22).a){e=a.c.c;e!=null&&dq(e,b);d=new pl(a);Ok(d,c)}return d}
function So(a,b,c){var d,e;for(e=a.Eb();e.Rb();){d=e.Sb();if(ke(b)===ke(d)||b!=null&&wb(b,d)){c&&e.Tb();return true}}return false}
function Kr(a,b,c,d){var e;a.c=false;e=0;for(;e<d-1;e++){Jr(a,b[e],c[e])}(b[e]!==b[0]||c[e]!==c[0])&&Jr(a,b[e],c[e]);Mr(a);return a}
function jz(a){hz();var b,c,d;c=':'+a;d=gz[c];if(!(d===undefined)){return d}d=ez[c];b=d===undefined?iz(a):d;kz();gz[c]=b;return b}
function et(a){var b;b=typeof a;if(Xt(b,bB)||Xt(b,cB)||Xt(b,dB)){return true}return a!=null&&a.$implements__java_lang_Comparable}
function _i(a){Gb(a,$doc.createElement('img'));Ci((xi(),a.d));a.c==-1?zi(a.d,133398655|(a.d.__eventBits||0)):(a.c|=133398655)}
function aA(a,b){var c;c=kj();c.open('GET',b,true);jj(c,(vj(),tj).a);ij(c,new oA(a,b));c.send(null);$d(av(a.d,b),49).c=true;++a.c}
function lA(a,b){var c;this.d=new Jx;this.e=new Jw;this.a=a;Mz(new mA(this));Sp=this;c=eA(this,b,new wA(this,b));!!c&&hA(this,b)}
function po(a,b,c){var d,e;if(!a.v){throw Dh(new lt(rC))}d=bo(a,1,a.w,a.f,0);e=bo(a,1,a.A,a.g,0);b.a=e-c<<16>>16;b.b=d-1<<16>>16}
function Bo(a,b){var c;if(b.b>=a.f+a.g.b.a.length){throw Dh(new jt('JB2Image bad parent shape'))}c=a.f+a.g.b.a.length;Pq(a.g,b);return c}
function gs(a,b){var c,d;if(b){while(cn(b)){c=en(b);d=c.b;Xt(d.substr(0,5),'FORM:')?gs(a,Yj(c)):(Xt(aC,d)||Xt(bC,d))&&ds(a,c)}}return a}
function cn(b){var c;if(!b.d){try{b.d=fn(b)}catch(a){a=Ch(a);if(de(a,6)){c=a;Rb(c,(Ek(),Dk),'');b.d=null}else throw Dh(a)}}return !!b.d}
function rc(){var a;if(mc!=0){a=lc();if(a-nc>2000){nc=a;oc=$wnd.setTimeout(xc,10)}}if(mc++==0){Ac((zc(),yc));return true}return false}
function yo(a){de(a.a,41)?(a.b=go(a,a.b,$d(a.a,41))):(a.b=fo(a,a.b,a.a));if(a.b==11){if(!a.v){throw Dh(new lt(rC))}return true}return false}
function om(){om=ci;var a;lm=od(oe,YB,8,256,15,1);km=od(oe,YB,8,256,15,1);nm=od(oe,$A,12,256,0,2);for(a=0;a<lm.length;a++){lm[a]=a}}
function Xy(a,b,c){if(a<0||b>c){throw Dh(new ws(pB+a+', toIndex: '+b+', size: '+c))}if(a>b){throw Dh(new jt(pB+a+' > toIndex: '+b))}}
function Wc(){if(Error.stackTraceLimit>0){$wnd.Error.stackTraceLimit=Error.stackTraceLimit=64;return true}return 'stack' in new Error}
function at(a,b){var c=0;while(!b[c]||b[c]==''){c++}var d=b[c++];for(;c<b.length;c++){if(!b[c]||b[c]==''){continue}d+=a+b[c]}return d}
function jo(a,b){var c,d,e,f;e=b.f;Vq(a.T);Vq(a.J);Vq(a.K);for(c=0;c<e;c++){f=new nt(c);Pq(a.T,f);Pq(a.J,f);d=Do(b,c);Pq(a.K,wl(d.a))}}
function cA(a,b){var c,d,e;c=$d(av(a.d,b),49);if(!c)return;for(e=new dx(c.d);e.a<e.c.a.length;){d=$d(cx(e),168);d.qb()}c.d.a=od(pg,$A,1,0,5,1)}
function on(a,b,c){var d,e,f,g;e=0;oi(b,kn);for(f=0;f<c;f++){d=nn(a,f);if(!d){e+=16}else{for(g=0;g<16;){b[ln[e]]=d.b.c[g+d.a];++g;++e}}}}
function Hn(a,b,c,d,e,f,g){var h,i,j;for(j=f>>1;j>=g;j>>=1){for(i=0;i<c;i+=j){In(a,b,i,i+d*e,i,j*e)}for(h=0;h<d;h+=j){In(a,b,h*e,h*e+c,h*e,j)}}}
function iA(a,b,c){var d;if(c.readyState!=4)return;a.o=false;if(c.status==200){d=ui(c.response);bA(a,b,Zj(new ik,Up(new Wp,d)));oz(a.a.a)}}
function Iq(b,c){if(b!=null&&b.length>=2&&b.charCodeAt(0)==35){try{c=Vh(Gp(b.substr(1)))}catch(a){a=Ch(a);if(!de(a,7))throw Dh(a)}}return c}
function Ab(a){return ie(a)?jz(a):fe(a)?le((Uy(a),a)):ee(a)?(Uy(a),a)?1231:1237:ce(a)?a.Y():qd(a)?dz(a):!!a&&!!a.hashCode?a.hashCode():dz(a)}
function wb(a,b){return ie(a)?Xt(a,b):fe(a)?(Uy(a),a===b):ee(a)?(Uy(a),a===b):ce(a)?a.W(b):qd(a)?a===b:!!a&&!!a.equals?a.equals(b):ke(a)===ke(b)}
function zn(a){var b,c,d,e;c=0;for(e=0;e<16;e++){b=a.n[e]>>1;a.n[e]=b;b!=0&&(c=1)}for(d=0;d<10;d++){b=a.k[d]>>1;a.k[d]=b;b!=0&&(c=1)}return c}
function Iw(a,b){var c,d,e;e=a.a.length;b.length<e&&(b=(d=new Array(e),td(d,b)));for(c=0;c<e;++c){rd(b,c,a.a[c])}b.length>e&&rd(b,e,null);return b}
function px(a,b){var c,d,e;e=a.a.length;b.length<e&&(b=(d=new Array(e),td(d,b)));for(c=0;c<e;++c){rd(b,c,a.a[c])}b.length>e&&rd(b,e,null);return b}
function tr(a,b){var c,d;for(d=a.b.a.length;--d>=0;){c=(_q(d,a.b.a.length),yw(a.b,d));if(de(c,26)&&Xt(b,$d(c,26).a)){return $d(c,26)}}return null}
function Ks(a,b,c){My(a>=0&&a<=1114111);if(a>=lB){b[c++]=55296+(a-lB>>10&1023)&mB;b[c]=56320+(a-lB&1023)&mB;return 2}else{b[c]=a&mB;return 1}}
function xo(a,b,c,d){var e,f,g,h;e=bo(a,sC,tC,a.Q,0);g=bo(a,sC,tC,a.R,0);f=c+e;h=d+g;if(f!=(mB&f)||h!=(mB&h)){throw Dh(new lt(uC))}yl(b,h,f,4)}
function to(a){var b,c,d;d=bo(a,0,tC,a.q,0);b=od(me,UB,8,d,15,1);for(c=0;c<d;c++){b[c]=bo(a,0,255,a.p,0)<<24>>24}return Wt(b,0,b.length,(Gy(),Fy))}
function uo(a){var b,c;c=bo(a,0,tC,a.B,0);b=bo(a,0,tC,a.B,0);if(c!=0||b!=0){throw Dh(new lt('JB2Image bad dict 2'))}a.G=0;a.H=a.F=0;io(a,a.G);a.v=true}
function pq(a){for(;a.p<=24;a.p=a.p+8<<16>>16){a.r=-1;a.r=ak(a.k)<<16>>16;if(a.r==-1){a.r=255;if(--a.f<1){throw Dh(new os(gC))}}a.c=a.c<<8|a.r}}
function Zd(a,b){if(ie(a)){return !!Yd[b]}else if(a._b){return !!a._b[b]}else if(fe(a)){return !!Xd[b]}else if(ee(a)){return !!Wd[b]}return false}
function Gm(){Gm=ci;var a,b,c;Fm=md(Ah,[$A,lC],[169,8],15,[16,512],2);for(b=0;b<16;b++){a=Fm[b];for(c=-255;c<=255;c++){a[256+c]=c*b+8>>4<<16>>16}}}
function Fn(a,b,c){var d,e,f,g;f=c.ymin*a;for(d=c.ymin;d<c.ymax;d+=2){for(e=c.xmin;e<c.xmax;e+=2){g=b[f+e];b[f+e+a]=g;b[f+e+a+1]=g;b[f+e+1]=g}f+=a+a}}
function Zk(a,b,c){var d,e,f;f=!c?$d(ev(a.e,b),39):$d(cv(a.e,b,c),39);if(a.f){e=new cl(b,c);vw(a.f,e)}if(!!c&&c.pb()){d=a.q;a.q=new nt(d.a+1)}return f}
function En(a,b,c){var d;a.e=b;a.d=c;a.c=b+32-1&-32;a.a=c+32-1&-32;a.f=a.c*a.a/EB|0;a.b=od(lf,$A,81,a.f,0,1);for(d=0;d<a.f;d++){a.b[d]=new pn}return a}
function du(a){var b,c,d;c=a.length;d=0;while(d<c&&a.charCodeAt(d)<=32){++d}b=c;while(b>d&&a.charCodeAt(b-1)<=32){--b}return d>0||b<c?a.substr(d,b-d):a}
function _j(a){var b,c,d,e;if(de(a.d,22)){a.d.jb(0);b=a.d.kb();c=a.d.kb();e=a.d.kb();d=a.d.kb();a.d.mb();return b==65&&c==84&&e==38&&d==84}return false}
function Vm(a,b,c){if(!(a.xmin>=a.xmax||a.ymin>=a.ymax)){a.xmin+=b;a.xmax+=b;a.ymin+=c;a.ymax+=c;return true}a.xmin=a.ymin=a.xmax=a.ymax=0;return false}
function Rm(a,b,c){a.xmin-=b;a.xmax+=b;a.ymin-=c;a.ymax+=c;if(a.xmin>=a.xmax||a.ymin>=a.ymax){a.xmin=a.ymin=a.xmax=a.ymax=0;return false}else{return true}}
function Rb(a,b,c){var d,e,f,g,h;Sb(a);for(e=(a.i==null&&(a.i=od(wg,$A,7,0,0,1)),a.i),f=0,g=e.length;f<g;++f){d=e[f];Rb(d,b,'\t'+c)}h=a.e;!!h&&Rb(h,b,c)}
function hu(a){var b;b=0;while(0<=(b=a.indexOf('\\',b))){a.charCodeAt(b+1)==36?(a=a.substr(0,b)+'$'+bu(a,++b)):(a=a.substr(0,b)+(''+bu(a,++b)))}return a}
function Cd(a,b){var c,d,e;if(b<=22){c=a.l&(1<<b)-1;d=e=0}else if(b<=44){c=a.l;d=a.m&(1<<b-22)-1;e=0}else{c=a.l;d=a.m;e=a.h&(1<<b-44)-1}return xd(c,d,e)}
function jk(a){de(a.d,22)?(this.d=new Xp($d(a.d,22))):de(a.d,40)?(this.d=new Lj($d(a.d,40))):(this.d=new jk($d(a.d,17)));this.c=a.c;this.a=a.a;this.b=a.b}
function qq(){fq();var a;gq(this);this.j=od(me,UB,8,eq.length,15,1);su(eq,0,this.j,0,this.j.length);for(a=0;a<256;a++){this.q[a]=new Qj;this.g[a]=new Qj}}
function Oz(b,c){Lz();try{return xt(Fp($wnd?$wnd.DJVU_CONTEXT?$wnd.DJVU_CONTEXT[b]:null:null)).a}catch(a){a=Ch(a);if(de(a,29)){return c}else throw Dh(a)}}
function pd(a,b){var c=new Array(b);var d;switch(a){case 14:case 15:d=0;break;case 16:d=false;break;default:return c;}for(var e=0;e<b;++e){c[e]=d}return c}
function Gd(a,b){var c,d,e;e=a.h-b.h;if(e<0){return false}c=a.l-b.l;d=a.m-b.m+(c>>22);e+=d>>22;if(e<0){return false}a.l=c&wB;a.m=d&wB;a.h=e&xB;return true}
function Vz(a){var b,c,d;d=(Lz(),Oz(NC,KB));for(b=a.e.a.length-1;Kh(a.f,d)&&b>4;b--){c=$d(Cw(a.e,b),49);if(!c.a)continue;a.f=Th(a.f,c.b);c.a=null;c.c=false}}
function Ih(a,b){var c;if(Mh(a)&&Mh(b)){c=a/b;if(BB<c&&c<zB){return c<0?$wnd.Math.ceil(c):$wnd.Math.floor(c)}}return Hh(yd(Mh(a)?Uh(a):a,Mh(b)?Uh(b):b,false))}
function yy(a,b,c,d,e,f){var g,h,i;if(ke(a)===ke(c)){a=a.slice(b,b+e);b=0}for(h=b,i=b+e;h<i;){g=h+10000<i?h+10000:i;e=g-h;wy(c,d,f?e:0,a.slice(h,g));h=g;d+=e}}
function dq(a,b){if((new RegExp('^(\\w+://.*)$')).test(b))return b;if(Xt(b.substr(0,1),'/'))return au(a,'//([^/]+).*','//$1'+b);return au(a,'/[^/]+$','/')+(''+b)}
function Hs(a,b){if(b<2||b>36){return -1}if(a>=48&&a<48+(b<10?b:10)){return a-48}if(a>=97&&a<b+97-10){return a-97+10}if(a>=65&&a<b+65-10){return a-65+10}return -1}
function Jd(a,b){var c,d,e,f,g,h,i,j;i=a.h>>19;j=b.h>>19;if(i!=j){return j-i}e=a.h;h=b.h;if(e!=h){return e-h}d=a.m;g=b.m;if(d!=g){return d-g}c=a.l;f=b.l;return c-f}
function pz(a){var b;this.a=a;this.f=new lA(a,(Lz(),Lz(),Jz));this.g=new IA(a,this.f);b=$wnd.navigator.userAgent;this.e=b.indexOf(TB)!=-1||b.indexOf('trident')!=-1}
function MA(a,b,c,d){var e,f;f=(d.width+a.b-1)/a.b|0;e=(d.height+a.b-1)/a.b|0;b.xmin=Ct(a.c*c,f);b.xmax=Ct((a.c+1)*c,f);b.ymin=Bt(e-(a.d+1)*c,0);b.ymax=Bt(e-a.d*c,0)}
function Zh(b,c,d,e){Yh();var f=Wh;$moduleName=c;$moduleBase=d;Bh=e;function g(){for(var a=0;a<f.length;a++){f[a]()}}
if(b){try{WA(g)()}catch(a){b(c,a)}}else{WA(g)()}}
function Tc(a){var b,c,d,e;b='Sc';c='Yb';e=Ct(a.length,5);for(d=e-1;d>=0;d--){if(Xt(a[d].d,b)||Xt(a[d].d,c)){a.length>=d+1&&(a.splice(0,d+1),undefined);break}}return a}
function Oq(b){var c,d;d=-1;try{c=tr(b,'background');!!c&&c.b.a.length==1&&(d=Hq((_q(0,c.b.a.length),fi(yw(c.b,0)))))}catch(a){a=Ch(a);if(!de(a,7))throw Dh(a)}return d}
function Wz(a,b){var c,d,e;d=(Lz(),Oz(OC,134217728));for(c=a.j.a.length-1;a.k>d&&c>=b;c--){e=$d(yw(a.j,c),32);if(e.e==a.g)continue;if(e.b){a.k-=e.c;e.b=false}e.d=null}}
function vo(a,b){a.w=bo(a,0,tC,a.B,0);a.A=bo(a,0,tC,a.B,0);if(a.w==0||a.A==0){throw Dh(new lt('JB2Image zero size'))}b.d=a.w;b.b=a.A;a.G=a.A;a.H=a.F=0;io(a,a.G);a.v=true}
function nd(a,b,c,d,e,f,g){var h,i,j,k,l;k=e[f];j=f==g-1;h=j?d:0;l=pd(h,k);d!=10&&sd(kd(a,g-f),b[f],c[f],h,l);if(!j){++f;for(i=0;i<k;++i){l[i]=nd(a,b,c,d,e,f,g)}}return l}
function Yx(){function b(){try{return (new Map).entries().next().done}catch(a){return false}}
if(typeof Map===aB&&Map.prototype.entries&&b()){return Map}else{return Zx()}}
function Vo(a){var b,c,d;d=new ty('[',']');for(c=a.Eb();c.Rb();){b=c.Sb();sy(d,b===a?'(this Collection)':b==null?jB:fi(b))}return !d.a?d.c:d.e.length==0?d.a.a:d.a.a+(''+d.e)}
function Eu(a,b,c){var d,e,f;for(e=new Bv((new sv(a)).a);e.b;){d=zv(e);f=d.Ub();if(ke(b)===ke(f)||b!=null&&wb(b,f)){if(c){d=new pw(d.Ub(),d.Vb());Av(e)}return d}}return null}
function Ni(){Ki=WA(Si);Li=WA(Ti);var c=Vi;var d=Ii;c(d,function(a,b){d[a]=WA(b)});var e=Ji;c(e,function(a,b){e[a]=WA(b)});c(e,function(a,b){$wnd.addEventListener(a,b,true)})}
function Tk(a,b,c,d){var e;e=a.q;d=Xk(a)||!Wk(a)?Uk(a,b,c,de(d,28)?$d(d,28):null):Rk(a,b,c);if(d){dv(d.n,a.r,e);dv(d.n,'rect',new Zm(b));dv(d.n,'subsample',new nt(c))}return d}
function fc(a){var b;if(a.c==null){b=ke(a.b)===ke(dc)?null:a.b;a.d=b==null?jB:ge(b)?ic(ae(b)):ie(b)?'String':Ns(yb(b));a.a=a.a+': '+(ge(b)?hc(ae(b)):b+'');a.c='('+a.d+') '+a.a}}
function mo(){var a,b;_n(this);this.u=false;for(b=0;b<this.n.length;){this.n[b++]=0}for(a=0;a<this.o.length;){this.o[a++]=0}Pq(this.k,new Qj);Pq(this.I,new Jp);Pq(this.S,new Jp)}
function fu(a){var b,c;if(a>=lB){b=55296+(a-lB>>10&1023)&mB;c=56320+(a-lB&1023)&mB;return String.fromCharCode(b)+(''+String.fromCharCode(c))}else{return String.fromCharCode(a&mB)}}
function IA(a,b){this.a=new Jx;this.g=new Jx;this.i=new Xm;this.j=new OA;this.d=new Xm;this.b=a;this.f=b;this.k=(Lz(),Oz('tileCacheSize',256));this.n=Oz('tileSize',512);Mz(new JA(this))}
function Od(a,b){var c,d,e;b&=63;if(b<22){c=a.l<<b;d=a.m<<b|a.l>>22-b;e=a.h<<b|a.m>>22-b}else if(b<44){c=0;d=a.l<<b-22;e=a.m<<b-22|a.l>>44-b}else{c=0;d=0;e=a.l<<b-44}return xd(c&wB,d&wB,e&xB)}
function Du(a,b){var c,d,e;c=b.Ub();e=b.Vb();d=ie(c)?av(a,c):Lu(Mx(a.a,c));if(!(ke(e)===ke(d)||e!=null&&wb(e,d))){return false}if(d==null&&!(ie(c)?bv(a,c):!!Mx(a.a,c))){return false}return true}
function yn(a,b){var c,d,e,f,g;if(b==0){e=1;for(d=0;d<16;d++){g=a.n[d];a.b[d]=1;if(g>0&&g<DB){e=0;a.b[d]=0}}return e}f=a.k[b];if(f<=0||f>=DB){return 1}for(c=0;c<sn[b].a<<4;c++){a.b[c]=0}return 0}
function zl(a,b,c){var d,e;if(a!=b){yl(a,b.k,b.i,c);a.a=b.a;for(d=0;d<a.k;d++){e=si(b.e,(d*b.c+b.border)*ql,d*b.c+b.border+a.i*ql);ri(a.e,e,(d*a.c+a.border)*ql)}}else c>a.border&&Dl(a,c);return a}
function Eo(a,b,c){if(!b){a.e=null;a.f=0;return}if(!c){if(a.g.b.a.length>0){throw Dh(new lt('JB2Image cannot set'))}if(a.e){throw Dh(new lt('JB2Image cannot change'))}}a.e=b;a.f=b.f+b.g.b.a.length}
function Nm(a,b,c,d,e){var f,g,h,i,j,k;h=d*16;f=((h+e)/(2*e)|0)-8;j=f;k=e/2|0;g=(b-1)*16;for(i=0;i<c;i++){a[i]=j<g?j:g;k=k+h;j=j+(k/e|0);k=k%e}if(e==c&&j!=f+h){throw Dh(new lt('Scaler assertion'))}}
function Kc(b,c){var d,e,f,g;for(e=0,f=b.length;e<f;e++){g=b[e];try{g[1]?g[0].db()&&(c=Jc(c,g)):g[0].bc()}catch(a){a=Ch(a);if(de(a,7)){d=a;pc();vc(de(d,57)?$d(d,57).cb():d)}else throw Dh(a)}}return c}
function ym(a){var b,c,d;(a<0.1||a>10)&&(Ek(),Dk);if(a<1.001&&a>0.999){c=lm}else{if(a!=mm){for(b=0;b<256;b++){d=b/255;Ek();d=$wnd.Math.pow(d,1/a);km[b]=le($wnd.Math.floor(255*d+0.5))}mm=a}c=km}return c}
function ek(b){var c,d,e;e=new Vj;for(d=ak(b);d>=0;d=ak(b)){Sj(e,e.b+1);e.a[e.b]=d<<24>>24;e.b+=1}c=hx(e.a,e.b);try{return Wt(c,0,c.length,gu())}catch(a){a=Ch(a);if(de(a,13)){return null}else throw Dh(a)}}
function wo(a,b){var c,d;d=bo(a,0,tC,a.C,0);c=b.e;if(!c&&d>0){if(a.c){c=a.c;Eo(b,c,false)}else{throw Dh(new lt('JB2Image need dict'))}}if(!!c&&d!=c.f+c.g.b.a.length){throw Dh(new lt('JB2Image bad dict'))}}
function yq(){yq=ci;xq=sd(kd(vg,1),$A,2,6,[xC,'page','width','one2one','stretch']);wq=sd(kd(vg,1),$A,2,6,[xC,'color','fore','back','bw']);vq=sd(kd(vg,1),$A,2,6,[xC,'left','center','right','top','bottom'])}
function vn(a,b){var c,d,e;if(a.i<0){return 0}if(yn(a,a.g)==0){for(c=0;c<a.j.f;c++){d=sn[a.g].b;e=sn[a.g].a;wn(a,b,a.g,a.j.b[c],d,e)}}if(++a.g>=sn.length){a.g=0;++a.i;if(zn(a)==0){a.i=-1;return 0}}return 1}
function Yk(a,b){var c,d;a.s=null;c=new Oo;d=$d(av(a.e,a.n),25);!!d&&d.a<19&&(c.c=true);a.o=Ko(c,b,$d(av(a.e,a.g),61));if(Zk(a,a.i,c)){throw Dh(new lt('DjVu Decoder: Corrupted data (Duplicate FGxx chunk)'))}}
function Nx(a,b,c){var d,e,f,g,h;h=b==null?0:(g=Ab(b),g|0);e=(d=a.a.get(h),d==null?[]:d);if(e.length==0){a.a.set(h,e)}else{f=Kx(b,e);if(f){return f.Wb(c)}}rd(e,e.length,new pw(b,c));++a.c;Gx(a.b);return null}
function iz(a){var b,c,d,e;b=0;d=a.length;e=d-4;c=0;while(c<e){b=a.charCodeAt(c+3)+31*(a.charCodeAt(c+2)+31*(a.charCodeAt(c+1)+31*(a.charCodeAt(c)+31*b)));b=b|0;c+=4}while(c<d){b=b*31+Tt(a,c++)}b=b|0;return b}
function Sm(a,b,c){a.xmin=Bt(b.xmin,c.xmin);a.xmax=Ct(b.xmax,c.xmax);a.ymin=Bt(b.ymin,c.ymin);a.ymax=Ct(b.ymax,c.ymax);if(a.xmin>=a.xmax||a.ymin>=a.ymax){a.xmin=a.ymin=a.xmax=a.ymax=0;return false}return true}
function nx(a,b,c,d,e,f){var g,h,i,j;g=d-c;if(g<7){kx(b,c,d,f);return}i=c+e;h=d+e;j=i+(h-i>>1);nx(b,a,i,j,-e,f);nx(b,a,j,h,-e,f);if(f.Xb(a[j-1],a[j])<=0){while(c<d){rd(b,c++,a[i++])}return}lx(a,i,j,h,b,c,d,f)}
function Wk(a){var b,c,d,e;d=$d(av(a.e,a.n),25);if(!d){return false}e=d.width;c=d.height;if(e<=0||c<=0){return false}b=$d(av(a.e,a.i),41);if(!b||b.d!=e||b.b!=c){return false}return !(Vk(a,a.c)||!!a.k||Vk(a,a.j))}
function Pm(a,b){var c,d,e;if(de(b,9)){e=$d(b,9);c=a.xmin>=a.xmax||a.ymin>=a.ymax;d=e.xmin>=e.xmax||e.ymin>=e.ymax;return (c||d)&&c&&d||a.xmin==e.xmin&&a.xmax==e.xmax&&a.ymin==e.ymin&&a.ymax==e.ymax}return false}
function lq(b){b.a=0;oq(b,b.e);b.d=65280;try{b.d=Fh(b.d,ak(b.k)<<8);b.r=(255&ak(b.k))<<16>>16}catch(a){a=Ch(a);if(de(a,6)){b.r=255}else throw Dh(a)}b.d=Rh(b.d,b.r);b.f=25;b.p=0;pq(b);b.i=b.d;Lh(b.d,DB)&&(b.i=wC)}
function Rk(a,b,c){var d,e,f,g;if(b.xmin>=b.xmax||b.ymin>=b.ymax){return new Gl}f=$d(av(a.e,a.n),25);if(f){g=f.width;e=f.height;d=$d(av(a.e,a.i),41);if(g!=0&&e!=0&&!!d&&d.d==g&&d.b==e){return Lo(d,b,c)}}return null}
function ko(a){a.p.a=0;a.q.a=0;a.s.a=0;a.r.a=0;a.f.a=0;a.g.a=0;a.i.a=0;a.j.a=0;a.B.a=0;a.C.a=0;a.M.a=0;a.N.a=0;a.O.a=0;a.P.a=0;a.Q.a=0;a.R.a=0;Vq(a.k);Vq(a.I);Vq(a.S);Pq(a.k,new Qj);Pq(a.I,new Jp);Pq(a.S,new Jp);Ek()}
function HA(a){var b,c,d;a.c=(Lz(),Lz(),Gz);a.e=(null,Hz);Pz(a.d);a.j.a=a.c;a.j.b=a.e;for(d=a.d.ymin;d<=a.d.ymax;d++){for(c=a.d.xmin;c<=a.d.xmax;c++){b=$d(_u(a.a,NA(a.j,c,d)),51);!!b&&(b.b=(ru(),Jh(Cy())))}}oz(a.b.a)}
function Lj(a){Dj();var b;Ej(this);this.i=new sq(a.i);for(b=0;b<a.c.length;b++){!!a.c[b]&&(this.c[b]=new Rj((a.c[b].a&255)<<16>>16))}if(a.d){this.d=ti(a.d.length);ri(this.d,a.d,0)}this.e=a.e;this.a=a.a;this.b=a.b;this.f=a.f}
function Uk(a,b,c,d){var e;if(b.xmin>=b.xmax||b.ymin>=b.ymax){return !d?new wm:tm(d,0,0,null)}e=Qk(a,b,c,d);if(Vk(a,a.i)){if(!e){e=!d?new wm:d;tm(e,b.ymax-b.ymin,b.xmax-b.xmin,(Jl(),Il))}$k(a,e,b,c)&&(d=e)}else{d=e}return d}
function Ur(a){var b,c,d;for(c=new dx(a.a);c.a<c.c.a.length;){b=$d(cx(c),58);Ur(b)}while(a.a.a.length==1){d=a.b;b=$d(yw(a.a,0),58);if(d==null){Wr(a,b.b);Xr(a,b.c)}else if(b.b!=null){break}a.a.a=od(pg,$A,1,0,5,1);xw(a.a,b.a)}}
function ei(a,b){var c=$wnd;if(a===''){return c}var d=a.split('.');!(d[0] in c)&&c.execScript&&c.execScript('var '+d[0]);if(b){var e=b.prototype.$b;e.e=b}for(var f;d.length&&(f=d.shift());){c=c[f]=c[f]||!d.length&&b||{}}return c}
function ut(a){var b,c,d;if(a<0){return 0}else if(a==0){return 32}else{d=-(a>>16);b=d>>16&16;c=16-b;a=a>>b;d=a-256;b=d>>16&8;c+=b;a<<=b;d=a-4096;b=d>>16&4;c+=b;a<<=b;d=a-16384;b=d>>16&2;c+=b;a<<=b;d=a>>14;b=d&~(d>>1);return c+2-b}}
function GA(a){var b,c,d,e;if(gv(a.a)<a.k)return;b=new Lw(new sv(a.a));Gw(b,new TA);e=new Jw;for(c=0;c<(a.k/4|0);c++){d=$d((Ty(c,b.a.length),$d(b.a[c],34)).Ub(),50);ev(a.a,d);e.a[e.a.length]=d}uz(new Ez('tiles-release',new xp(e)))}
function Ox(a,b){var c,d,e,f,g,h;g=b==null?0:(f=Ab(b),f|0);d=(c=a.a.get(g),c==null?[]:c);for(h=0;h<d.length;h++){e=d[h];if(Ix(b,e.Ub())){if(d.length==1){d.length=0;a.a[JC](g)}else{d.splice(h,1)}--a.c;Gx(a.b);return e.Vb()}}return null}
function Pd(a,b){var c,d,e,f,g;b&=63;c=a.h;d=(c&yB)!=0;d&&(c|=-1048576);if(b<22){g=c>>b;f=a.m>>b|c<<22-b;e=a.l>>b|a.m<<22-b}else if(b<44){g=d?xB:0;f=c>>b-22;e=a.m>>b-22|c<<44-b}else{g=d?xB:0;f=d?wB:0;e=c>>b-44}return xd(e&wB,f&wB,g&xB)}
function kl(a,b,c,d){var e,f,g,h,i,j;i=new yk(d,c);if(bv(a.b,i.b)){throw Dh(new os('No duplicates allowed.'))}j=new jk(b);e=ak(j);f=ak(j);g=ak(j);h=ak(j);(e!=dl[0]||f!=dl[1]||g!=dl[2]||h!=dl[3])&&(b=Xj(j,eB));dv(a.b,i.b,b);rk(a.c,i,-1)}
function $z(a){var b,c,d,e,f;c=(Lz(),Oz(OC,134217728));f=0;b=0;while(b<a.j.a.length&&f<c){d=$d(yw(a.j,b),32);if(!d.b)break;f+=d.c;++b}if(b==a.j.a.length)return false;Wz(a,b+1);e=$d(yw(a.j,b),32);if(a.k+e.c>c)return false;return Zz(a,e)}
function bi(a,b,c){var d=_h,h;var e=d[a];var f=e instanceof Array?e[0]:null;if(e&&!f){_=e}else{_=(h=b&&b.prototype,!h&&(h=_h[b]),di(h));_._b=c;!b&&(_.ac=gi);d[a]=_}for(var g=3;g<arguments.length;++g){arguments[g].prototype=_}f&&(_.$b=f)}
function Jj(a,b){var c,d,e,f,g;if(a.e){return 0}f=0;g=b.length;d=0;while(g>0&&!a.e){if(a.f==0){a.b=0;if(Fj(a)==0){a.f=1;a.e=true}--a.f}c=a.f>g?g:a.f;if(c>0){for(e=0;e<c;e++){b[f+e]=a.d[a.b+e]<<24>>24}f+=c}a.f-=c;a.b+=c;g-=c;d+=c}return d}
function Nk(a){var b,c,d,e,f,g;g=new wm;e=new Yi;f=(xi(),e.d);f.style['visibility']='hidden';Bi(f,new al(f,g));c=new nu;while((b=ak(a))!=-1){c.a+=String.fromCharCode(b&mB)}d='data:image/jpeg;base64,'+Jk(c.a);$i(e,(ni(),new ji(d)));return g}
function hA(b,c){var d,e;try{b.b=new nl;ml(b.b,c);e=b.b.c.d.b.a.length;uz(new Ez('page-count',''+e));b.i=new Kw(e);for(d=0;d<e;d++)vw(b.i,new uA(b,d));b.j=new Lw(b.i);Gw(b.j,null);oz(b.a.a)}catch(a){a=Ch(a);if(de(a,6)){new vy}else throw Dh(a)}}
function CA(a,b){var c,d,e,f,g;e=a.c+b;if(e<0||e>=gA(a.f))return false;d=fA(a.f,e);if(!d)return false;a.j.a=e;a.j.b=a.e;for(g=a.d.ymin;g<=a.d.ymax;g++){for(f=a.d.xmin;f<=a.d.xmax;f++){c=e!=a.c;if(EA(a,NA(a.j,f,g),d,c))return true}}return false}
function un(){un=ci;tn=sd(kd(oe,1),YB,8,15,[lB,GB,GB,HB,HB,HB,yB,yB,yB,IB,IB,IB,JB,IB,IB,JB]);sn=sd(kd(mf,1),$A,33,0,[new Bn(0,1),new Bn(1,1),new Bn(2,1),new Bn(3,1),new Bn(4,4),new Bn(8,4),new Bn(12,4),new Bn(16,16),new Bn(32,16),new Bn(48,16)])}
function Zs(a){if(a.Ob()){var b=a.c;b.Pb()?(a.k='['+b.j):!b.Ob()?(a.k='[L'+b.Mb()+';'):(a.k='['+b.Mb());a.b=b.Lb()+'[]';a.i=b.Nb()+'[]';return}var c=a.g;var d=a.d;d=d.split('/');a.k=at('.',[c,at('$',d)]);a.b=at('.',[c,at('.',d)]);a.i=d[d.length-1]}
function yu(a){Ny(a!=null,'Null charset name');a=a.toLocaleUpperCase();if(Xt((Gy(),Dy).a,a)){return Dy}else if(Xt(Ey.a,a)){return Ey}else if(Xt(Fy.a,a)){return Fy}if(/^[A-Za-z0-9][\w-:\.\+]*$/.test(a)){throw Dh(new Cu(a))}else{throw Dh(new Bu(a))}}
function Km(a,b,c){if(a.n<=0||a.k<=0||a.b<=0||a.a<=0){throw Dh(new lt(nC))}if(b==0&&c==0){b=a.b;c=a.n}else if(b<=0||c<=0){throw Dh(new jt(oC))}a.p=0;a.j=a.n;while(b+b<c){++a.p;a.j=a.j+1>>1;b<<=1}a.c==null&&(a.c=od(oe,YB,8,a.b,15,1));Nm(a.c,a.j,a.b,c,b)}
function Lm(a,b,c){if(a.n<=0||a.k<=0||a.b<=0||a.a<=0){throw Dh(new lt(nC))}if(b==0&&c==0){b=a.a;c=a.k}else if(b<=0||c<=0){throw Dh(new jt(oC))}a.q=0;a.i=a.k;while(b+b<c){++a.q;a.i=a.i+1>>1;b<<=1}a.o==null&&(a.o=od(oe,YB,8,a.a,15,1));Nm(a.o,a.i,a.a,c,b)}
function Mk(a,b){var c,d,e;if(!(!de(b.d,22)||!!$d(b.d,22).a))return false;e=Yj(b);if(!e||!cn(e)){throw Dh(new os(gC))}d=en(e);c=Yj(d);if(!!c&&Xt('FORM:DJVI',d.b)){vw(a.d,c);return true}else{throw Dh(new lt('DejaVu decoder: a DJVI include was expected'))}}
function Vr(a,b){var c,d,e,f,g;c=new Yr;d=ak(b);if(d<0){throw Dh(new lt(BC))}g=dk(b);if(g<0){throw Dh(new lt(BC))}g>0&&Wr(c,fk(b,g));g=dk(b);if(g<0){throw Dh(new lt(BC))}g>0&&Xr(c,fk(b,g));f=1;try{if(d>0){for(e=0;e<d;e++){f+=Vr(c,b)}}}finally{vw(a.a,c)}return f}
function EA(a,b,c,d){var e,f;MA(b,a.i,a.n,$d(av(c.e,c.n),25));if(Tm(a.i))return false;f=AA(a,b);if(!f){f=new LA;FA(a,b,f)}if(f.a)return false;e=Tk(c,a.i,b.b,null);!!e&&vz(new Fz('tile-data',b,e),e.f);f.a=true;f.b=(ru(),Th(Jh(Cy()),d?500:0));d||mz(a.b.a);return true}
function Fd(a){var b,c,d;c=a.l;if((c&c-1)!=0){return -1}d=a.m;if((d&d-1)!=0){return -1}b=a.h;if((b&b-1)!=0){return -1}if(b==0&&d==0&&c==0){return -1}if(b==0&&d==0&&c!=0){return vt(c)}if(b==0&&d!=0&&c==0){return vt(d)+22}if(b!=0&&d==0&&c==0){return vt(b)+44}return -1}
function xn(a,b){var c,d,e,f,g,h,i,j;a.j=b;c=0;i=tn;j=0;for(e=0;e<4;e++){a.n[c++]=i[j++]}for(f=0;f<4;f++){a.n[c++]=i[j]}++j;for(g=0;g<4;g++){a.n[c++]=i[j]}++j;for(h=0;h<4;h++){a.n[c++]=i[j]}++j;a.k[0]=0;for(d=1;d<10;d++){a.k[d]=i[j++]}while(a.n[0]>=DB){zn(a)}return a}
function Nq(b,c){var d,e;for(e=0;++e<xq.length;){if(Xt(xq[e],b)){return -e}}try{if(b.charCodeAt(0)==100){return Fp(b.substr(1))}else if(c){return Fp(b)}throw Dh(new jt('DjVuAnno.bad_zoom'))}catch(a){a=Ch(a);if(de(a,11)){d=a;if(!c){throw Dh(d)}}else throw Dh(a)}return 0}
function Kd(a){var b,c,d,e,f;if(isNaN(a)){return Vd(),Ud}if(a<-9223372036854775808){return Vd(),Sd}if(a>=9223372036854775807){return Vd(),Rd}e=false;if(a<0){e=true;a=-a}d=0;if(a>=zB){d=le(a/zB);a-=d*zB}c=0;if(a>=AB){c=le(a/AB);a-=c*AB}b=le(a);f=xd(b,c,d);e&&Dd(f);return f}
function tm(a,b,c,d){var e,f,g,h;if(b!=a.k||c!=a.i){a.e=null;a.k=b;a.i=c}h=sl(a,a.k);if(h>0){if(!a.e){rl(a,a.i,a.k);if(!d){for(g=0;g<h;g++){a.e[g*ql+3]=255}}}if(d){a.e[a.o]=d.i;a.e[a.g]=d.g;a.e[a.d]=d.f;a.e[3]=255;e=qi(a.e.buffer);f=e[0];for(g=0;g<h;g++){e[g]=f}}}return a}
function Xz(a){var b,c,d,e,f;b=a.b.c;if(a.c>0||!(b.a.b.a.length>0&&Qq(b.a,0)!=null&&$d(Qq(b.a,0),21).c==0)||Kh(a.f,(Lz(),Oz(NC,KB))))return;for(e=new dx(a.j);e.a<e.c.a.length;){d=$d(cx(e),32);f=tk(b,d.e);c=dA(a,f);if(!c.a&&!c.c){Nh(Eh(a.f,c.b),(Lz(),Oz(NC,KB)))&&aA(a,f);break}}}
function pl(a){Gk();this.a=a;this.b=(Uy('anno'),'anno');this.c=(Uy(jC),jC);this.g=(Uy('fgJb2Dict'),'fgJb2Dict');this.i=(Uy('fgJb2'),'fgJb2');this.j=(Uy('fgPalette'),'fgPalette');this.n=(Uy('info'),'info');this.r=(Uy(kC),kC);this.t=(Uy('text'),'text');this.q=new nt(0);this.e=new Jx;this.d=new Jw}
function Mi(){Mi=ci;Ii={_default_:Si,dragenter:Ri,dragover:Ri};Ji={click:Qi,dblclick:Qi,mousedown:Qi,mouseup:Qi,mousemove:Qi,mouseover:Qi,mouseout:Qi,mousewheel:Qi,keydown:Pi,keyup:Pi,keypress:Pi,touchstart:Qi,touchend:Qi,touchmove:Qi,touchcancel:Qi,gesturestart:Qi,gestureend:Qi,gesturechange:Qi}}
function hn(a){var b,c,d,e;for(c=0;c<4;c++){if(a.charCodeAt(c)<32||a.charCodeAt(c)>126){throw Dh(new os(pC))}}for(d=0;d<_m.length;d++){if(Xt(a,_m[d])){return true}}e=a.substr(0,3);for(b=0;b<an.length;b++){if(Xt(e,an[b])&&a.charCodeAt(3)>=49&&a.charCodeAt(3)<=57){throw Dh(new os(pC))}}return false}
function lz(a){var b;b=Yz(a.f)||DA(a.g,false)||CA(a.g,0)||_z(a.f)||DA(a.g,true)||$z(a.f)||BA(a.g,0)||CA(a.g,1)||CA(a.g,-1)||BA(a.g,1)||BA(a.g,-1);if(b){a.e&&Nh(Eh(a.d,400),(ru(),Jh(Cy())))&&(a.b=true);if(a.b){a.b=false;a.d=(ru(),Jh(Cy()));Lc((zc(),new rz(a)),50);return false}return true}return a.c=false}
function Lk(a){var b,c;if(Xt(a.p,eC)){b=$d(Sk(a,a.c),83);de(b,47)&&Jn($d(b,47));c=$d(av(a.e,a.n),25);if(!c){throw Dh(new lt('DjVu Decoder: Corrupted data (Missing INFO chunk)'))}}else if(Xt(a.p,fC)){if(!Vk(a,a.n)){throw Dh(new lt('DjVu Decoder: Corrupted data (Missing IW44 data chunks)'))}}Zk(a,a.g,null)}
function wr(b,c,d){var e,f,g,h,i;try{while(d.a<c.length){h=vr(c,d);switch(h.b){case 0:{if(Js(Tt(c,d.a))){throw Dh(new jt('Expected Token'))}g=vr(c,d);f=$d(g.a,48);i=new sr(f.a);wr(i,c,d);vw(b.b,i);break}case 1:return;default:Pq(b,h.a);}}}catch(a){a=Ch(a);if(de(a,7)){e=a;Rb(e,(Ek(),Dk),'')}else throw Dh(a)}}
function Fc(a){var b,c,d,e,f,g,h;f=a.length;if(f==0){return null}b=false;c=new Ob;while(lc()-c.a<16){d=false;for(e=0;e<f;e++){h=a[e];if(!h){continue}d=true;if(!h[0].db()){a[e]=null;b=true}}if(!d){break}}if(b){g=[];for(e=0;e<f;e++){!!a[e]&&(g[g.length]=a[e],undefined)}return g.length==0?null:g}else{return a}}
function iq(a,b){var c;if(Kh(b,a.d)){b=lB-b;a.a+=b;a.d=Eh(a.d,b);c=mq(a,a.a);a.p=a.p-c<<16>>16;a.a=mB&a.a<<c;a.d=Fh(mB,Rh(Sh(a.d,c),a.c>>a.p&(1<<c)-1));a.p<16&&pq(a);a.i=a.d;Lh(a.d,DB)&&(a.i=wC);return 1}--a.p;a.a=mB&b<<1;a.d=Fh(mB,Rh(Sh(a.d,1),a.c>>a.p&1));a.p<16&&pq(a);a.i=a.d;Lh(a.d,DB)&&(a.i=wC);return 0}
function Lo(a,b,c){var d,e,f,g,h,i,j,k;if(a.d==0||a.b==0){throw Dh(new lt('JB2Image can not create bitmap'))}h=b.xmin*c;i=b.ymin*c;k=b.xmax-b.xmin;j=b.ymax-b.ymin;d=new Gl;yl(d,j,k,0);Cl(d,1+c*c);for(f=new dx(a.a.b);f.a<f.c.a.length;){e=$d(cx(f),53);g=Do(a,e.c);!!g.a&&vl(d,g.a,(mB&e.b)-h,(mB&e.a)-i,c)}return d}
function kA(a){var b,c,d,e,f,g,h,i,j,k;k=0;for(i=new dx(a.i);i.a<i.c.a.length;){h=$d(cx(i),32);b=h.f/10|0;k+=b;h.f-=b}e=sd(kd(oe,1),YB,8,15,[1,-1]);f=0;while(k>0){for(c=0,d=e.length;c<d;++c){b=e[c];g=a.g+b*(f%a.i.a.length);if(g<0||g>=a.i.a.length)continue;j=(k/10|0)+1;k-=j;$d(yw(a.i,g),32).f+=j;if(k<=0)break}++f}}
function No(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o;e=Do(a,b.c).a;if(!e)return false;g=c.xmin*d;h=c.ymin*d;l=(mB&b.b)-g;o=(mB&b.a)-h;if(d==1){j=l>0?l:0;m=o>0?o:0;k=-l>0?-l:0;n=-o>0?-o:0;i=Ct(c.xmax-c.xmin-j,e.i-k);f=Ct(c.ymax-c.ymin-m,e.k-n);return i>0&&f>0}return l<(c.xmax-c.xmin)*d||o>=(c.ymax-c.ymin)*d||l+e.i<0||o+e.k<0}
function Xx(){if(!Object.create||!Object.getOwnPropertyNames){return false}var a='__proto__';var b=Object.create(null);if(b[a]!==undefined){return false}var c=Object.getOwnPropertyNames(b);if(c.length!=0){return false}b[a]=42;if(b[a]!==42){return false}if(Object.getOwnPropertyNames(b).length==0){return false}return true}
function bA(b,c,d){var e,f,g,h;try{g=Yj(d);while(cn(g)&&(!c.a||!c.g)){e=en(g);f=e.b;if(Xt(f.substr(0,5),'FORM:')){bA(b,c,e)}else if(Xt('INFO',f)){h=new Ak;zk(h,e);c.a=h;uz(new Fz('page-info',''+c.e,h))}else (Xt(aC,f)||Xt(bC,f))&&tA(c,fs(new is,e))}!c.g&&tA(c,new is)}catch(a){a=Ch(a);if(de(a,6)){tA(c,null)}else throw Dh(a)}}
function co(a,b,c,d){var e,f,g,h,i,j,k,l;e=c.i;h=b.i;g=b.k;j=$d(Qq(a.K,d),9);k=1+(h/2|0)-h-(((1+j.xmax-j.xmin)/2|0)-j.xmax);l=1+(g/2|0)-g-(((1+j.ymax-j.ymin)/2|0)-j.ymax);Dl(b,2);Dl(c,2-k);Dl(c,2+h+k-e);i=g-1;f=i+l;ro(a,b,c,k,h,i,f,(i+1)*b.c+b.border,i*b.c+b.border,(f+1)*c.c+c.border+k,f*c.c+c.border+k,(f-1)*c.c+c.border+k)}
function Xk(a){var b,c,d,e,f,g,h;g=$d(av(a.e,a.n),25);if(!g){return false}h=g.width;f=g.height;if(h<=0||f<=0){return false}d=$d(av(a.e,a.i),41);if(!d||d.d!=h||d.b!=f){return false}b=$d(av(a.e,a.c),47);c=0;!!b&&(c=_k(h,f,b.k?b.k.e:0,b.k?b.k.d:0));if(c<1||c>12){return false}e=0;!!a.k&&(e=_k(h,f,a.k.i,a.k.k));return e>=1&&e<=12}
function Fp(a){var b,c,d,e,f;if(a==null){throw Dh(new Ot(jB))}d=a.length;e=d>0&&(a.charCodeAt(0)==45||a.charCodeAt(0)==43)?1:0;for(b=e;b<d;b++){if(Hs(a.charCodeAt(b),10)==-1){throw Dh(new Ot(kB+a+'"'))}}f=parseInt(a,10);c=f<-2147483648;if(isNaN(f)){throw Dh(new Ot(kB+a+'"'))}else if(c||f>eB){throw Dh(new Ot(kB+a+'"'))}return f}
function Al(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p;m=c>0?c:0;o=d>0?d:0;n=-c>0?-c:0;p=-d>0?-d:0;l=Ct(a.i-m,b.i-n);g=Ct(a.k-o,b.k-p);if(l>0&&g>0){f=a.a-1<<24>>24;do{i=sl(a,o++)+m;j=sl(b,p++)+n;h=l;do{e=(k=i>=0?a.e[i*ql+3]:0,((k*(a.a-1)+(a.a-2))/255|0)+xl(b,j++));Bl(a,i++,(e<f?e:f)<<24>>24)}while(--h>0)}while(--g>0);return true}return false}
function An(){un();var a,b,c;this.f=od(Se,WB,19,32,0,1);for(b=0;b<32;b++){this.f[b]=new Qj}this.c=md(Se,[$A,WB],[71,19],0,[10,8],2);for(a=0;a<10;a++){for(c=0;c<8;c++){rd(this.c[a],c,new Qj)}}this.k=new Int32Array(10);this.n=new Int32Array(16);this.b=new Int8Array(256);this.a=new Int8Array(16);this.g=0;this.i=1;this.d=new Qj;this.e=new Qj}
function _n(a){a.t=new Qj;a.L=new Qj;a.f=new Jp;a.g=new Jp;a.i=new Jp;a.j=new Jp;a.p=new Jp;a.q=new Jp;a.r=new Jp;a.s=new Jp;a.B=new Jp;a.C=new Jp;a.M=new Jp;a.N=new Jp;a.O=new Jp;a.P=new Jp;a.Q=new Jp;a.R=new Jp;a.k=new Wq;a.I=new Wq;a.J=new Wq;a.K=new Wq;a.S=new Wq;a.T=new Wq;a.U=od(oe,YB,8,3,15,1);a.n=od(me,UB,8,EB,15,1);a.o=od(me,UB,8,2048,15,1)}
function Xl(a,b){var c,d,e,f,g,h,i,j;if(a.d.j){throw Dh(new lt('YCC_to_RGB only legal with three colors'))}while(b-->0){j=a.d.e[a.c]<<24>>24;c=a.d.e[a.c+1]<<24>>24;f=a.d.e[a.c+2]<<24>>24;h=f+(f>>1);i=j+128-(c>>2);d=i+(c<<1);a.d.e[a.c+a.a]=d<255?d>0?d:0:255;e=i-(h>>1);a.d.e[a.c+a.b]=e<255?e>0?e:0:255;g=j+128+h;a.d.e[a.c+a.e]=g<255?g>0?g:0:255;a.c+=ql}}
function DA(a,b){var c,d,e,f,g,h,i,j;if(a.c<0)return false;a.j.b=12;for(d=0;d<(b?gA(a.f)*2:1);d++){e=a.c+(d%2==0?-1:1)*(d/2|0);if(e<0||e>=gA(a.f))continue;g=fA(a.f,e);if(!g)continue;a.j.a=e;f=$d(av(g.e,g.n),25);h=(f.width+12-1)/12|0;c=(f.height+12-1)/12|0;for(i=0;i*a.n<h;i++){for(j=0;j*a.n<c;j++){if(EA(a,NA(a.j,i,j),g,false))return true}}}return false}
function es(b,c){var d,e,f,g;try{d=od(me,UB,8,c,15,1);for(f=0;f<d.length;f++)d[f]=b.b[f]<<24>>24;g=Wt(d,0,d.length,gu());g=ii(as,g,'\u2010');g=ii(_r,g,'?');return g}catch(a){a=Ch(a);if(de(a,75)){throw Dh(new ac)}else if(de(a,13)){e=od(ne,UB,8,c,15,1);for(f=0;f<c;f++){e[f]=b.b[f]&255&mB;(e[f]<32||e[f]>126)&&(e[f]=63)}return ju(e,e.length)}else throw Dh(a)}}
function Ln(a){var b,c,d,e,f,g,h,i;if(!a.k){return null}h=a.k.e;c=a.k.d;g=h*4;b=new Uint8Array(c*g);Cn(a.k,0,b,g,false);if(!!a.f&&!!a.b&&a.c>=0){Cn(a.b,1,b,g,a.d);Cn(a.f,2,b,g,a.d)}f=um(new wm,b,c,h);e=new bm(f,0);for(d=0;d<c;){_l(e,d++,0);if(!!a.f&&!!a.b&&a.c>=0){Xl(e,h)}else{for(i=h;i-->0;e.c+=ql){Ll(e,127-(255&e.d.e[e.c+e.a]<<24>>24)<<24>>24)}}}return f}
function qm(a,b){var c,d,e,f,g,h,i,j,k,l,m;l=Ct(b.k,a.k);k=Ct(b.i,a.i);if(l<=0||k<=0){return}f=b.a-1;g=Bm(f);h=b.border;c=a.border;d=new bm(a,0);for(m=0;m<l;m++){d.c=c*ql;for(j=0;j<k;d.c+=ql){i=xl(b,h+j++);if(i>0){if(i>=f){Ll(d,0)}else{e=g[i];Yl(d,(255&d.d.e[d.c+d.a]<<24>>24)*e>>16,(255&d.d.e[d.c+d.b]<<24>>24)*e>>16,(255&d.d.e[d.c+d.e]<<24>>24)*e>>16)}}}c+=a.i;h+=b.c}}
function zq(a){var b,c,d,e;Oq(a);Fq((d=tr(a,'zoom'),!!d&&d.b.a.length>0?Nq((_q(0,d.b.a.length),fi(yw(d.b,0))),true):0));Cq((e=tr(a,'mode'),!!e&&e.b.a.length>0?Mq((_q(0,e.b.a.length),fi(yw(e.b,0)))):0));Bq((b=tr(a,'align'),!!b&&b.b.a.length>0?Lq((_q(0,b.b.a.length),fi(yw(b.b,0)))):0));Eq((c=tr(a,'align'),!!c&&c.b.a.length>1?Lq((_q(1,c.b.a.length),fi(yw(c.b,1)))):0));Aq(a)}
function Bd(a,b,c,d,e,f){var g,h,i,j,k,l,m;j=Ed(b)-Ed(a);g=Od(b,j);i=xd(0,0,0);while(j>=0){h=Gd(a,g);if(h){j<22?(i.l|=1<<j,undefined):j<44?(i.m|=1<<j-22,undefined):(i.h|=1<<j-44,undefined);if(a.l==0&&a.m==0&&a.h==0){break}}k=g.m;l=g.h;m=g.l;g.h=l>>>1;g.m=k>>>1|(l&1)<<21;g.l=m>>>1|(k&1)<<21;--j}c&&Dd(i);if(f){if(d){ud=Md(a);e&&(ud=Qd(ud,(Vd(),Td)))}else{ud=xd(a.l,a.m,a.h)}}return i}
function fn(a){var b,c,d,e;do{if(bk(a.c,a.a)<4){return null}}while(a.a[0]==65&&a.a[1]==84&&a.a[2]==38&&a.a[3]==84);if(bk(a.c,a.b)<4){return null}e=(255&a.b[0])<<24|(255&a.b[1])<<16|(255&a.b[2])<<8|255&a.b[3];if(e<0){return null}b=jn(a.a);c=null;if(hn(b)){if(e<4){return null}if(bk(a.c,a.b)<4){return null}e-=4;c=jn(a.b);hn(c)}d=Xj(a.c,e);d.b=c!=null?b+':'+c:b;hk(a.c,e+(e&1));return d}
function Um(a,b,c){if(b.xmin>=b.xmax||b.ymin>=b.ymax){a.xmin=c.xmin;a.xmax=c.xmax;a.ymin=c.ymin;a.ymax=c.ymax;return !(a.xmin>=a.xmax||a.ymin>=a.ymax)}if(c.xmin>=c.xmax||c.ymin>=c.ymax){a.xmin=b.xmin;a.xmax=b.xmax;a.ymin=b.ymin;a.ymax=b.ymax;return !(a.xmin>=a.xmax||a.ymin>=a.ymax)}a.xmin=Ct(b.xmin,c.xmin);a.xmax=Bt(b.xmax,c.xmax);a.ymin=Ct(b.ymin,c.ymin);a.ymax=Bt(b.ymax,c.ymax);return true}
function jd(a,b){var c;switch(ld(a)){case 6:return ie(b);case 7:return fe(b);case 8:return ee(b);case 3:return Array.isArray(b)&&(c=ld(b),!(c>=14&&c<=16));case 11:return b!=null&&typeof b===aB;case 12:return b!=null&&(typeof b===XA||typeof b==aB);case 0:return Zd(b,a.__elementTypeId$);case 2:return je(b)&&!(b.ac===gi);case 1:return je(b)&&!(b.ac===gi)||Zd(b,a.__elementTypeId$);default:return true;}}
function Pr(a,b,c,d,e,f){var g,h,i,j,k,l;g=(a-e[0])*(f[1]-e[1])-(b-e[1])*(f[0]-e[0]);h=(c-e[0])*(f[1]-e[1])-(d-e[1])*(f[0]-e[0]);i=(e[0]-a)*(d-b)-(e[1]-b)*(c-a);j=(f[0]-a)*(d-b)-(f[1]-b)*(c-a);if(g==0&&h==0){return Sr(a,b,e[0],e[1],f[0],f[1])||Sr(c,d,e[0],e[1],f[0],f[1])||Sr(e[0],e[1],a,b,c,d)||Sr(f[0],f[1],a,b,c,d)}k=(g<0?-1:g>0?1:0)*(h<0?-1:h>0?1:0);l=(i<0?-1:i>0?1:0)*(j<0?-1:j>0?1:0);return k<=0&&l<=0}
function Nt(){Nt=ci;var a;Jt=sd(kd(oe,1),YB,8,15,[-1,-1,30,19,15,13,11,11,10,9,9,8,8,8,8,7,7,7,7,7,7,7,6,6,6,6,6,6,6,6,6,6,6,6,6,6,5]);Kt=od(oe,YB,8,37,15,1);Lt=sd(kd(oe,1),YB,8,15,[-1,-1,63,40,32,28,25,23,21,20,19,19,18,18,17,17,16,16,16,15,15,15,15,14,14,14,14,14,14,13,13,13,13,13,13,13,13]);Mt=od(pe,UB,8,37,14,1);for(a=2;a<=36;a++){Kt[a]=le($wnd.Math.pow(a,Jt[a]));Mt[a]=Ih({l:wB,m:wB,h:524287},Kt[a])}}
function Zz(b,c){var d,e,f,g,h;d=c.d;try{if(!d){d=c.d=gl(b.b,c.e);if(!d)return true}if(Pk(d)){c.b=true;if(!c.a){sA(c,$d(av(d.e,d.n),25));tA(c,$d(av(d.e,d.t),55))}c.c=(e=0,f=$d(Sk(d,d.c),83),!!f&&(e+=f.Ab()),g=$d(av(d.e,d.i),41),!!g&&(e+=Co(g)),h=$d(av(d.e,d.j),68),!!h&&(e+=h.b.length*10+h.a.length*4),!!d.k&&(e+=d.k.e.byteLength),e);b.k+=c.c}return true}catch(a){a=Ch(a);if(de(a,6)){return false}else throw Dh(a)}}
function rk(a,b,c){var d,e,f,g;c<0&&(c=a.a.b.a.length);if(bv(a.b,b.b)){throw Dh(new os('DjVmDir.dupl_id2 '+b.b))}if((b.a&63)==3){for(d=0;d<a.a.b.a.length;d++){g=$d(Qq(a.a,d),21);if((g.a&63)==3){throw Dh(new os('DjVmDir.multi_save2'))}}}Sq(a.a,b,c);if((b.a&63)==1){f=0;for(e=0;e<a.a.b.a.length;e++){g=$d(Qq(a.a,e),21);if(g==b){break}(g.a&63)==1&&++f}Sq(a.d,b,f);for(d=f;d<a.d.b.a.length;d++){$d(Qq(a.d,d),21)}}return c}
function ds(a,b){var c,d,e,f,g,h;if(Xt(bC,b.b)){b=Zj(new ik,Ij(new Kj,b));b.b=aC}g=dk(b);f=od(me,UB,8,g,15,1);d=bk(b,f);for(e=0;e<d;e++){c=f[e];if(c==0){break}switch(c){case 11:case 29:case 31:f[e]=10;}}if(d<g){while(d<g){f[d++]=0}a.b=cs(f);a.c=a.b.buffer;throw Dh(new os('DjVuText.corrupt_chunk'))}a.b=cs(f);a.c=a.b.buffer;h=ak(b);if(h!=-1){if(h!=1){throw Dh(new os('DjVuText.bad_version='+h))}ms(a.a,b,g,null,null)}}
function hj(){var a=navigator.userAgent.toLowerCase();var b=$doc.documentMode;if(function(){return a.indexOf('webkit')!=-1}())return PB;if(function(){return a.indexOf(TB)!=-1&&b>=10&&b<11}())return 'ie10';if(function(){return a.indexOf(TB)!=-1&&b>=9&&b<11}())return 'ie9';if(function(){return a.indexOf(TB)!=-1&&b>=8&&b<11}())return 'ie8';if(function(){return a.indexOf('gecko')!=-1||b>=11}())return 'gecko1_8';return 'unknown'}
function hq(a,b,c){var d,e,f;d=b.a&1;e=24576+(c+a.a>>2);c>e&&(c=e);if(Kh(c,a.d)){c=lB-c;a.a+=c;a.d=Eh(a.d,c);Pj(b,a.g[255&b.a]);f=mq(a,a.a);a.p=a.p-f<<16>>16;a.a=mB&a.a<<f;a.d=Fh(mB,Rh(Sh(a.d,f),a.c>>a.p&(1<<f)-1));a.p<16&&pq(a);a.i=a.d;Lh(a.d,DB)&&(a.i=wC);return d^1}Lh(Fh(ZB,a.a),Fh(ZB,a.n[255&b.a]))&&Pj(b,a.q[255&b.a]);--a.p;a.a=mB&c<<1;a.d=Fh(mB,Rh(Sh(a.d,1),a.c>>a.p&1));a.p<16&&pq(a);a.i=a.d;Lh(a.d,DB)&&(a.i=wC);return d}
function Ik(a,b,c,d,e){var f,g,h,i,j,k,l;if(b.a.length==0||c.xmin>=c.xmax||c.ymin>=c.ymax)return null;c.xmin=c.xmin/e|0;c.ymin=c.ymin/e|0;c.xmax=(c.xmax+e-1)/e|0;c.ymax=(c.ymax+e-1)/e|0;Sm(c,c,d);if(c.xmin>=c.xmax||c.ymin>=c.ymax)return null;f=new Gl;yl(f,c.ymax-c.ymin,c.xmax-c.xmin,0);Cl(f,1+e*e);k=c.xmin*e;l=c.ymin*e;g=$d(av(a.e,a.i),41);for(i=new dx(b);i.a<i.c.a.length;){h=$d(cx(i),53);j=Do(g,h.c);vl(f,j.a,h.b-k,h.a-l,e)}return f}
function sq(a){fq();var b,c,d;gq(this);for(c=0;c<a.e.length;c++){tq(this.e[c],a.e[c])}this.b=a.b;for(d=0;d<a.g.length;d++){!!a.g[d]&&(this.g[d]=new Rj((a.g[d].a&255)<<16>>16))}this.j=hx(a.j,a.j.length);su(a.n,0,this.n,0,a.n.length);su(a.o,0,this.o,0,a.o.length);for(b=0;b<a.g.length;b++){!!a.q[b]&&(this.q[b]=new Rj((a.q[b].a&255)<<16>>16))}this.a=a.a;this.k=new jk(a.k);this.c=a.c;this.d=a.d;this.i=a.i;this.f=a.f;this.p=a.p;this.r=a.r}
function zk(a,b){var c,d,e;c=new jk(b);d=od(me,UB,8,10,15,1);e=bk(c,d);if(e<5){throw Dh(new os('DjVuInfo: Corrupted file (truncated INFO chunk)'))}a.width=(255&d[0])<<8|255&d[1];a.height=(255&d[2])<<8|255&d[3];a.a=255&d[4];e>=6&&d[5]!=-1&&(a.a=(255&d[5])<<8|a.a);if(e>=8&&d[7]!=-1){a.dpi=(255&d[7])<<8|255&d[6];(a.dpi<25||a.dpi>6000)&&(a.dpi=300)}if(a.width<0||a.height<0){throw Dh(new os('DjVu Decoder: Corrupted file (image size is zero)'))}}
function su(a,b,c,d,e){ru();var f,g,h,i,j,k,l,m,n;Vy(a,'src');Vy(c,'dest');m=yb(a);i=yb(c);Ry((m.f&4)!=0,'srcType is not an array');Ry((i.f&4)!=0,'destType is not an array');l=m.c;g=i.c;Ry((l.f&1)!=0?l==g:(g.f&1)==0,"Array types don't match");n=a.length;j=c.length;if(b<0||d<0||e<0||b+e>n||d+e>j){throw Dh(new vs)}if((l.f&1)==0&&m!=i){k=_d(a);f=_d(c);if(ke(a)===ke(c)&&b<d){b+=e;for(h=d+e;h-->d;){rd(f,h,k[--b])}}else{for(h=d+e;d<h;){rd(f,d++,k[b++])}}}else e>0&&yy(a,b,c,d,e,true)}
function Ok(a,b){var c,d,e,f;if(a.q.a!=0){throw Dh(new lt((Ms($e),$e.k+' decode already called.')))}a.q=xt(1);e=Yj(b);if(!e||!cn(e)){throw Dh(new os(gC))}d=en(e);f=Yj(d);vw(a.d,f);if(Xt('FORM:DJVU',d.b)){a.p=eC;if(!Vk(a,a.n)){c=en(f);if(!Xt('INFO',c.b)){throw Dh(new os('DjVuDecoder:: Corrupted file (Does not start with INFO chunk)'))}Hk(a,a.n,new Ak,c)}}else if(Xt('FORM:PM44',d.b)||Xt('FORM:BM44',d.b)){a.p=fC}else{throw Dh(new lt('DejaVu decoder: a DJVU or IW44 image was expected'))}}
function vl(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r;if(e==1){return Al(a,b,c,d)}if(c>=a.i*e||d>=a.k*e||c+b.i<0||d+b.k<0){return false}if(b.e){h=d/e|0;i=d-e*h;if(i<0){--h;i+=e}q=c/e|0;r=c-e*q;if(r<0){--q;r+=e}n=0;for(;n<b.k;n++){if(h>=0&&h<a.k){f=q;g=r;l=n*b.c+b.border;k=h*a.c+a.border;for(m=0;m<b.i;m++){if(f>=0&&f<a.i){j=k+f;Bl(a,j,(p=j>=0?a.e[j*ql+3]:0,((p*(a.a-1)+(a.a-2))/255|0)+(o=l+m>=0?b.e[(l+m)*ql+3]:0,(o*(b.a-1)+(b.a-2))/255|0)))}if(++g>=e){g=0;++f}}}if(++i>=e){i=0;++h}}}return true}
function _z(b){var c,d,e,f,g,h,i,j;e=null;c=b.b.c;for(g=new dx(b.j);g.a<g.c.a.length;){f=$d(cx(g),32);if(f.g)continue;if(c.a.b.a.length>0&&Qq(c.a,0)!=null&&$d(Qq(c.a,0),21).c==0){d=dA(b,tk(c,f.e));if(d.a){i=Zj(new ik,Up(new Wp,d.a))}else{!e&&(e=f);continue}}else{try{i=il(b.b,f.e)}catch(a){a=Ch(a);if(de(a,6)){return false}else throw Dh(a)}}bA(b,f,i)}if(!e||b.c>0||b.o||!b.n)return false;j=tk(c,e.e);h=kj();h.open('GET',j,true);jj(h,(vj(),tj).a);ij(h,new yA(b,e));h.send(null);b.o=true;return true}
function In(a,b,c,d,e,f){var g,h,i,j,k,l,m;l=3*f;(e<c||e>d)&&(Ek(),Dk);k=e;h=i=0;j=e+f>=d?0:a[b+e+f];for(;k+l<d;k=k+l-f){g=h;h=i;i=j;j=a[b+k+l];a[b+k]=a[b+k]-(9*(h+i)-(g+j)+16>>5)}for(;k<d;k=k+f+f){g=h;h=i;i=j;j=0;a[b+k]=a[b+k]-(9*(h+i)-g+16>>5)}k=e+f;h=a[b+k-f];i=k+f>=d?0:a[b+k+f];j=k+l>=d?0:a[b+k+l];if(k<d){m=h;k+f<d&&(m=h+i+1>>1);a[b+k]=a[b+k]+m;k=k+f+f}for(;k+l<d;k=k+l-f){g=h;h=i;i=j;j=a[b+k+l];m=9*(h+i)-(g+j)+8>>4;a[b+k]=a[b+k]+m}if(k+f<d){h=i;i=j;j=0;m=h+i+1>>1;a[b+k]=a[b+k]+m;k=k+f+f}if(k<d){h=i;a[b+k]=a[b+k]+h}}
function jl(a,b){var c,d,e,f,g;if(b==null){throw Dh(new os('Can not find blank name.'))}g=$d(av(a.b,b),17);c=a.c;if(!g){f=c.c;d=qk(a.c,b);if(!d){if(f==null){throw Dh(new os('Requested data outside document'))}g=new ik;$j(g,dq(f,b));(!de(g.d,22)||!!$d(g.d,22).a)&&kl(a,g,0,b)}else if(a.d){g=new jk(a.d);hk(g,d.c);gk(g,d.d);dv(a.b,b,g)}else if(f!=null){g=$j(new ik,dq(f,b));dv(a.b,b,g)}}if(c.a.b.a.length>0&&Qq(c.a,0)!=null&&$d(Qq(c.a,0),21).c==0&&(!de(g.d,22)||!!$d(g.d,22).a)){e=Yj(g);if(!e||!cn(e)){throw Dh(new os(gC))}}return g}
function Cn(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w;h=pi(a.c*a.a);o=new Int16Array(EB);q=0;f=a.b;g=0;for(j=0;j<a.a;j+=32,q+=32*a.c){for(m=0;m<a.c;m+=32){on(f[g],o,64);++g;s=q+m;for(l=0,p=0;l++<32;p+=32,s+=a.c){v=o.subarray(p,p+32);h.set(v,s)}}}if(e){Hn(h,0,a.e,a.d,a.c,32,2);q=0;for(k=0;k<a.a;k+=2,q+=a.c){for(n=0;n<a.c;n+=2,q+=2){u=h[q];h[q+a.c]=u;h[q+a.c+1]=u;h[q+1]=u}}}else{Hn(h,0,a.e,a.d,a.c,32,1)}q=0;for(i=0,t=b;i++<a.d;t+=d,q+=a.c){for(m=0,r=t;m<a.e;r+=4){w=h[q+m++]+32>>6;w<-128?(w=-128):w>127&&(w=127);c[r]=w}}}
function Mr(a){var b,c;for(c=0;c<(a.c?a.d.b.a.length-1:a.d.b.a.length);){$d(Qq(a.d,c%a.d.b.a.length),12)[0]==$d(Qq(a.d,(c+1)%a.d.b.a.length),12)[0]&&$d(Qq(a.d,c%a.d.b.a.length),12)[1]==$d(Qq(a.d,(c+1)%a.d.b.a.length),12)[1]?Tq(a.d,c%a.d.b.a.length):++c}for(b=0;b<(a.c?a.d.b.a.length-1:a.d.b.a.length);){(a.c&&b+1<(a.c?a.d.b.a.length-1:a.d.b.a.length)||!a.c)&&Or($d(Qq(a.d,b%a.d.b.a.length),12),$d(Qq(a.d,(b+1)%a.d.b.a.length),12),$d(Qq(a.d,(b+1)%a.d.b.a.length),12),$d(Qq(a.d,(b+2)%a.d.b.a.length),12))?Tq(a.d,(b+1)%a.d.b.a.length):++b}}
function ho(a,b,c,d){var e,f,g,h,i,j,k;if(!a.v){throw Dh(new lt(rC))}e=0;f=0;h=0;if(a.u){f=(mB&b.b)+1;e=(mB&b.a)+1;h=f+d-1}g=no(a,a.L);if(g){j=bo(a,sC,tC,a.N,0);k=bo(a,sC,tC,a.P,0);if(!a.u){f=a.H+j;i=a.G+k;h=f+d-1;e=i-c+1}a.H=f;a.F=h;a.D=a.G=e;a.U[0]=a.U[1]=a.U[2]=e;a.V=0}else{j=bo(a,sC,tC,a.M,0);k=bo(a,sC,tC,a.O,0);if(!a.u){f=a.F+j;e=a.D+k;h=f+d-1}a.F=h;a.D=(++a.V==3&&(a.V=0),a.U[a.V]=e,a.U[0]>=a.U[1]?a.U[0]>a.U[2]?a.U[1]>=a.U[2]?a.U[1]:a.U[2]:a.U[0]:a.U[0]<a.U[2]?a.U[1]>=a.U[2]?a.U[2]:a.U[1]:a.U[0])}if(!a.u){b.a=e-1<<16>>16;b.b=f-1<<16>>16}}
function Lr(a){var b,c,d,e;e=a.b;if(e==null){d=a.d.b.a.length;if(d<2||d<3&&!a.c){a.b=(Bs(),Bs(),false);return false}for(b=0;b<(a.c?a.d.b.a.length-1:a.d.b.a.length);b++){for(c=b+2;c<(a.c?a.d.b.a.length-1:a.d.b.a.length);c++){if(b!=(c+1)%a.d.b.a.length){if(Pr($d(Qq(a.d,b%a.d.b.a.length),12)[0],$d(Qq(a.d,b%a.d.b.a.length),12)[1],$d(Qq(a.d,(b+1)%a.d.b.a.length),12)[0],$d(Qq(a.d,(b+1)%a.d.b.a.length),12)[1],$d(Qq(a.d,c%a.d.b.a.length),12),$d(Qq(a.d,(c+1)%a.d.b.a.length),12))){a.b=(Bs(),Bs(),false);return false}}}}e=a.b=(Bs(),Bs(),true)}return Uy(e),e}
function ml(a,b){var c,d,e,f,g,h,i,j,k;e=a.c;e.c=null;j=$j(new ik,b);h=Yj(j);if(!h||!cn(h)){throw Dh(new os(iC))}g=en(h);if(Xt('FORM:DJVM',g.b)){f=Yj(g);if(!f||!cn(f)){throw Dh(new os(gC))}d=en(f);if(!Xt('DIRM',d.b)){throw Dh(new os(hC))}pk(e,d);if(e.a.b.a.length>0&&Qq(e.a,0)!=null&&$d(Qq(e.a,0),21).c==0){c=fl(a);if(c){while(cn(f)){d=en(f);Xt('NAVM',d.b)&&c.ob(Zj(new ik,Ij(new Kj,d)))}}fv(a.b)}else{ll(a,j)}}else{if(!_j(j)){throw Dh(new os(iC))}i=au(b,'.+/','');k=Yt(i,fu(63));k>0&&(i=i.substr(0,k));k=$t(i,fu(47));k>0&&(i=i.substr(k));kl(a,j,1,i)}e.c=b}
function Ld(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G;c=a.l&8191;d=a.l>>13|(a.m&15)<<9;e=a.m>>4&8191;f=a.m>>17|(a.h&255)<<5;g=(a.h&1048320)>>8;h=b.l&8191;i=b.l>>13|(b.m&15)<<9;j=b.m>>4&8191;k=b.m>>17|(b.h&255)<<5;l=(b.h&1048320)>>8;B=c*h;C=d*h;D=e*h;F=f*h;G=g*h;if(i!=0){C+=c*i;D+=d*i;F+=e*i;G+=f*i}if(j!=0){D+=c*j;F+=d*j;G+=e*j}if(k!=0){F+=c*k;G+=d*k}l!=0&&(G+=c*l);n=B&wB;o=(C&511)<<13;m=n+o;q=B>>22;r=C>>9;s=(D&262143)<<4;t=(F&31)<<17;p=q+r+s+t;v=D>>18;w=F>>5;A=(G&4095)<<8;u=v+w+A;p+=m>>22;m&=wB;u+=p>>22;p&=wB;u&=xB;return xd(m,p,u)}
function $c(a,b){var c,d,e,f,g,h,i,j,k;if(b.length==0){return a.gb(_A,YA,-1,-1)}k=du(b);Xt(k.substr(0,3),'at ')&&(k=k.substr(3));k=k.replace(/\[.*?\]/g,'');g=k.indexOf('(');if(g==-1){g=k.indexOf('@');if(g==-1){j=k;k=''}else{j=du(k.substr(g+1));k=du(k.substr(0,g))}}else{c=k.indexOf(')',g);j=k.substr(g+1,c-(g+1));k=du(k.substr(0,g))}g=Yt(k,fu(46));g!=-1&&(k=k.substr(g+1));(k.length==0||Xt(k,'Anonymous function'))&&(k=YA);h=$t(j,fu(58));e=_t(j,fu(58),h-1);i=-1;d=-1;f=_A;if(h!=-1&&e!=-1){f=j.substr(0,e);i=Vc(j.substr(e+1,h-(e+1)));d=Vc(j.substr(h+1))}return a.gb(f,k,i,d)}
function fo(a,b,c){var d,e,f,g,h,i;d=null;b=bo(a,0,11,a.s,0);f=null;switch(b){case 2:case 5:{a.u?(f=new Ro):(f=Qo(new Ro,-1));d=f.a;break}}switch(b){case 0:{uo(a);no(a,a.t);a.u||jo(a,c);break}case 2:{qo(a,d,4);eo(a,d);break}case 5:{h=bo(a,0,a.J.b.a.length-1,a.r,0);a.u||(f.b=Hp($d(Qq(a.J,h),15)));e=Do(c,f.b).a;g=$d(Qq(a.K,h),9);xo(a,d,1+g.xmax-g.xmin,1+g.ymax-g.ymin);co(a,d,e,f.b);break}case 10:{to(a);break}case 9:{a.v?ko(a):wo(a,c);break}case 11:break;default:throw Dh(new jt('JB2Image bad type'));}if(!a.u){switch(b){case 2:case 5:{i=Bo(c,f);ao(a,i,f);break}}}return b}
function yd(a,b,c){var d,e,f,g,h,i;if(b.l==0&&b.m==0&&b.h==0){throw Dh(new us)}if(a.l==0&&a.m==0&&a.h==0){c&&(ud=xd(0,0,0));return xd(0,0,0)}if(b.h==yB&&b.m==0&&b.l==0){return zd(a,c)}i=false;if(b.h>>19!=0){b=Md(b);i=true}g=Fd(b);f=false;e=false;d=false;if(a.h==yB&&a.m==0&&a.l==0){e=true;f=true;if(g==-1){a=wd((Vd(),Rd));d=true;i=!i}else{h=Pd(a,g);i&&Dd(h);c&&(ud=xd(0,0,0));return h}}else if(a.h>>19!=0){f=true;a=Md(a);d=true;i=!i}if(g!=-1){return Ad(a,g,i,f,c)}if(Jd(a,b)<0){c&&(f?(ud=Md(a)):(ud=xd(a.l,a.m,a.h)));return xd(0,0,0)}return Bd(d?a:xd(a.l,a.m,a.h),b,i,f,e,c)}
function rm(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t;if(!b){return}s=Ct(d+b.k,a.k);d>0&&(s-=d);r=Ct(c+b.i,a.i);c>0&&(r-=c);if(s<=0||r<=0){return}m=b.a-1;n=Bm(m);j=255&e.d.e[e.c+e.e]<<24>>24;i=255&e.d.e[e.c+e.b]<<24>>24;h=255&e.d.e[e.c+e.a]<<24>>24;o=(d<0?-d:0)*b.c+b.border-(c<0?c:0);f=(d>0?d*a.i+a.border:0)+(c>0?c:0);g=new bm(a,f);for(t=0;t<s;t++){g.c=f*ql;for(q=0;q<r;g.c+=ql){p=xl(b,o+q++);if(p!=0){if(p>=m){Yl(g,h,i,j)}else{k=n[p];l=lB-k;Yl(g,Ct((255&g.d.e[g.c+g.a]<<24>>24)*k+h*l>>16,255),Ct((255&g.d.e[g.c+g.b]<<24>>24)*k+i*l>>16,255),Ct((255&g.d.e[g.c+g.e]<<24>>24)*k+j*l>>16,255))}}}f+=a.i;o+=b.c}}
function Hm(a,b,c){var d;d=new Xm;if(b.xmin<0||b.ymin<0||b.xmax>a.b||b.ymax>a.a){throw Dh(new jt('desired rectangle too big: '+b.xmin+','+b.ymin+','+b.xmax+','+b.ymax+','+a.b+','+a.a))}a.o==null&&Lm(a,0,0);a.c==null&&Km(a,0,0);c.xmin=a.c[b.xmin]>>4;c.ymin=a.o[b.ymin]>>4;c.xmax=a.c[b.xmax-1]+16-1>>4;c.ymax=a.o[b.ymax-1]+16-1>>4;c.xmin=c.xmin>0?c.xmin:0;c.xmax=c.xmax<a.j?c.xmax+1:a.j;c.ymin=c.ymin>0?c.ymin:0;c.ymax=c.ymax<a.i?c.ymax+1:a.i;d.xmin=c.xmin<<a.p;d.xmin<0&&(d.xmin=0);d.xmax=c.xmax<<a.p;d.xmax>a.n&&(d.xmax=a.n);d.ymin=c.ymin<<a.q;d.ymin<0&&(d.ymin=0);d.ymax=c.ymax<<a.q;d.ymax>a.k&&(d.ymax=a.k);return d}
function ll(a,b){var c,d,e,f,g,h,i,j,k,l,m,n;f=a.c;f.c=null;m=Yj(b);if(!m||!cn(m)){throw Dh(new os(gC))}k=en(m);if(!Xt('FORM:DJVM',k.b)){kl(a,b,1,'noname.djvu');return}j=Yj(k);if(!!j&&!cn(j)){throw Dh(new os(gC))}e=en(j);if(!Xt('DIRM',e.b)){throw Dh(new os(hC))}pk(f,e);fv(a.b);if(f.a.b.a.length>0&&Rq(f.a,0)!=null&&$d(Rq(f.a,0),21).c==0){throw Dh(new os('Cannot read indirect chunk.'))}a.d=b;i=f.a;for(l=0;l<i.b.a.length;l++){g=(_q(l,i.b.a.length),$d(yw(i.b,l),21));h=new jk(b);hk(h,g.c);gk(h,g.d);dv(a.b,g.b,h)}c=fl(a);while(cn(j)){d=en(j);n=d.b;if(n==null||Xt(n.substr(0,4),'FORM')){break}Xt(n,'NAVM')&&c.ob(Zj(new ik,Ij(new Kj,d)))}}
function bo(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o;i=false;f=0;h=d.a;if(h>=a.k.b.a.length){throw Dh(new ys('JB2Image bad numcontext'))}for(k=1,n=-1;n!=1;h=d.a){if(h==0){h=a.k.b.a.length;d.a=h;j=new Qj;l=new Jp;m=new Jp;Pq(a.k,j);Pq(a.I,l);Pq(a.S,m);g=a.u?b<f&&c>=f?kq(a.d,j)!=0:e>=f:b>=f||c>=f&&kq(a.d,j)!=0;d=g?m:l}else{g=a.u?b<f&&c>=f?no(a,$d(Qq(a.k,h),19)):e>=f:b>=f||c>=f&&no(a,$d(Qq(a.k,h),19));d=g?$d(Qq(a.S,h),16):$d(Qq(a.I,h),16)}switch(k){case 1:{i=!g;if(i){a.u&&(e=-e-1);o=-b-1;b=-c-1;c=o}k=2;f=1;break}case 2:{if(g){f=2*f+1}else{k=3;n=(f+1)/2|0;n==1?(f=0):(f-=n/2|0)}break}case 3:{n=n/2|0;n!=1?g?(f+=n/2|0):(f-=n/2|0):g||--f;break}}}return i?-f-1:f}
function vr(a,b){var c,d,e,f,g,h,i;h=xr(a,b.a);c=a.charCodeAt(h);switch(c){case 40:{b.a=h+1;return new yr(0,null)}case 41:{b.a=h+1;return new yr(1,null)}case 45:case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:{e=h+1;for(;e<a.length;e++){if(!Is(a.charCodeAt(e))){break}}b.a=e;return new yr(2,new ot(a.substr(h,e-h)))}case 34:{i=new ku;for(f=0;h<a.length-1;i.a+=String.fromCharCode(d),i){d=Tt(a,++h);if(d==92){++f}else if(d==34){rs(i,i.a.length-((f+1)/2|0));if((f&1)==0){break}f=0}else{f=0}}b.a=h+1;return new yr(2,i.a)}default:{g=h+1;for(;g<a.length;g++){d=a.charCodeAt(g);if(d==41||Js(d))break}b.a=g;return new yr(2,new zr(a.substr(h,g-h)))}}}
function wl(a){var b,c,d,e,f,g,h,i,j,k,l;h=a.i;b=a.k;f=a.c;for(i=h-1;i>=0;i--){c=a.border+i;d=c+f*b;while(c<d&&(c<a.border||c>=a.b||(g=c>=0?a.e[c*ql+3]:0,((g*(a.a-1)+(a.a-2))/255|0)==0))){c+=f}if(c<d){break}}for(k=b-1;k>=0;k--){c=k*a.c+a.border;d=c+h;while(c<d&&(c<a.border||c>=a.b||(g=c>=0?a.e[c*ql+3]:0,((g*(a.a-1)+(a.a-2))/255|0)==0))){++c}if(c<d){break}}for(j=0;j<=i;j++){c=a.border+j;d=c+f*b;while(c<d&&(c<a.border||c>=a.b||(g=c>=0?a.e[c*ql+3]:0,((g*(a.a-1)+(a.a-2))/255|0)==0))){c+=f}if(c<d){break}}for(l=0;l<=k;l++){c=l*a.c+a.border;d=c+h;while(c<d&&(c<a.border||c>=a.b||(g=c>=0?a.e[c*ql+3]:0,((g*(a.a-1)+(a.a-2))/255|0)==0))){++c}if(c<d){break}}e=new Xm;e.xmin=j;e.xmax=i;e.ymin=l;e.ymax=k;return e}
function ms(a,b,c,d,e){var f,g,h,i,j,k;a.e=b.kb();if(a.e<1||a.e>7){throw Dh(new os(DC))}a.xmin=b.kb()<<8;a.xmin|=b.kb();a.xmin-=DB;a.ymin=b.kb()<<8;a.ymin|=b.kb();a.ymin-=DB;i=b.kb()<<8;i|=b.kb();i-=DB;f=b.kb()<<8;f|=b.kb();f-=DB;a.d=b.kb()<<8;a.d|=b.kb();a.d-=DB;a.c=b.kb()<<16;a.c|=b.kb()<<8;a.c|=b.kb();if(e){if(a.e==1||a.e==4||a.e==5){a.xmin+=e.xmin;a.ymin=e.ymin-(a.ymin+f)}else{a.xmin+=e.xmax;a.ymin+=e.ymin}a.d+=e.d+e.c}else if(d){a.xmin+=d.xmin;a.ymin=d.ymax-(a.ymin+f);a.d+=d.d}a.xmax=a.xmin+i;a.ymax=a.ymin+f;h=b.kb()<<16;h|=b.kb()<<8;h|=b.kb();if(a.xmin>=a.xmax||a.ymin>=a.ymax||a.d<0||a.d+a.c>c){throw Dh(new os(DC))}g=null;a.b.array=[];while(h-->0){j=(k=new ns,k.e=a.e,k.a=a.a,up(a.b,k),k);ms(j,b,c,a,g);g=j}}
function Gp(a){var b,c,d,e,f,g,h,i,j,k,l;if(a==null){throw Dh(new Ot(jB))}j=a;f=a.length;i=false;if(f>0){b=a.charCodeAt(0);if(b==45||b==43){a=a.substr(1);--f;i=b==45}}if(f==0){throw Dh(new Ot(kB+j+'"'))}while(a.length>0&&a.charCodeAt(0)==48){a=a.substr(1);--f}if(f>(Nt(),Lt)[16]){throw Dh(new Ot(kB+j+'"'))}for(e=0;e<f;e++){if(Hs(a.charCodeAt(e),16)==-1){throw Dh(new Ot(kB+j+'"'))}}l=0;g=Jt[16];k=Kt[16];h=Qh(Mt[16]);c=true;d=f%g;if(d>0){l=-az(a.substr(0,d),16);a=a.substr(d);f-=d;c=false}while(f>=g){d=az(a.substr(0,g),16);a=a.substr(g);f-=g;if(c){c=false}else{if(Gh(l,h)<0){throw Dh(new Ot(kB+j+'"'))}l=Ph(l,k)}l=Th(l,d)}if(Gh(l,0)>0){throw Dh(new Ot(kB+j+'"'))}if(!i){l=Qh(l);if(Gh(l,0)<0){throw Dh(new Ot(kB+j+'"'))}}return l}
function Kn(a,b){var c,d,e,f,g,h,i,j;if(!a.j){a.i=a.g=0;a.k=null}if(ak(b)!=a.g){throw Dh(new os('(IWPixmap::decode) Chunk does not bear expected serial number'))}h=a.i+ak(b);if(a.g==0){f=ak(b);g=ak(b);if((f&127)!=1){throw Dh(new os('(IWPixmap::decode) File has been compressed with an incompatible IWCodec'))}if(g>2){throw Dh(new os('(IWPixmap::decode) File has been compressed with a more recent IWCodec'))}i=ak(b)<<8;i|=ak(b);e=ak(b)<<8;e|=ak(b);a.c=0;a.d=false;c=ak(b);g>=2&&(a.c=127&c);g>=2&&(a.d=(128&c)==0);(f&128)!=0&&(a.c=-1);a.k=En(new Gn,i,e);a.j=xn(new An,a.k);if(a.c>=0){a.b=En(new Gn,i,e);a.f=En(new Gn,i,e);a.a=xn(new An,a.b);a.e=xn(new An,a.f)}}j=nq(new qq,b);for(d=1;d!=0&&a.i<h;a.i++){d=vn(a.j,j);if(!!a.e&&!!a.a&&a.c<=a.i){d|=vn(a.a,j);d|=vn(a.e,j)}}++a.g}
function Ei(a){switch(a){case 'blur':return 4096;case 'change':return EB;case 'click':return 1;case 'dblclick':return 2;case 'focus':return 2048;case 'keydown':return 128;case 'keypress':return 256;case 'keyup':return 512;case 'load':return DB;case 'losecapture':return 8192;case 'mousedown':return 4;case 'mousemove':return 64;case 'mouseout':return 32;case 'mouseover':return 16;case 'mouseup':return 8;case 'scroll':return 16384;case FB:return lB;case 'DOMMouseScroll':case 'mousewheel':return GB;case 'contextmenu':return HB;case 'paste':return yB;case 'touchstart':return IB;case 'touchmove':return JB;case 'touchend':return AB;case 'touchcancel':return 8388608;case 'gesturestart':return KB;case 'gesturechange':return LB;case 'gestureend':return MB;default:return -1;}}
function Im(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C;b<c.ymin?(b=c.ymin):b>=c.ymax&&(b=c.ymax-1);if(b==a.e){return new bm(a.g,0)}if(b==a.d){return new bm(a.f,0)}n=a.f;a.f=a.g;a.d=a.e;a.g=n;a.e=b;m=new Xm;m.xmin=c.xmin<<a.p;m.xmax=c.xmax<<a.p;m.ymin=b<<a.q;m.ymax=b+1<<a.q;Sm(m,m,d);Vm(m,-d.xmin,-d.ymin);g=sl(e,m.ymin);r=e.i;t=1<<a.p;h=a.p+a.q;p=1<<h-1;q=p+p;k=new bm(e,0);l=new bm(n,0);for(C=m.xmin;C<m.xmax;C+=t,l.c+=ql){o=0;i=0;f=0;s=0;j=g+C;B=m.ymax-m.ymin;A=1<<a.q;A>B&&(A=B);for(w=0;w<A;++w,j+=r){v=C+t;k.c=j*ql;v>m.xmax&&(v=m.xmax);for(u=v-C;u-->0;++s,k.c+=ql){o+=255&k.d.e[k.c+k.e]<<24>>24;i+=255&k.d.e[k.c+k.b]<<24>>24;f+=255&k.d.e[k.c+k.a]<<24>>24}}s==q?Yl(l,f+p>>h,i+o>>h,o+p>>h):Yl(l,(f+(s/2|0))/2|0,(i+(s/2|0))/s|0,(o+(s/2|0))/s|0)}return new bm(a.g,0)}
function Pk(a){var b,c,d,e,f,g,h,i,j,k,l;if(a.o){e=yo(a.o);e&&(a.o=null);return false}if(a.u!=null){j=$d(av(Fk,a.u),38);if(j){for(h=j.Eb();h.Rb();){g=$d(h.Sb(),77);Zk(a,g.b,g.a)}}else{i=ol(a,a.u);if(!Mk(a,i))return false;if(!a.f){a.f=new Jw;dv(Fk,a.u,a.f)}}a.u=null}d=a.d;while(d.a.length!=0&&!cn($d(yw(d,d.a.length-1),66))){Cw(d,d.a.length-1);d.a.length==1&&(a.f=null)}if(d.a.length==0){if(a.s){Yk(a,new jk(a.s))}else{Lk(a);return true}}c=en($d(yw(d,d.a.length-1),66));if(Xt(a.p,eC)){l=d.a.length>1;Kk(a,c,l)}else if(Xt(a.p,fC)){if(Xt('PM44',c.b)||Xt('BM44',c.b)){f=$d(Sk(a,a.c),47);if(!f){f=new Mn;Kn(f,c);k=new Ak;k.width=f.k?f.k.e:0;k.height=f.k?f.k.d:0;k.dpi=100;Zk(a,a.n,k);Zk(a,a.c,f)}else{Kn(f,c)}}else if(Xt('ANTa',c.b)||Xt('ANTz',c.b)){b=Sk(a,a.b);!b&&(b=new Gq);Hk(a,a.b,b,c)}}return false}
function BA(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o;k=a.c+b;if(k<0||k>=gA(a.f))return false;i=fA(a.f,k);if(!i)return false;a.j.a=k;a.j.b=a.e;j=$d(av(i.e,i.n),25);g=le($wnd.Math.ceil(j.width/a.e/a.n))-1;h=le($wnd.Math.ceil(j.height/a.e/a.n))-1;e=(Wm(a.d)+1)/2|0;f=(Qm(a.d)+1)/2|0;for(d=1;d<=e;d++){l=a.d.xmax+d;for(o=a.d.ymin;o<=a.d.ymax+(d<f?d:f);o++){if(l>=0&&l<=g&&o>=0&&o<=h&&EA(a,NA(a.j,l,o),i,true))return true}l=a.d.ymin-d;for(n=a.d.ymin-(d<f?d:f);n<=a.d.ymax;n++){if(l>=0&&l<=g&&n>=0&&n<=h&&EA(a,NA(a.j,l,n),i,true))return true}}for(c=1;c<=f;c++){n=a.d.ymax+c;for(m=a.d.xmin;m<=a.d.xmax+(c<e?c:e);m++){if(m>=0&&m<=g&&n>=0&&n<=h&&EA(a,NA(a.j,m,n),i,true))return true}n=a.d.ymin-c;for(l=a.d.xmin-(c<e?c:e);l<=a.d.xmax;l++){if(l>=0&&l<=g&&n>=0&&n<=h&&EA(a,NA(a.j,l,n),i,true))return true}}return false}
function Zx(){function e(){this.obj=this.createObject()}
;e.prototype.createObject=function(a){return Object.create(null)};e.prototype.get=function(a){return this.obj[a]};e.prototype.set=function(a,b){this.obj[a]=b};e.prototype[JC]=function(a){delete this.obj[a]};e.prototype.keys=function(){return Object.getOwnPropertyNames(this.obj)};e.prototype.entries=function(){var b=this.keys();var c=this;var d=0;return {next:function(){if(d>=b.length)return {done:true};var a=b[d++];return {value:[a,c.get(a)],done:false}}}};if(!Xx()){e.prototype.createObject=function(){return {}};e.prototype.get=function(a){return this.obj[':'+a]};e.prototype.set=function(a,b){this.obj[':'+a]=b};e.prototype[JC]=function(a){delete this.obj[':'+a]};e.prototype.keys=function(){var a=[];for(var b in this.obj){b.charCodeAt(0)==58&&a.push(b.substring(1))}return a}}return e}
function so(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v;while(d>=0){h=(l=e-1>=0?b.e[(e-1)*ql+3]:0,((l*(b.a-1)+(b.a-2))/255|0)<<9|(m=e>=0?b.e[e*ql+3]:0,((m*(b.a-1)+(b.a-2))/255|0)<<8)|(o=e+1>=0?b.e[(e+1)*ql+3]:0,((o*(b.a-1)+(b.a-2))/255|0)<<7)|(p=f-2>=0?b.e[(f-2)*ql+3]:0,((p*(b.a-1)+(b.a-2))/255|0)<<6)|(q=f-1>=0?b.e[(f-1)*ql+3]:0,((q*(b.a-1)+(b.a-2))/255|0)<<5)|(r=f>=0?b.e[f*ql+3]:0,((r*(b.a-1)+(b.a-2))/255|0)<<4)|(s=f+1>=0?b.e[(f+1)*ql+3]:0,((s*(b.a-1)+(b.a-2))/255|0)<<3)|(t=f+2>=0?b.e[(f+2)*ql+3]:0,((t*(b.a-1)+(b.a-2))/255|0)<<2)|(u=g-2>=0?b.e[(g-2)*ql+3]:0,((u*(b.a-1)+(b.a-2))/255|0)<<1)|(v=g-1>=0?b.e[(g-1)*ql+3]:0,(v*(b.a-1)+(b.a-2))/255|0));for(i=0;i<c;){j=oo(a,a.n,h);Bl(b,g+i++,j);h=h<<1&890|(n=f+i+2>=0?b.e[(f+i+2)*ql+3]:0,((n*(b.a-1)+(b.a-2))/255|0)<<2)|(k=e+i+1>=0?b.e[(e+i+1)*ql+3]:0,((k*(b.a-1)+(b.a-2))/255|0)<<7)|j}e=f;f=g;g=sl(b,--d)}}
function Ai(){var a,b,c;b=$doc.compatMode;a=sd(kd(vg,1),$A,2,6,[CB]);for(c=0;c<a.length;c++){if(Xt(a[c],b)){return}}a.length==1&&Xt(CB,a[0])&&Xt('BackCompat',b)?"GWT no longer supports Quirks Mode (document.compatMode=' BackCompat').<br>Make sure your application's host HTML page has a Standards Mode (document.compatMode=' CSS1Compat') doctype,<br>e.g. by using &lt;!doctype html&gt; at the start of your application's HTML page.<br><br>To continue using this unsupported rendering mode and risk layout problems, suppress this message by adding<br>the following line to your*.gwt.xml module file:<br>&nbsp;&nbsp;&lt;extend-configuration-property name=\"document.compatMode\" value=\""+b+'"/&gt;':"Your *.gwt.xml module configuration prohibits the use of the current document rendering mode (document.compatMode=' "+b+"').<br>Modify your application's host HTML page doctype, or update your custom "+"'document.compatMode' configuration property settings."}
function Qk(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A;j=$d(av(a.e,a.n),25);w=!j?0:j.width;h=!j?0:j.height;if(w<=0||h<=0||!j){return null}f=1;e=$d(Sk(a,a.c),83);if(e){v=e.Cb();g=e.zb();if(v==0||g==0||w==0||h==0){return null}u=_k(w,h,v,g);if(u<1||u>12){return null}if(c==u){q=e.Bb(1,b,d)}else if(c==2*u){q=e.Bb(2,b,d)}else if(c==4*u){q=e.Bb(4,b,d)}else if(c==8*u){q=e.Bb(8,b,d)}else if(u*4==c*3){A=new Xm;A.xmin=le($wnd.Math.floor(b.xmin*4/3));A.ymin=le($wnd.Math.floor(b.ymin*4/3));A.xmax=le($wnd.Math.ceil(b.xmax*4/3));A.ymax=le($wnd.Math.ceil(b.ymax*4/3));n=new Ym(0,0,b.xmax-b.xmin,b.ymax-b.ymin);A.xmax>v&&(A.xmax=v);A.ymax>g&&(A.ymax=g);m=e.Bb(1,A,null);q=d?d:new wm;sm(q,m,n)}else{r=16;while(r>1&&c<r*u){r>>=1}l=(v+r-1)/r|0;k=(g+r-1)/r|0;p=(w+c-1)/c|0;o=(h+c-1)/c|0;s=new Mm(l,k,p,o);Km(s,u*r,c);Lm(s,u*r,c);A=(t=new Xm,Hm(s,b,t));m=e.Bb(r,A,null);q=d?d:new wm;Jm(s,A,m,b,q)}if(!!q&&f!=1){pm(q,f);for(i=0;i<9;i++){pm(q,f)}}return q}else{return null}}
function Oi(a,b){var c=(a.__eventBits||0)^b;a.__eventBits=b;if(!c)return;c&1&&(a.onclick=b&1?Ki:null);c&2&&(a.ondblclick=b&2?Ki:null);c&4&&(a.onmousedown=b&4?Ki:null);c&8&&(a.onmouseup=b&8?Ki:null);c&16&&(a.onmouseover=b&16?Ki:null);c&32&&(a.onmouseout=b&32?Ki:null);c&64&&(a.onmousemove=b&64?Ki:null);c&128&&(a.onkeydown=b&128?Ki:null);c&256&&(a.onkeypress=b&256?Ki:null);c&512&&(a.onkeyup=b&512?Ki:null);c&EB&&(a.onchange=b&EB?Ki:null);c&2048&&(a.onfocus=b&2048?Ki:null);c&4096&&(a.onblur=b&4096?Ki:null);c&8192&&(a.onlosecapture=b&8192?Ki:null);c&16384&&(a.onscroll=b&16384?Ki:null);c&DB&&(a.onload=b&DB?Li:null);c&lB&&(a.onerror=b&lB?Ki:null);c&GB&&(a.onmousewheel=b&GB?Ki:null);c&HB&&(a.oncontextmenu=b&HB?Ki:null);c&yB&&(a.onpaste=b&yB?Ki:null);c&IB&&(a.ontouchstart=b&IB?Ki:null);c&JB&&(a.ontouchmove=b&JB?Ki:null);c&AB&&(a.ontouchend=b&AB?Ki:null);c&8388608&&(a.ontouchcancel=b&8388608?Ki:null);c&KB&&(a.ongesturestart=b&KB?Ki:null);c&LB&&(a.ongesturechange=b&LB?Ki:null);c&MB&&(a.ongestureend=b&MB?Ki:null)}
function ro(a,b,c,d,e,f,g,h,i,j,k,l){var m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G,H;while(f>=0){m=(q=h-1>=0?b.e[(h-1)*ql+3]:0,((q*(b.a-1)+(b.a-2))/255|0)<<10|(r=h>=0?b.e[h*ql+3]:0,((r*(b.a-1)+(b.a-2))/255|0)<<9)|(w=h+1>=0?b.e[(h+1)*ql+3]:0,((w*(b.a-1)+(b.a-2))/255|0)<<8)|(A=i-1>=0?b.e[(i-1)*ql+3]:0,((A*(b.a-1)+(b.a-2))/255|0)<<7)|(B=j>=0?c.e[j*ql+3]:0,((B*(c.a-1)+(c.a-2))/255|0)<<6)|(C=k-1>=0?c.e[(k-1)*ql+3]:0,((C*(c.a-1)+(c.a-2))/255|0)<<5)|(D=k>=0?c.e[k*ql+3]:0,((D*(c.a-1)+(c.a-2))/255|0)<<4)|(F=k+1>=0?c.e[(k+1)*ql+3]:0,((F*(c.a-1)+(c.a-2))/255|0)<<3)|(G=l-1>=0?c.e[(l-1)*ql+3]:0,((G*(c.a-1)+(c.a-2))/255|0)<<2)|(H=l>=0?c.e[l*ql+3]:0,((H*(c.a-1)+(c.a-2))/255|0)<<1)|(s=l+1>=0?c.e[(l+1)*ql+3]:0,(s*(c.a-1)+(c.a-2))/255|0));for(n=0;n<e;){o=oo(a,a.o,m);Bl(b,i+n++,o);m=m<<1&1590|(t=h+n+1>=0?b.e[(h+n+1)*ql+3]:0,((t*(b.a-1)+(b.a-2))/255|0)<<8)|(u=j+n>=0?c.e[(j+n)*ql+3]:0,((u*(c.a-1)+(c.a-2))/255|0)<<6)|(v=k+n+1>=0?c.e[(k+n+1)*ql+3]:0,((v*(c.a-1)+(c.a-2))/255|0)<<3)|(p=l+n+1>=0?c.e[(l+n+1)*ql+3]:0,(p*(c.a-1)+(c.a-2))/255|0)|o<<7}h=i;i=sl(b,--f);j=k;k=l;l=sl(c,--g-1)+d}}
function vm(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D;t=new Ym(0,0,(c.i*d+e-1)/e|0,(c.k*d+e-1)/e|0);if(f.xmin<t.xmin||f.ymin<t.ymin||f.xmax>t.xmax||f.ymax>t.ymax){throw Dh(new jt('rectangle out of boundsbounds=('+f.xmin+','+f.ymin+','+f.xmax+','+f.ymax+'),rect=('+t.xmin+','+t.ymin+','+t.xmax+','+t.ymax+')'))}C=Ct(Ct(a.k,b.k),f.ymax-f.ymin);B=Ct(Ct(a.i,b.i),f.xmax-f.xmin);r=b.a-1;p=ym(g);s=d/e;n=f.ymin*e/d|0;o=f.ymin-s*n;if(o<0){--n;o+=s}m=f.xmin*e/d|0;l=f.xmin-s*m;if(l<0){--m;l+=s}i=n*c.i+c.border;j=new bm(c,0);h=new bm(a,0);for(D=0;D<C;D++){j.c=(i+m)*ql;k=l;h.c=sl(h.d,D)*ql;u=D*b.c+b.border;for(A=0;A<B;++A,h.c+=ql){v=(w=u+A>=0?b.e[(u+A)*ql+3]:0,(w*(b.a-1)+(b.a-2))/255|0);if(v>0){if(v>=r){Yl(h,p[255&j.d.e[j.c+j.a]<<24>>24],p[255&j.d.e[j.c+j.b]<<24>>24],p[255&j.d.e[j.c+j.e]<<24>>24])}else{q=lB*v/r|0;Yl(h,(255&h.d.e[h.c+h.a]<<24>>24)*(lB-q)+q*p[255&j.d.e[j.c+j.a]<<24>>24]>>16,(255&h.d.e[h.c+h.b]<<24>>24)*(lB-q)+q*p[255&j.d.e[j.c+j.b]<<24>>24]>>16,(255&h.d.e[h.c+h.e]<<24>>24)*(lB-q)+q*p[255&j.d.e[j.c+j.e]<<24>>24]>>16)}}if(++k>=s){k-=s;j.c+=ql}}if(++o>=s){o-=s;i+=c.i}}}
function Kk(b,c,d){var e,f,g,h,i;g=c.b;if(Xt(g,'Djbz')){Hk(b,b.g,new Fo,c);!!b.s&&Yk(b,new jk(b.s))}else if(Xt(g,'ANTa')||Xt(g,'ANTz')){e=Sk(b,b.b);!e&&(e=new Gq);Hk(b,b.b,e,c)}else if(!d){if(Xt(g,'INFO')){throw Dh(new lt('DjVu Decoder: Corrupted file (Duplicate INFO chunk)'))}else if(Xt(g,'INCL')){b.u=ek(c)}else if(Xt(g,'FGbz')){if(b.k){throw Dh(new lt('Duplicate foreground'))}Hk(b,b.j,new Pp,c)}else if(Xt(g,aC)||Xt(g,bC)){Hk(b,b.t,new is,c)}else if(Xt(g,'Sjbz')){if(b.s){throw Dh(new lt('Duplicate Sjbz chunk'))}try{c.d.jb(eB);Yk(b,c)}catch(a){a=Ch(a);if(de(a,10)){h=a;if(Vk(b,b.g)){throw Dh(h)}c.d.mb();b.s=c}else throw Dh(a)}}else if(Xt(g,'BG44')){f=Sk(b,b.c);!f&&(f=new Mn);Hk(b,b.c,f,c)}else if(Xt(g,'FG44')){if(Vk(b,b.j)||!!b.k){throw Dh(new lt(cC))}i=new Mn;Kn(i,c);b.k=Ln(i)}else if(Xt(g,'BG2k')){if(Vk(b,b.c)){throw Dh(new lt(dC))}}else if(Xt(g,'FG2k')){if(!!b.k||Vk(b,b.j)){throw Dh(new lt(cC))}}else if(Xt(g,'Smmr')){if(Vk(b,b.i)){throw Dh(new lt(dC))}}else if(Xt(g,'BGjp')){if(Vk(b,b.c)){throw Dh(new lt(dC))}Zk(b,b.c,Nk(c))}else if(Xt(g,'FGjp')){if(!!b.k||Vk(b,b.j)){throw Dh(new lt(cC))}b.k=Nk(c)}}}
function $k(a,b,c,d){var e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G,H,I;w=$d(av(a.e,a.n),25);if(!w){return false}H=w.width;s=w.height;if(H<=0||s<=0){return false}q=1;o=$d(av(a.e,a.i),41);if(o){p=$d(av(a.e,a.j),68);if(p){e=o.a.b.a.length;if(e!=p.a.length){h=Rk(a,c,d);qm(b,h);return false}l=p.b.length;k=tm(new wm,1,l,null);i=new bm(k,0);for(u=0;u<k.i;i.c+=ql){Op(p,u++,i)}pm(k,q);g=new Kw(l);for(v=0;v<l;v++){f=new Jw;g.a[g.a.length]=f}for(C=0;C<e;C++){B=$d(Qq(o.a,C),53);if(!No(o,B,c,d))continue;f=$d(yw(g,p.a[C]),38);f.add(B)}A=0;m=new Xm;n=new Jw;for(j=0;j<l;j++){f=(Ty(j,g.a.length),$d(g.a[j],38));if(!f)continue;i.c=j*ql;m.xmin=m.xmax=m.ymin=m.ymax=0;n.a=od(pg,$A,1,0,5,1);for(t=0;t<f.size();t++){B=$d(f.getAtIndex(t),53);if(A<B.b){rm(b,Ik(a,n,m,c,d),m.xmin-c.xmin,m.ymin-c.ymin,i);m.xmin=m.xmax=m.ymin=m.ymax=0;n.a=od(pg,$A,1,0,5,1)}A=B.b;D=Do(o,B.c);I=new Ym(B.b,B.a,D.a.i,D.a.k);Um(m,m,I);n.a[n.a.length]=B}rm(b,Ik(a,n,m,c,d),m.xmin-c.xmin,m.ymin-c.ymin,i)}return true}if(a.k){h=Rk(a,c,d);if(!!h&&!!b){G=a.k.i;r=a.k.k;F=_k(H,s,G,r);if(F<1||F>16){return false}vm(b,h,a.k,F,d,c,q);return true}}}return false}
function Jm(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G,H,I,J;F=new Xm;G=Hm(a,d,F);if(b.xmax-b.xmin!=c.i||b.ymax-b.ymin!=c.k){throw Dh(new jt(mC))}if(b.xmin>G.xmin||b.ymin>G.ymin||b.xmax<G.xmax||b.ymax<G.ymax){throw Dh(new lt(mC))}(d.xmax-d.xmin!=e.i||d.ymax-d.ymin!=e.k)&&tm(e,d.ymax-d.ymin,d.xmax-d.xmin,null);f=F.xmax-F.xmin;s=od(ef,$A,37,f+2,0,1);for(q=0;q<s.length;){s[q++]=new Ml}try{if(a.p>0||a.q>0){a.f=tm(new wm,1,f,null);a.g=tm(new wm,2,f,null);a.d=a.e=-1}for(J=d.ymin;J<d.ymax;J++){n=a.o[J];o=n>>4;p=o+1;if(a.p>0||a.q>0){u=Im(a,o,F,b,c);H=Im(a,p,F,b,c)}else{l=F.xmin-b.xmin;F.ymin>o&&(o=F.ymin);F.ymax<=p&&(p=F.ymax-1);u=new cm(c,o-b.ymin,l);H=new cm(c,p-b.ymin,l)}r=1;j=Fm[n&15];for(m=1+f;r<m;H.c+=ql,u.c+=ql){k=s[r++];C=255&u.d.e[u.c+u.e]<<24>>24;i=j[256+(255&H.d.e[H.c+H.e]<<24>>24)-C];B=255&u.d.e[u.c+u.b]<<24>>24;h=j[256+(255&H.d.e[H.c+H.b]<<24>>24)-B];A=255&u.d.e[u.c+u.a]<<24>>24;g=j[256+(255&H.d.e[H.c+H.a]<<24>>24)-A];k.vb(A+g,B+h,C+i)}s[0]=s[1];t=1-F.xmin;k=new cm(e,J-d.ymin,0);for(I=d.xmin;I<d.xmax;I++){D=a.c[I];u=t+(D>>4);v=s[u];w=s[u+1];j=Fm[D&15];C=255&v.ub();i=j[256+(255&w.ub())-C];B=255&v.tb();h=j[256+(255&w.tb())-B];A=255&v.sb();g=j[256+(255&w.sb())-A];Yl(k,A+g,B+h,C+i);k.c+=ql}}}finally{a.f=null;a.g=null}}
function wn(a,b,c,d,e,f){var g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A;A=a.k[c];h=0;n=a.b;l=0;for(k=0;k<f;){i=0;w=nn(d,e+k);if(!w){i=8}else{for(s=0;s<16;s++){o=n[l+s]&1;if(o==0){o=w.b.c[s+w.a]!=0?2:8;n[l+s]=o}i|=o}}a.a[k]=i;h|=i;++k;l+=16}f<16||(h&2)!=0?(h|=4):(h&8)!=0&&kq(b,a.e)!=0&&(h|=4);if((h&4)!=0){for(j=0;j<f;j++){if((a.a[j]&8)!=0){p=0;Ek();if(c>0){v=e+j<<2;g=nn(d,v>>4);if(g){v&=15;g.b.c[v+g.a]!=0&&++p;g.b.c[v+1+g.a]!=0&&++p;g.b.c[v+2+g.a]!=0&&++p;p<3&&g.b.c[v+3+g.a]!=0&&++p}}(h&2)!=0&&(p|=4);kq(b,a.c[c][p])!=0&&(a.a[j]=a.a[j]|4,undefined)}}}if((h&4)!=0){n=a.b;l=0;for(j=0;j<f;){if((a.a[j]&4)!=0){w=nn(d,e+j);if(!w){w=(d.b[e+j]=1,d.a.a=(e+j)*16,d.a);for(t=0;t<16;t++){(n[l+t]&1)==0&&(n[l+t]=8,undefined)}}q=0;Ek();for(u=0;u<16;u++){(n[l+u]&8)!=0&&++q}for(s=0;s<16;s++){if((n[l+s]&8)!=0){c==0&&(A=a.n[s]);q>=7?(p=7):(p=q);(a.a[j]&2)!=0&&(p|=8);if(kq(b,a.f[p])!=0){n[l+s]=n[l+s]|4;r=A>>1;m=A+r-(r>>2);iq(b,DB+(b.a+b.a+b.a>>3))!=0?qn(w,s,-m<<16>>16):qn(w,s,m<<16>>16)}(n[l+s]&4)!=0?(q=0):q>0&&--q}}}++j;l+=16}}if((h&2)!=0){n=a.b;l=0;for(j=0;j<f;){if((a.a[j]&2)!=0){w=nn(d,e+j);for(s=0;s<16;s++){if((n[l+s]&2)!=0){m=w.b.c[s+w.a];m<0&&(m=-m);c==0&&(A=a.n[s]);if(m<=3*A){m+=A>>2;kq(b,a.d)!=0?(m+=A>>1):(m=m-A+(A>>1))}else{iq(b,DB+(b.a+b.a+b.a>>3))!=0?(m+=A>>1):(m=m-A+(A>>1))}w.b.c[s+w.a]>0?qn(w,s,m<<16>>16):qn(w,s,-m<<16>>16)}}}++j;l+=16}}}
function pk(a,b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C;w=new jk(b);Vq(a.a);Vq(a.d);fv(a.b);C=ak(w);g=(C&128)!=0;C&=127;if(C>1){throw Dh(new os('DjVmDir.version_error 1 '+C))}j=ak(w);j=j<<8|ak(w);if(j==0)return;for(n=0;n<j;n++){i=new xk;Pq(a.a,i);if(g){c=ak(w);c=c<<8|ak(w);c=c<<8|ak(w);i.c=c<<8|ak(w);if(C==0){c=ak(w);c=c<<8|ak(w);i.d=c<<8|ak(w)}if(i.c==0){throw Dh(new os('DjVmDir.no_indirect'))}}else{i.c=i.d=0}}e=Ij(new Kj,w);if(C>0){for(o=0;o<a.a.b.a.length;o++){i=$d(Qq(a.a,o),21);c=Jj(e,e.g)==1?255&e.g[0]:-1;c=c<<8|(Jj(e,e.g)==1?255&e.g[0]:-1);c=c<<8|(Jj(e,e.g)==1?255&e.g[0]:-1);i.d=c}}for(p=0;p<a.a.b.a.length;p++){i=$d(Qq(a.a,p),21);i.a=(Jj(e,e.g)==1?255&e.g[0]:-1)<<24>>24}if(C==0){for(o=0;o<a.a.b.a.length;o++){i=$d(Qq(a.a,o),21);k=i.a;l=(k&1)!=0?1:0;(k&2)!=0&&(l=(l|-128)<<24>>24);(k&4)!=0&&(l=(l|64)<<24>>24);i.a=l}}if(a.a.b.a.length>0){d=new Vj;f=od(me,UB,8,EB,15,1);for(;(s=Jj(e,f))>0;){Uj(d,f,s)}f=hx(d.a,d.b);A=new Wq;v=0;h=0;for(;h<f.length;++h){if(f[h]==0){t=Wt(f,v,h-v,gu());vw(A.b,t);v=h+1}}v<h&&Pq(A,Wt(f,v,h-v,(Gy(),Fy)));for(o=0,B=0;o<a.a.b.a.length;o++){i=$d(Qq(a.a,o),21);i.b=be(Qq(A,B++));(i.a&-128)!=0&&++B;(i.a&64)!=0&&++B}}u=0;for(q=0;q<a.a.b.a.length;q++){i=$d(Qq(a.a,q),21);(i.a&63)==3&&++u}if(u>1){throw Dh(new os('DjVmDir.corrupt'))}Vq(a.d);for(r=0;r<a.a.b.a.length;r++){i=$d(Qq(a.a,r),21);(i.a&63)==1&&Pq(a.d,i)}for(m=0;m<a.a.b.a.length;m++){i=$d(Qq(a.a,m),21);if(bv(a.b,i.b)){throw Dh(new os('DjVmDir.dupl_id '+i.b))}dv(a.b,i.b,i)}}
function go(a,b,c){var d,e,f,g,h,i,j,k,l;d=null;g=null;f=null;b=bo(a,0,11,a.s,0);switch(b){case 1:case 3:case 4:case 6:case 8:{f=new Yn}case 2:case 5:{a.u?(g=new Ro):(g=Qo(new Ro,b==8?-2:-1));d=g.a;break}case 7:{f=new Yn;break}}k=false;j=false;switch(b){case 0:{vo(a,c);no(a,a.t);a.u||jo(a,c);break}case 1:{j=k=true;qo(a,d,4);eo(a,d);ho(a,f,d.k,d.i);break}case 2:{k=true;qo(a,d,4);eo(a,d);break}case 3:{j=true;qo(a,d,3);eo(a,d);ho(a,f,d.k,d.i);break}case 4:{j=true;k=true;i=bo(a,0,a.J.b.a.length-1,a.r,0);a.u||(g.b=Hp($d(Qq(a.J,i),15)));e=Do(c,g.b).a;h=$d(Qq(a.K,i),9);xo(a,d,1+h.xmax-h.xmin,1+h.ymax-h.ymin);co(a,d,e,i);ho(a,f,d.k,d.i);break}case 5:{k=true;i=bo(a,0,a.J.b.a.length-1,a.r,0);a.u||(g.b=Hp($d(Qq(a.J,i),15)));Do(c,g.b);h=$d(Qq(a.K,i),9);xo(a,d,1+h.xmax-h.xmin,1+h.ymax-h.ymin);break}case 6:{j=true;i=bo(a,0,a.J.b.a.length-1,a.r,0);a.u||(g.b=Hp($d(Qq(a.J,i),15)));e=Do(c,g.b).a;h=$d(Qq(a.K,i),9);xo(a,d,1+h.xmax-h.xmin,1+h.ymax-h.ymin);co(a,d,e,i);ho(a,f,d.k,d.i);break}case 7:{i=bo(a,0,a.J.b.a.length-1,a.r,0);a.u||Xn(f,Hp($d(Qq(a.J,i),15)));d=Do(c,f.c).a;h=$d(Qq(a.K,i),9);Vn(f,h.xmin);Tn(f,h.ymin);c.c?ho(a,f,d.k,d.i):ho(a,f,1+h.ymax-h.ymin,1+h.xmax-h.xmin);Wn(f,h.xmin);Un(f,h.ymin);break}case 8:{j=true;qo(a,d,3);eo(a,d);po(a,f,d.k);break}case 10:{to(a);break}case 9:{a.v?ko(a):wo(a,c);break}case 11:break;default:throw Dh(new jt('JB2Image unknown type'));}if(!a.u){switch(b){case 1:case 2:case 4:case 5:case 3:case 6:case 8:{l=Bo(c,g);lo(a,l,Zn);k&&ao(a,l,g);if(j){f.c=l;Jo(c,f)}break}case 7:{Jo(c,f);break}}}return b}
function Fj(a){var b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B;a.f=Hj(a);if(a.f==0){return 0}if(a.f>AB){throw Dh(new os(XB))}if(a.a<a.f){a.a=a.f;a.d=ti(a.a)}else !a.d&&(a.d=ti(a.a));i=0;if(jq(a.i)!=0){++i;jq(a.i)!=0&&++i}u=hx(Cj,Cj.length);h=od(oe,YB,8,4,15,1);for(k=0;k<4;h[k++]=0);f=4;v=3;t=-1;for(l=0;l<a.f;l++){d=2;2>v&&(d=v);e=0;switch(0){default:if(kq(a.i,a.c[d])!=0){v=0;a.d[l]=u[0];break}e+=3;if(kq(a.i,a.c[e+d])!=0){v=1;a.d[l]=u[1];break}e+=3;if(kq(a.i,a.c[e])!=0){v=2+Gj(a,e+1,1);a.d[l]=u[v];break}e+=2;if(kq(a.i,a.c[e])!=0){v=4+Gj(a,e+1,2);a.d[l]=u[v];break}e+=4;if(kq(a.i,a.c[e])!=0){v=8+Gj(a,e+1,3);a.d[l]=u[v];break}e+=8;if(kq(a.i,a.c[e])!=0){v=16+Gj(a,e+1,4);a.d[l]=u[v];break}e+=16;if(kq(a.i,a.c[e])!=0){v=32+Gj(a,e+1,5);a.d[l]=u[v];break}e+=32;if(kq(a.i,a.c[e])!=0){v=64+Gj(a,e+1,6);a.d[l]=u[v];break}e+=64;if(kq(a.i,a.c[e])!=0){v=128+Gj(a,e+1,7);a.d[l]=u[v];break}v=256;a.d[l]=0;t=l;continue;}f=f+(f>>i);if(f>268435456){f>>=24;h[0]>>=24;h[1]>>=24;h[2]>>=24;h[3]>>=24;r=4}g=f;v<4&&(g+=h[v]);for(r=v;r>=4;r--){u[r]=u[r-1]}for(;r>0&&Lh(Fh(ZB,g),Fh(ZB,h[r-1]));r--){u[r]=u[r-1];h[r]=h[r-1]}u[r]=a.d[l]<<24>>24;h[r]=g}if(t<1||t>=a.f){throw Dh(new os(XB))}A=od(oe,YB,8,a.f,15,1);for(q=0;q<a.f;A[q++]=0);c=od(oe,YB,8,256,15,1);for(m=0;m<256;c[m++]=0);for(n=0;n<t;n++){b=a.d[n]<<24>>24;A[n]=b<<24|c[255&b]&$B;++c[255&b]}for(o=t+1;o<a.f;o++){b=a.d[o]<<24>>24;A[o]=b<<24|c[255&b]&$B;++c[255&b]}s=1;for(j=0;j<256;j++){B=c[j];c[j]=s;s+=B}p=0;s=a.f-1;while(s>0){w=A[p];b=A[p]>>24<<24>>24;a.d[--s]=b;p=c[255&b]+(w&$B)}if(p!=t){throw Dh(new os(XB))}return a.f}
function Dn(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X;I=0;while(I<5&&32>>I>b){++I}l=1<<I;if(b!=32>>I){throw Dh(new jt('(IWMap::image) Unsupported subsampling factor'))}if(c.xmin>=c.xmax||c.ymin>=c.ymax){throw Dh(new jt('(IWMap::image) GRect is empty'))}w=new Ym(0,0,(a.e+b-1)/b|0,(a.d+b-1)/b|0);if(c.xmin<0||c.ymin<0||c.xmax>w.xmax||c.ymax>w.ymax){throw Dh(new jt('(IWMap::image) GRect is out of bounds: '+c.xmin+','+c.ymin+','+c.xmax+','+c.ymax+','+w.xmax+','+w.ymax))}H=od(hf,$A,9,8,0,1);R=od(hf,$A,9,8,0,1);for(s=0;s<8;){H[s]=new Xm;R[s++]=new Xm}P=1;H[I]=new Zm(c);R[I]=new Zm(c);for(t=I-1;t>=0;t--){H[t]=R[t+1];Rm(H[t],3*P,3*P);Sm(H[t],H[t],w);P+=P;R[t].xmin=H[t].xmin+P-1&~(P-1);R[t].xmax=H[t].xmax&~(P-1);R[t].ymin=H[t].ymin+P-1&~(P-1);R[t].ymax=H[t].ymax&~(P-1)}W=new Xm;W.xmin=H[0].xmin&~(l-1);W.ymin=H[0].ymin&~(l-1);W.xmax=(H[0].xmax-1&~(l-1))+l;W.ymax=(H[0].ymax-1&~(l-1))+l;q=W.xmax-W.xmin;p=pi(q*(W.ymax-W.ymin));i=a.c>>5;C=(W.ymin>>I)*i+(W.xmin>>I);F=new Int16Array(EB);for(n=W.ymin,D=0;n<W.ymax;n+=l,D+=q<<I,C+=i){for(m=W.xmin,h=C,Q=D;m<W.xmax;m+=l,++h,Q+=l){j=a.b[h];G=I;I>2&&(m+31<H[2].xmin||m>H[2].xmax||n+31<H[2].ymin||n>H[2].ymax)&&(G=2);k=(1<<G+G)+15>>4;N=1<<I-G;O=q<<I-G;U=32>>G;V=U<<5;on(j,F,k);for(v=0,T=0,M=Q;v<l;v+=N,M+=O,T+=V-32){for(B=0;B<l;B+=N,T+=U){p[M+B]=F[T]}}}}P=l;for(u=0;u<I;u++){o=H[u];o.xmin=o.xmin&~(P-1);o.ymin=o.ymin&~(P-1);Vm(o,-W.xmin,-W.ymin);if(g&&u>=4){Fn(q,p,o);break}Hn(p,o.ymin*q+o.xmin,o.xmax-o.xmin,o.ymax-o.ymin,q,P,P>>1);P>>=1}J=new Zm(c);Vm(J,-W.xmin,-W.ymin);for(r=J.ymin,K=J.ymin*q,S=d;r++<J.ymax;S+=f,K+=q){for(A=J.xmin,L=S;A<J.xmax;++A,L+=4){X=p[K+A]+32>>6;X<-128?(X=-128):X>127&&(X=127);e[L]=X}}}
function Aq(b){var c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G,H;j=new Wq;for(p=0;p<b.b.a.length;++p){n=(_q(p,b.b.a.length),yw(b.b,p));if(de(n,26)){l=$d(n,26);k=l.a;if(Xt('maparea',k)){try{t=(_q(0,l.b.a.length),yw(l.b,0));if(de(t,26)){s=$d(t,26);if(!Xt('url',s.a)){throw Dh(new jt('DjVuAnno.bad_url'))}_q(0,s.b.a.length);fi(yw(s.b,0));_q(1,s.b.a.length);fi(yw(s.b,1))}else{be(t)}_q(1,l.b.a.length);be(yw(l.b,1));r=(_q(2,l.b.a.length),yw(l.b,2));w=null;if(de(r,26)){q=$d(r,26);if(Xt('rect',q.a)){g=new Ym((_q(0,q.b.a.length),Hp($d(yw(q.b,0),15))),(_q(1,q.b.a.length),Hp($d(yw(q.b,1),15))),(_q(2,q.b.a.length),Hp($d(yw(q.b,2),15))),(_q(3,q.b.a.length),Hp($d(yw(q.b,3),15))));w=Br(new Dr,g)}else if(Xt('line',q.a)){u=(_q(0,q.b.a.length),Hp($d(yw(q.b,0),15)));F=(_q(1,q.b.a.length),Hp($d(yw(q.b,1),15)));v=(_q(2,q.b.a.length),Hp($d(yw(q.b,2),15)));G=(_q(3,q.b.a.length),Hp($d(yw(q.b,3),15)));w=Gr(new Hr,u,F,v,G)}else if(Xt('poly',q.a)){o=q.b.a.length/2|0;D=od(oe,YB,8,o,15,1);H=od(oe,YB,8,o,15,1);for(h=0,i=0;i<q.b.a.length;h++){D[h]=Hp($d(Qq(q,i++),15));H[h]=Hp($d(Qq(q,i++),15))}w=Kr(new Nr,D,H,o)}else if(Xt('oval',q.a)){g=new Ym((_q(0,q.b.a.length),Hp($d(yw(q.b,0),15))),(_q(1,q.b.a.length),Hp($d(yw(q.b,1),15))),(_q(2,q.b.a.length),Hp($d(yw(q.b,2),15))),(_q(3,q.b.a.length),Hp($d(yw(q.b,3),15))));w=Br(new Ir,g)}else if(Xt('text',q.a)){g=new Ym((_q(0,q.b.a.length),Hp($d(yw(q.b,0),15))),(_q(1,q.b.a.length),Hp($d(yw(q.b,1),15))),(_q(2,q.b.a.length),Hp($d(yw(q.b,2),15))),(_q(3,q.b.a.length),Hp($d(yw(q.b,3),15))));w=Br(new Tr,g)}}if(!!w&&!w.Jb()){for(m=3;m<l.b.a.length;m++){e=(_q(m,l.b.a.length),yw(l.b,m));if(de(e,26)){d=$d(e,26);A=d.a;if(Xt('border_avis',A));else if(Xt('arrow',A));else if(Xt('pushpin',A));else if(Xt('hilite',A)){B=(_q(0,d.b.a.length),yw(d.b,0));de(B,48)&&(new nt(Iq(fi(B),255)),undefined)}else if(Xt('lineclr',A)){B=(_q(0,d.b.a.length),yw(d.b,0));de(B,48)&&(new nt(Iq(fi(B),255)),undefined)}else if(Xt('backclr',A)){B=(_q(0,d.b.a.length),yw(d.b,0));de(B,48)&&(new nt(Iq(fi(B),255)),undefined)}else if(Xt('textclr',A)){B=(_q(0,d.b.a.length),yw(d.b,0));de(B,48)&&(new nt(Iq(fi(B),255)),undefined)}else if(Xt('opacity',A)){B=(_q(0,d.b.a.length),yw(d.b,0));de(B,18)&&($d(B,18),undefined)}else if(Xt('width',A)){B=(_q(0,d.b.a.length),yw(d.b,0));de(B,18)&&($d(B,18),undefined)}else{c=Xt('none',A)?0:Xt('xor',A)?1:Xt('border',A)?2:Xt('shadow_in',A)?3:Xt('shadow_out',A)?4:Xt('shadow_ein',A)?5:Xt('shadow_eout',A)?6:-1;if(c>=0){Cr(c);for(C=0;C<d.b.a.length;++C){B=(_q(C,d.b.a.length),yw(d.b,C));de(B,48)?(new nt(Iq(fi(B),255)),undefined):de(B,15)&&Hp($d(B,15))}}}}}vw(j.b,w)}}catch(a){a=Ch(a);if(de(a,7)){f=a;Rb(f,(Ek(),Dk),'')}else throw Dh(a)}}}}return j}
function sm(a,b,c){var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,A,B,C,D,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,ab,bb,cb,db,eb,fb,gb,hb,ib,jb,kb,lb,mb,nb,ob,pb,qb,rb,sb,tb;qb=b.i;pb=b.k;e=le($wnd.Math.ceil(qb*0.75));d=le($wnd.Math.ceil(pb*0.75));l=new Ym(0,0,e,d);if(c.xmin<l.xmin||c.ymin<l.ymin||c.xmax>l.xmax||c.ymax>l.ymax){throw Dh(new jt('rectangle out of boundspdr=('+c.xmin+','+c.ymin+','+c.xmax+','+c.ymax+'),rect=('+l.xmin+','+l.ymin+','+l.xmax+','+l.ymax+')'))}e=c.xmax-c.xmin;d=c.ymax-c.ymin;tm(a,d,e,null);tb=c.ymin/3|0;k=c.ymin-3*tb;sb=c.xmin/3|0;j=c.xmin-3*sb;if(j<0){--sb;j+=3}sb*=4;tb*=4;lb=new bm(b,0);mb=new bm(b,0);nb=new bm(b,0);ob=new bm(b,0);f=new bm(a,0);g=new bm(a,0);h=new bm(a,0);while(k<d){_l(lb,tb++,sb);tb>=pb&&--tb;_l(mb,tb++,sb);tb>=pb&&--tb;_l(nb,tb++,sb);tb>=pb&&--tb;_l(ob,tb++,sb);f.c=(sl(f.d,k<0?0:k)+j)*ql;++k>=d&&--k;g.c=(sl(g.d,k<0?0:k)+j)*ql;++k>=d&&--k;_l(h,k++,j);i=j;rb=sb;while(i<e){m=255&lb.d.e[lb.c+lb.a]<<24>>24;n=255&lb.d.e[lb.c+lb.b]<<24>>24;o=255&lb.d.e[lb.c+lb.e]<<24>>24;p=255&mb.d.e[mb.c+mb.a]<<24>>24;q=255&mb.d.e[mb.c+mb.b]<<24>>24;r=255&mb.d.e[mb.c+mb.e]<<24>>24;s=255&nb.d.e[nb.c+nb.a]<<24>>24;t=255&nb.d.e[nb.c+nb.b]<<24>>24;u=255&nb.d.e[nb.c+nb.e]<<24>>24;v=255&ob.d.e[ob.c+ob.a]<<24>>24;w=255&ob.d.e[ob.c+ob.b]<<24>>24;A=255&ob.d.e[ob.c+ob.e]<<24>>24;if(++rb<qb){lb.c+=ql;mb.c+=ql;nb.c+=ql;ob.c+=ql}B=255&lb.d.e[lb.c+lb.a]<<24>>24;C=255&lb.d.e[lb.c+lb.b]<<24>>24;D=255&lb.d.e[lb.c+lb.e]<<24>>24;F=255&mb.d.e[mb.c+mb.a]<<24>>24;G=255&mb.d.e[mb.c+mb.b]<<24>>24;H=255&mb.d.e[mb.c+mb.e]<<24>>24;I=255&nb.d.e[nb.c+nb.a]<<24>>24;J=255&nb.d.e[nb.c+nb.b]<<24>>24;K=255&nb.d.e[nb.c+nb.e]<<24>>24;L=255&ob.d.e[ob.c+ob.a]<<24>>24;M=255&ob.d.e[ob.c+ob.b]<<24>>24;N=255&ob.d.e[ob.c+ob.e]<<24>>24;if(++rb<qb){lb.c+=ql;mb.c+=ql;nb.c+=ql;ob.c+=ql}O=255&lb.d.e[lb.c+lb.a]<<24>>24;P=255&lb.d.e[lb.c+lb.b]<<24>>24;Q=255&lb.d.e[lb.c+lb.e]<<24>>24;R=255&mb.d.e[mb.c+mb.a]<<24>>24;S=255&mb.d.e[mb.c+mb.b]<<24>>24;T=255&mb.d.e[mb.c+mb.e]<<24>>24;U=255&nb.d.e[nb.c+nb.a]<<24>>24;V=255&nb.d.e[nb.c+nb.b]<<24>>24;W=255&nb.d.e[nb.c+nb.e]<<24>>24;X=255&ob.d.e[ob.c+ob.a]<<24>>24;Y=255&ob.d.e[ob.c+ob.b]<<24>>24;Z=255&ob.d.e[ob.c+ob.e]<<24>>24;if(++rb<qb){lb.c+=ql;mb.c+=ql;nb.c+=ql;ob.c+=ql}$=255&lb.d.e[lb.c+lb.a]<<24>>24;ab=255&lb.d.e[lb.c+lb.b]<<24>>24;bb=255&lb.d.e[lb.c+lb.e]<<24>>24;cb=255&mb.d.e[mb.c+mb.a]<<24>>24;db=255&mb.d.e[mb.c+mb.b]<<24>>24;eb=255&mb.d.e[mb.c+mb.e]<<24>>24;fb=255&nb.d.e[nb.c+nb.a]<<24>>24;gb=255&nb.d.e[nb.c+nb.b]<<24>>24;hb=255&nb.d.e[nb.c+nb.e]<<24>>24;ib=255&ob.d.e[ob.c+ob.a]<<24>>24;jb=255&ob.d.e[ob.c+ob.b]<<24>>24;kb=255&ob.d.e[ob.c+ob.e]<<24>>24;if(++rb<qb){lb.c+=ql;mb.c+=ql;nb.c+=ql;ob.c+=ql}Zl(f,11*m+2*(p+B)+F+8>>4<<24>>24);$l(f,11*n+2*(q+C)+G+8>>4<<24>>24);am(f,11*o+2*(r+D)+H+8>>4<<24>>24);Zl(g,7*(p+s)+F+I+8>>4<<24>>24);$l(g,7*(q+t)+G+J+8>>4<<24>>24);am(g,7*(r+u)+H+K+8>>4<<24>>24);Zl(h,11*v+2*(s+L)+I+8>>4<<24>>24);$l(h,11*w+2*(t+M)+J+8>>4<<24>>24);am(h,11*A+2*(u+N)+K+8>>4<<24>>24);if(++i<e){f.c+=ql;g.c+=ql;h.c+=ql}Zl(f,7*(B+O)+F+R+8>>4<<24>>24);$l(f,7*(C+P)+G+S+8>>4<<24>>24);am(f,7*(D+Q)+H+T+8>>4<<24>>24);Zl(g,I+U+F+R+2>>2<<24>>24);$l(g,J+V+G+S+2>>2<<24>>24);am(g,K+W+H+T+2>>2<<24>>24);Zl(h,7*(L+X)+I+U+8>>4<<24>>24);$l(h,7*(M+Y)+J+V+8>>4<<24>>24);am(h,7*(N+Z)+K+W+8>>4<<24>>24);if(++i<e){f.c+=ql;g.c+=ql;h.c+=ql}Zl(f,11*$+2*(cb+O)+R+8>>4<<24>>24);$l(f,11*ab+2*(db+P)+S+8>>4<<24>>24);am(f,11*bb+2*(eb+Q)+T+8>>4<<24>>24);Zl(g,7*(cb+fb)+R+U+8>>4<<24>>24);$l(g,7*(db+gb)+S+V+8>>4<<24>>24);am(g,7*(eb+hb)+T+W+8>>4<<24>>24);Zl(h,11*ib+2*(fb+X)+U+8>>4<<24>>24);$l(h,11*jb+2*(gb+Y)+V+8>>4<<24>>24);am(h,11*kb+2*(hb+Z)+W+8>>4<<24>>24);if(++i<e){f.c+=ql;g.c+=ql;h.c+=ql}}}}
function mn(){mn=ci;ln=sd(kd(Ah,1),lC,8,15,[0,16,512,528,8,24,520,536,256,272,768,784,264,280,776,792,4,20,516,532,12,28,524,540,260,276,772,788,268,284,780,796,128,144,640,656,136,152,648,664,384,400,896,912,392,408,904,920,132,148,644,660,140,156,652,668,388,404,900,916,396,412,908,924,2,18,514,530,10,26,522,538,258,274,770,786,266,282,778,794,6,22,518,534,14,30,526,542,262,278,774,790,270,286,782,798,130,146,642,658,138,154,650,666,386,402,898,914,394,410,906,922,134,150,646,662,142,158,654,670,390,406,902,918,398,414,910,926,64,80,576,592,72,88,584,600,320,336,832,848,328,344,840,856,68,84,580,596,76,92,588,604,324,340,836,852,332,348,844,860,192,208,704,720,200,216,712,728,448,464,960,976,456,472,968,984,196,212,708,724,204,220,716,732,452,468,964,980,460,476,972,988,66,82,578,594,74,90,586,602,322,338,834,850,330,346,842,858,70,86,582,598,78,94,590,606,326,342,838,854,334,350,846,862,194,210,706,722,202,218,714,730,450,466,962,978,458,474,970,986,198,214,710,726,206,222,718,734,454,470,966,982,462,478,974,990,1,17,513,529,9,25,521,537,257,273,769,785,265,281,777,793,5,21,517,533,13,29,525,541,261,277,773,789,269,285,781,797,129,145,641,657,137,153,649,665,385,401,897,913,393,409,905,921,133,149,645,661,141,157,653,669,389,405,901,917,397,413,909,925,3,19,515,531,11,27,523,539,259,275,771,787,267,283,779,795,7,23,519,535,15,31,527,543,263,279,775,791,271,287,783,799,131,147,643,659,139,155,651,667,387,403,899,915,395,411,907,923,135,151,647,663,143,159,655,671,391,407,903,919,399,415,911,927,65,81,577,593,73,89,585,601,321,337,833,849,329,345,841,857,69,85,581,597,77,93,589,605,325,341,837,853,333,349,845,861,193,209,705,721,201,217,713,729,449,465,961,977,457,473,969,985,197,213,709,725,205,221,717,733,453,469,965,981,461,477,973,989,67,83,579,595,75,91,587,603,323,339,835,851,331,347,843,859,71,87,583,599,79,95,591,607,327,343,839,855,335,351,847,863,195,211,707,723,203,219,715,731,451,467,963,979,459,475,971,987,199,215,711,727,207,223,719,735,455,471,967,983,463,479,975,991,32,48,544,560,40,56,552,568,288,304,800,816,296,312,808,824,36,52,548,564,44,60,556,572,292,308,804,820,300,316,812,828,160,176,672,688,168,184,680,696,416,432,928,944,424,440,936,952,164,180,676,692,172,188,684,700,420,436,932,948,428,444,940,956,34,50,546,562,42,58,554,570,290,306,802,818,298,314,810,826,38,54,550,566,46,62,558,574,294,310,806,822,302,318,814,830,162,178,674,690,170,186,682,698,418,434,930,946,426,442,938,954,166,182,678,694,174,190,686,702,422,438,934,950,430,446,942,958,96,112,608,624,104,120,616,632,352,368,864,880,360,376,872,888,100,116,612,628,108,124,620,636,356,372,868,884,364,380,876,892,224,240,736,752,232,248,744,760,480,496,992,1008,488,504,1000,1016,228,244,740,756,236,252,748,764,484,500,996,1012,492,508,1004,1020,98,114,610,626,106,122,618,634,354,370,866,882,362,378,874,890,102,118,614,630,110,126,622,638,358,374,870,886,366,382,878,894,226,242,738,754,234,250,746,762,482,498,994,1010,490,506,1002,1018,230,246,742,758,238,254,750,766,486,502,998,1014,494,510,1006,1022,33,49,545,561,41,57,553,569,289,305,801,817,297,313,809,825,37,53,549,565,45,61,557,573,293,309,805,821,301,317,813,829,161,177,673,689,169,185,681,697,417,433,929,945,425,441,937,953,165,181,677,693,173,189,685,701,421,437,933,949,429,445,941,957,35,51,547,563,43,59,555,571,291,307,803,819,299,315,811,827,39,55,551,567,47,63,559,575,295,311,807,823,303,319,815,831,163,179,675,691,171,187,683,699,419,435,931,947,427,443,939,955,167,183,679,695,175,191,687,703,423,439,935,951,431,447,943,959,97,113,609,625,105,121,617,633,353,369,865,881,361,377,873,889,101,117,613,629,109,125,621,637,357,373,869,885,365,381,877,893,225,241,737,753,233,249,745,761,481,497,993,1009,489,505,1001,1017,229,245,741,757,237,253,749,765,485,501,997,1013,493,509,1005,1021,99,115,611,627,107,123,619,635,355,371,867,883,363,379,875,891,103,119,615,631,111,127,623,639,359,375,871,887,367,383,879,895,227,243,739,755,235,251,747,763,483,499,995,1011,491,507,1003,1019,231,247,743,759,239,255,751,767,487,503,999,1015,495,511,1007,1023]);kn=new Int16Array(EB)}
function bs(){bs=ci;_r=new RegExp('[\\0-\\x1F\\x7F-\\x9F\\u0378\\u0379\\u037F-\\u0383\\u038B\\u038D\\u03A2\\u0528-\\u0530\\u0557\\u0558\\u0560\\u0588\\u058B-\\u058E\\u0590\\u05C8-\\u05CF\\u05EB-\\u05EF\\u05F5-\\u0605\\u061C\\u061D\\u06DD\\u070E\\u070F\\u074B\\u074C\\u07B2-\\u07BF\\u07FB-\\u07FF\\u082E\\u082F\\u083F\\u085C\\u085D\\u085F-\\u089F\\u08A1\\u08AD-\\u08E3\\u08FF\\u0978\\u0980\\u0984\\u098D\\u098E\\u0991\\u0992\\u09A9\\u09B1\\u09B3-\\u09B5\\u09BA\\u09BB\\u09C5\\u09C6\\u09C9\\u09CA\\u09CF-\\u09D6\\u09D8-\\u09DB\\u09DE\\u09E4\\u09E5\\u09FC-\\u0A00\\u0A04\\u0A0B-\\u0A0E\\u0A11\\u0A12\\u0A29\\u0A31\\u0A34\\u0A37\\u0A3A\\u0A3B\\u0A3D\\u0A43-\\u0A46\\u0A49\\u0A4A\\u0A4E-\\u0A50\\u0A52-\\u0A58\\u0A5D\\u0A5F-\\u0A65\\u0A76-\\u0A80\\u0A84\\u0A8E\\u0A92\\u0AA9\\u0AB1\\u0AB4\\u0ABA\\u0ABB\\u0AC6\\u0ACA\\u0ACE\\u0ACF\\u0AD1-\\u0ADF\\u0AE4\\u0AE5\\u0AF2-\\u0B00\\u0B04\\u0B0D\\u0B0E\\u0B11\\u0B12\\u0B29\\u0B31\\u0B34\\u0B3A\\u0B3B\\u0B45\\u0B46\\u0B49\\u0B4A\\u0B4E-\\u0B55\\u0B58-\\u0B5B\\u0B5E\\u0B64\\u0B65\\u0B78-\\u0B81\\u0B84\\u0B8B-\\u0B8D\\u0B91\\u0B96-\\u0B98\\u0B9B\\u0B9D\\u0BA0-\\u0BA2\\u0BA5-\\u0BA7\\u0BAB-\\u0BAD\\u0BBA-\\u0BBD\\u0BC3-\\u0BC5\\u0BC9\\u0BCE\\u0BCF\\u0BD1-\\u0BD6\\u0BD8-\\u0BE5\\u0BFB-\\u0C00\\u0C04\\u0C0D\\u0C11\\u0C29\\u0C34\\u0C3A-\\u0C3C\\u0C45\\u0C49\\u0C4E-\\u0C54\\u0C57\\u0C5A-\\u0C5F\\u0C64\\u0C65\\u0C70-\\u0C77\\u0C80\\u0C81\\u0C84\\u0C8D\\u0C91\\u0CA9\\u0CB4\\u0CBA\\u0CBB\\u0CC5\\u0CC9\\u0CCE-\\u0CD4\\u0CD7-\\u0CDD\\u0CDF\\u0CE4\\u0CE5\\u0CF0\\u0CF3-\\u0D01\\u0D04\\u0D0D\\u0D11\\u0D3B\\u0D3C\\u0D45\\u0D49\\u0D4F-\\u0D56\\u0D58-\\u0D5F\\u0D64\\u0D65\\u0D76-\\u0D78\\u0D80\\u0D81\\u0D84\\u0D97-\\u0D99\\u0DB2\\u0DBC\\u0DBE\\u0DBF\\u0DC7-\\u0DC9\\u0DCB-\\u0DCE\\u0DD5\\u0DD7\\u0DE0-\\u0DF1\\u0DF5-\\u0E00\\u0E3B-\\u0E3E\\u0E5C-\\u0E80\\u0E83\\u0E85\\u0E86\\u0E89\\u0E8B\\u0E8C\\u0E8E-\\u0E93\\u0E98\\u0EA0\\u0EA4\\u0EA6\\u0EA8\\u0EA9\\u0EAC\\u0EBA\\u0EBE\\u0EBF\\u0EC5\\u0EC7\\u0ECE\\u0ECF\\u0EDA\\u0EDB\\u0EE0-\\u0EFF\\u0F48\\u0F6D-\\u0F70\\u0F98\\u0FBD\\u0FCD\\u0FDB-\\u0FFF\\u10C6\\u10C8-\\u10CC\\u10CE\\u10CF\\u1249\\u124E\\u124F\\u1257\\u1259\\u125E\\u125F\\u1289\\u128E\\u128F\\u12B1\\u12B6\\u12B7\\u12BF\\u12C1\\u12C6\\u12C7\\u12D7\\u1311\\u1316\\u1317\\u135B\\u135C\\u137D-\\u137F\\u139A-\\u139F\\u13F5-\\u13FF\\u169D-\\u169F\\u16F1-\\u16FF\\u170D\\u1715-\\u171F\\u1737-\\u173F\\u1754-\\u175F\\u176D\\u1771\\u1774-\\u177F\\u17DE\\u17DF\\u17EA-\\u17EF\\u17FA-\\u17FF\\u180F\\u181A-\\u181F\\u1878-\\u187F\\u18AB-\\u18AF\\u18F6-\\u18FF\\u191D-\\u191F\\u192C-\\u192F\\u193C-\\u193F\\u1941-\\u1943\\u196E\\u196F\\u1975-\\u197F\\u19AC-\\u19AF\\u19CA-\\u19CF\\u19DB-\\u19DD\\u1A1C\\u1A1D\\u1A5F\\u1A7D\\u1A7E\\u1A8A-\\u1A8F\\u1A9A-\\u1A9F\\u1AAE-\\u1AFF\\u1B4C-\\u1B4F\\u1B7D-\\u1B7F\\u1BF4-\\u1BFB\\u1C38-\\u1C3A\\u1C4A-\\u1C4C\\u1C80-\\u1CBF\\u1CC8-\\u1CCF\\u1CF7-\\u1CFF\\u1DE7-\\u1DFB\\u1F16\\u1F17\\u1F1E\\u1F1F\\u1F46\\u1F47\\u1F4E\\u1F4F\\u1F58\\u1F5A\\u1F5C\\u1F5E\\u1F7E\\u1F7F\\u1FB5\\u1FC5\\u1FD4\\u1FD5\\u1FDC\\u1FF0\\u1FF1\\u1FF5\\u1FFF\\u200B-\\u200F\\u202A-\\u202E\\u2060-\\u206F\\u2072\\u2073\\u208F\\u209D-\\u209F\\u20BB-\\u20CF\\u20F1-\\u20FF\\u218A-\\u218F\\u23F4-\\u23FF\\u2427-\\u243F\\u244B-\\u245F\\u2700\\u2B4D-\\u2B4F\\u2B5A-\\u2BFF\\u2C2F\\u2C5F\\u2CF4-\\u2CF8\\u2D26\\u2D28-\\u2D2C\\u2D2E\\u2D2F\\u2D68-\\u2D6E\\u2D71-\\u2D7E\\u2D97-\\u2D9F\\u2DA7\\u2DAF\\u2DB7\\u2DBF\\u2DC7\\u2DCF\\u2DD7\\u2DDF\\u2E3C-\\u2E7F\\u2E9A\\u2EF4-\\u2EFF\\u2FD6-\\u2FEF\\u2FFC-\\u2FFF\\u3040\\u3097\\u3098\\u3100-\\u3104\\u312E-\\u3130\\u318F\\u31BB-\\u31BF\\u31E4-\\u31EF\\u321F\\u32FF\\u4DB6-\\u4DBF\\u9FCD-\\u9FFF\\uA48D-\\uA48F\\uA4C7-\\uA4CF\\uA62C-\\uA63F\\uA698-\\uA69E\\uA6F8-\\uA6FF\\uA78F\\uA794-\\uA79F\\uA7AB-\\uA7F7\\uA82C-\\uA82F\\uA83A-\\uA83F\\uA878-\\uA87F\\uA8C5-\\uA8CD\\uA8DA-\\uA8DF\\uA8FC-\\uA8FF\\uA954-\\uA95E\\uA97D-\\uA97F\\uA9CE\\uA9DA-\\uA9DD\\uA9E0-\\uA9FF\\uAA37-\\uAA3F\\uAA4E\\uAA4F\\uAA5A\\uAA5B\\uAA7C-\\uAA7F\\uAAC3-\\uAADA\\uAAF7-\\uAB00\\uAB07\\uAB08\\uAB0F\\uAB10\\uAB17-\\uAB1F\\uAB27\\uAB2F-\\uABBF\\uABEE\\uABEF\\uABFA-\\uABFF\\uD7A4-\\uD7AF\\uD7C7-\\uD7CA\\uD7FC-\\uF8FF\\uFA6E\\uFA6F\\uFADA-\\uFAFF\\uFB07-\\uFB12\\uFB18-\\uFB1C\\uFB37\\uFB3D\\uFB3F\\uFB42\\uFB45\\uFBC2-\\uFBD2\\uFD40-\\uFD4F\\uFD90\\uFD91\\uFDC8-\\uFDEF\\uFDFE\\uFDFF\\uFE1A-\\uFE1F\\uFE27-\\uFE2F\\uFE53\\uFE67\\uFE6C-\\uFE6F\\uFE75\\uFEFD-\\uFF00\\uFFBF-\\uFFC1\\uFFC8\\uFFC9\\uFFD0\\uFFD1\\uFFD8\\uFFD9\\uFFDD-\\uFFDF\\uFFE7\\uFFEF-\\uFFFB\\uFFFE\\uFFFF]','g');as=new RegExp('\\xAD\\s+$')}
function gq(a){a.e=sd(kd(Df,1),$A,3,0,[new uq(DB,0,84,145),new uq(DB,0,3,4),new uq(DB,0,4,3),new uq(27581,4261,5,1),new uq(27581,4261,6,2),new uq(23877,7976,7,3),new uq(23877,7976,8,4),new uq(20921,11219,9,5),new uq(20921,11219,10,6),new uq(18451,14051,11,7),new uq(18451,14051,12,8),new uq(16341,16524,13,9),new uq(16341,16524,14,10),new uq(14513,18685,15,11),new uq(14513,18685,16,12),new uq(12917,20573,17,13),new uq(12917,20573,18,14),new uq(11517,22224,19,15),new uq(11517,22224,20,16),new uq(10277,23665,21,17),new uq(10277,23665,22,18),new uq(9131,24923,23,19),new uq(9131,24923,24,20),new uq(8071,26021,25,21),new uq(8071,26021,26,22),new uq(7099,26978,27,23),new uq(7099,26978,28,24),new uq(6213,27810,29,25),new uq(6213,27810,30,26),new uq(5411,28532,31,27),new uq(5411,28532,32,28),new uq(4691,29158,33,29),new uq(4691,29158,34,30),new uq(4047,29700,35,31),new uq(4047,29700,36,32),new uq(3477,30166,37,33),new uq(3477,30166,38,34),new uq(2973,30568,39,35),new uq(2973,30568,40,36),new uq(2531,30914,41,37),new uq(2531,30914,42,38),new uq(2145,31210,43,39),new uq(2145,31210,44,40),new uq(1809,31463,45,41),new uq(1809,31463,46,42),new uq(1521,31678,47,43),new uq(1521,31678,48,44),new uq(1273,31861,49,45),new uq(1273,31861,50,46),new uq(1061,32015,51,47),new uq(1061,32015,52,48),new uq(881,32145,53,49),new uq(881,32145,54,50),new uq(729,32254,55,51),new uq(729,32254,56,52),new uq(601,32346,57,53),new uq(601,32346,58,54),new uq(493,32422,59,55),new uq(493,32422,60,56),new uq(403,32486,61,57),new uq(403,32486,62,58),new uq(329,32538,63,59),new uq(329,32538,64,60),new uq(267,32581,65,61),new uq(267,32581,66,62),new uq(213,32619,67,63),new uq(213,32619,68,64),new uq(165,32653,69,65),new uq(165,32653,70,66),new uq(123,32682,71,67),new uq(123,32682,72,68),new uq(87,32707,73,69),new uq(87,32707,74,70),new uq(59,32727,75,71),new uq(59,32727,76,72),new uq(35,32743,77,73),new uq(35,32743,78,74),new uq(19,32754,79,75),new uq(19,32754,80,76),new uq(7,32762,81,77),new uq(7,32762,82,78),new uq(1,wC,81,79),new uq(1,wC,82,80),new uq(22165,0,9,85),new uq(9454,0,86,226),new uq(DB,0,5,6),new uq(3376,0,88,176),new uq(18458,0,89,143),new uq(1153,0,90,138),new uq(13689,0,91,141),new uq(378,0,92,112),new uq(9455,0,93,135),new uq(123,0,94,104),new uq(6520,0,95,133),new uq(40,0,96,100),new uq(4298,0,97,129),new uq(13,0,82,98),new uq(2909,0,99,127),new uq(52,0,76,72),new uq(1930,0,101,125),new uq(160,0,70,102),new uq(1295,0,103,123),new uq(279,0,66,60),new uq(856,0,105,121),new uq(490,0,106,110),new uq(564,0,107,119),new uq(324,0,66,108),new uq(371,0,109,117),new uq(564,0,60,54),new uq(245,0,111,115),new uq(851,0,56,48),new uq(161,0,69,113),new uq(1477,0,114,134),new uq(282,0,65,59),new uq(975,0,116,132),new uq(426,0,61,55),new uq(645,0,118,130),new uq(646,0,57,51),new uq(427,0,120,128),new uq(979,0,53,47),new uq(282,0,122,126),new uq(1477,0,49,41),new uq(186,0,124,62),new uq(2221,0,43,37),new uq(122,0,72,66),new uq(3276,0,39,31),new uq(491,0,60,54),new uq(4866,0,33,25),new uq(742,0,56,50),new uq(7041,0,29,131),new uq(1118,0,52,46),new uq(9455,0,23,17),new uq(1680,0,48,40),new uq(10341,0,23,15),new uq(2526,0,42,136),new uq(14727,0,137,7),new uq(3528,0,38,32),new uq(11417,0,21,139),new uq(4298,0,140,172),new uq(15199,0,15,9),new uq(2909,0,142,170),new uq(22165,0,9,85),new uq(1930,0,144,168),new uq(DB,0,141,248),new uq(1295,0,146,166),new uq(9454,0,147,247),new uq(856,0,148,164),new uq(3376,0,149,197),new uq(564,0,150,162),new uq(1153,0,151,95),new uq(371,0,152,160),new uq(378,0,153,173),new uq(245,0,154,158),new uq(123,0,155,165),new uq(161,0,70,156),new uq(40,0,157,161),new uq(282,0,66,60),new uq(13,0,81,159),new uq(426,0,62,56),new uq(52,0,75,71),new uq(646,0,58,52),new uq(160,0,69,163),new uq(979,0,54,48),new uq(279,0,65,59),new uq(1477,0,50,42),new uq(490,0,167,171),new uq(2221,0,44,38),new uq(324,0,65,169),new uq(3276,0,40,32),new uq(564,0,59,53),new uq(4866,0,34,26),new uq(851,0,55,47),new uq(7041,0,30,174),new uq(1477,0,175,193),new uq(9455,0,24,18),new uq(975,0,177,191),new uq(11124,0,178,222),new uq(645,0,179,189),new uq(8221,0,180,218),new uq(427,0,181,187),new uq(5909,0,182,216),new uq(282,0,183,185),new uq(4023,0,184,214),new uq(186,0,69,61),new uq(2663,0,186,212),new uq(491,0,59,53),new uq(1767,0,188,210),new uq(742,0,55,49),new uq(1174,0,190,208),new uq(1118,0,51,45),new uq(781,0,192,206),new uq(1680,0,47,39),new uq(518,0,194,204),new uq(2526,0,41,195),new uq(341,0,196,202),new uq(3528,0,37,31),new uq(225,0,198,200),new uq(11124,0,199,243),new uq(148,0,72,64),new uq(8221,0,201,239),new uq(392,0,62,56),new uq(5909,0,203,237),new uq(594,0,58,52),new uq(4023,0,205,235),new uq(899,0,54,48),new uq(2663,0,207,233),new uq(1351,0,50,44),new uq(1767,0,209,231),new uq(2018,0,46,38),new uq(1174,0,211,229),new uq(3008,0,40,34),new uq(781,0,213,227),new uq(4472,0,36,28),new uq(518,0,215,225),new uq(6618,0,30,22),new uq(341,0,217,223),new uq(9455,0,26,16),new uq(225,0,219,221),new uq(12814,0,20,220),new uq(148,0,71,63),new uq(17194,0,14,8),new uq(392,0,61,55),new uq(17533,0,14,224),new uq(594,0,57,51),new uq(24270,0,8,2),new uq(899,0,53,47),new uq(DB,0,228,87),new uq(1351,0,49,43),new uq(18458,0,230,246),new uq(2018,0,45,37),new uq(13689,0,232,244),new uq(3008,0,39,33),new uq(9455,0,234,238),new uq(4472,0,35,27),new uq(6520,0,138,236),new uq(6618,0,29,21),new uq(10341,0,24,16),new uq(9455,0,25,15),new uq(14727,0,240,8),new uq(12814,0,19,241),new uq(11417,0,22,242),new uq(17194,0,13,7),new uq(15199,0,16,10),new uq(17533,0,13,245),new uq(22165,0,10,2),new uq(24270,0,7,1),new uq(DB,0,244,83),new uq(DB,0,249,250),new uq(22165,0,10,2),new uq(18458,0,89,143),new uq(18458,0,230,246),new uq(0,0,0,0),new uq(0,0,0,0),new uq(0,0,0,0),new uq(0,0,0,0),new uq(0,0,0,0)]);a.g=od(Se,WB,19,256,0,1);a.n=od(oe,YB,8,256,15,1);a.o=od(oe,YB,8,256,15,1);a.q=od(Se,WB,19,256,0,1)}
var XA='object',YA='anonymous',ZA='fnStack',$A={4:1,5:1},_A='Unknown',aB='function',bB='boolean',cB='number',dB='string',eB=2147483647,fB='__java$exception',gB='__noinit__',hB={4:1,7:1},iB={4:1,14:1,11:1,7:1},jB='null',kB='For input string: "',lB=65536,mB=65535,nB='fromIndex: 0, toIndex: ',oB=', length: ',pB='fromIndex: ',qB='java.lang',rB='com.google.gwt.core.client',sB='com.google.gwt.core.client.impl',tB='com.google.gwt.user.client.ui',uB={70:1},vB='com.google.gwt.canvas.client',wB=4194303,xB=1048575,yB=524288,zB=17592186044416,AB=4194304,BB=-17592186044416,CB='CSS1Compat',DB=32768,EB=1024,FB='error',GB=131072,HB=262144,IB=1048576,JB=2097152,KB=16777216,LB=33554432,MB=67108864,NB='__gwtLastUnhandledEvent',OB='com.google.gwt.user.client.ui.impl',PB='safari',QB='Possible problem with your *.gwt.xml module file.\nThe compile time user.agent value (safari) does not match the runtime user.agent value (',RB=').\n',SB='Expect more errors.',TB='msie',UB={4:1},VB='com.lizardtech.djvu',WB={71:1,4:1,5:1},XB='ByteStream.corrupt',YB={12:1,4:1},ZB=4294967295,$B=16777215,_B='java.io',aC='TXTa',bC='TXTz',cC='DjVu Decoder: Corrupted data (Duplicate foreground layer)',dC='DjVu Decoder: Corrupted data (Duplicate background layer)',eC='image/djvu',fC='image/iw44',gC='EOF',hC='No DIRM chunk',iC='Invalid DjVu File Format',jC='bgIWPixmap',kC='progressive',lC={4:1,169:1},mC='invalid rectangle',nC='Scaler undefined size',oC='Scaler illegal ratio',pC='Illegal chunk id',qC='\x00\x00\x00\x00',rC='JB2Image no start',sC=-262143,tC=262142,uC='JB2Image bad number',vC='java.util',wC=32767,xC='default',yC='com.lizardtech.djvu.anno',zC={4:1,38:1},AC={26:1,4:1,38:1},BC='Unexpected EOF',CC='com.lizardtech.djvu.text',DC='DjVuText.corrupt_text',EC={4:1,14:1,13:1,11:1,7:1},FC={35:1,44:1},GC='java.nio.charset',HC='_gwt_modCount',IC={34:1},JC='delete',KC='javaemul.internal',LC='Invalid UTF8 sequence',MC='pl.djvuhtml5.client',NC='fileCacheSize',OC='pageCacheSize';var _,_h,Wh,Bh=-1;ai();bi(1,null,{},ub);_.W=function vb(a){return this===a};_.X=function xb(){return this.$b};_.Y=function zb(){return dz(this)};_.Z=function Bb(){return Ns(yb(this))+'@'+(Ab(this)>>>0).toString(16)};_.equals=function(a){return this.W(a)};_.hashCode=function(){return this.Y()};_.toString=function(){return this.Z()};var Qc;bi(183,1,{});bi(115,183,{},Xc);_.eb=function Yc(a){var b={},j;var c=[];a[ZA]=c;var d=arguments.callee.caller;while(d){var e=(Rc(),d.name||(d.name=Uc(d.toString())));c.push(e);var f=':'+e;var g=b[f];if(g){var h,i;for(h=0,i=g.length;h<i;h++){if(g[h]===d){return}}}(g||(b[f]=[])).push(d);d=d.caller}};_.fb=function Zc(a){var b,c,d,e;d=(Rc(),a&&a[ZA]?a[ZA]:[]);c=d.length;e=od(rg,$A,42,c,0,1);for(b=0;b<c;b++){e[b]=new Pt(d[b],null,-1)}return e};bi(184,183,{});_.eb=function _c(a){};_.gb=function ad(a,b,c,d){return new Pt(b,a+'@'+d,c<0?-1:c)};_.fb=function bd(a){var b,c,d,e,f,g,h;e=(Rc(),h=a.backingJsObject,h&&h.stack?h.stack.split('\n'):[]);f=od(rg,$A,42,0,0,1);b=0;d=e.length;if(d==0){return f}g=$c(this,e[0]);Xt(g.d,YA)||(f[b++]=g);for(c=1;c<d;c++){f[b++]=$c(this,e[c])}return f};bi(116,184,{},cd);_.gb=function dd(a,b,c,d){return new Pt(b,a,-1)};var Wd,Xd,Yd;Wd={4:1,111:1,35:1};bi(84,1,{},Os);_.Kb=function Ps(a){var b;b=new Os;b.f=4;a>1?(b.c=Vs(this,a-1)):(b.c=this);return b};_.Lb=function Us(){Ms(this);return this.b};_.Mb=function Ws(){return Ns(this)};_.Nb=function Ys(){Ms(this);return this.i};_.Ob=function $s(){return (this.f&4)!=0};_.Pb=function _s(){return (this.f&1)!=0};_.Z=function ct(){return ((this.f&2)!=0?'interface ':(this.f&1)!=0?'':'class ')+(Ms(this),this.k)};_.f=0;var Ls=1;bi(7,1,hB);_._=function Vb(a){return new $wnd.Error(a)};_.ab=function Xb(){return this.f};_.bb=function Yb(){var a,b,c;c=this.f==null?null:this.f.replace(new RegExp('\n','g'),' ');b=(a=Ns(this.$b),c==null?a:a+': '+c);Tb(this,Wb(this._(b)));Sc(this)};_.Z=function $b(){return Ub(this,this.ab())};_.backingJsObject=gB;_.j=true;bi(14,7,{4:1,14:1,7:1});bi(11,14,iB,ac);bi(113,11,iB,dt);bi(15,1,{4:1,15:1});Xd={4:1,35:1,112:1,15:1};bi(18,15,{4:1,35:1,18:1,15:1},nt,ot);_.ib=function qt(a){return mt(this,$d(a,18))};_.W=function rt(a){return de(a,18)&&$d(a,18).a==this.a};_.Y=function st(){return this.a};_.Hb=function tt(){return this.a};_.Z=function wt(){return ''+this.a};_.a=0;bi(56,11,iB,cc);bi(73,56,iB,Ft,Gt,Ht);_._=function It(a){return new $wnd.TypeError(a)};Yd={4:1,85:1,35:1,2:1};var cz=0;var ez,fz=0,gz;var pg=Rs(qB,'Object',1);var ve=Rs(rB,'JavaScriptObject$',0);var Ee=Rs(sB,'StackTraceCreator/Collector',183);var Be=Rs(sB,'StackTraceCreator/CollectorLegacy',115);var De=Rs(sB,'StackTraceCreator/CollectorModern',184);var Ce=Rs(sB,'StackTraceCreator/CollectorModernNoSourceMap',116);var _f=Rs(qB,'Boolean',111);var bg=Rs(qB,'Class',84);var wg=Rs(qB,'Throwable',7);var fg=Rs(qB,'Exception',14);var qg=Rs(qB,'RuntimeException',11);var ag=Rs(qB,'ClassCastException',113);var og=Rs(qB,'Number',15);var cg=Rs(qB,'Double',112);var jg=Rs(qB,'Integer',18);var kg=Rs(qB,'JsException',56);var mg=Rs(qB,'NullPointerException',73);var vg=Rs(qB,'String',2);bi(191,1,{});_.Z=function Eb(){if(!this.d){return '(null handle)'}return (xi(),this.d).outerHTML};var Ke=Rs(tB,'UIObject',191);bi(192,191,uB);_.$=function Hb(a){Fb(this,a)};_.b=false;_.c=0;var Le=Rs(tB,'Widget',192);bi(194,192,uB);var Ge=Rs(tB,'FocusWidget',194);bi(164,194,uB,Kb);var Jb;var se=Rs(vB,'Canvas',164);bi(195,1,{});var re=Rs(vB,'Canvas/CanvasElementSupportDetector',195);bi(165,195,{},Mb);var qe=Rs(vB,'Canvas/CanvasElementSupportDetectedMaybe',165);bi(130,1,{},Ob);_.a=0;var te=Rs(rB,'Duration',130);bi(117,56,iB);var xe=Rs(sB,'JavaScriptExceptionBase',117);bi(57,117,{57:1,4:1,14:1,11:1,7:1},gc);_.ab=function jc(){fc(this);return this.c};_.cb=function kc(){return ke(this.b)===ke(dc)?null:this.b};var dc;var ue=Rs(rB,'JavaScriptException',57);bi(170,1,{});var we=Rs(rB,'Scheduler',170);var mc=0,nc=0,oc=-1;bi(126,170,{},Hc);_.d=false;_.i=false;var yc;var Ae=Rs(sB,'SchedulerImpl',126);bi(127,1,{},Mc);_.db=function Nc(){this.a.d=true;Cc(this.a);this.a.d=false;return this.a.i=Dc(this.a)};var ye=Rs(sB,'SchedulerImpl/Flusher',127);bi(128,1,{},Oc);_.db=function Pc(){this.a.d&&Lc(this.a.e,1);return this.a.i};var ze=Rs(sB,'SchedulerImpl/Rescuer',128);var ud;var Rd,Sd,Td,Ud;bi(69,1,{197:1,69:1},ji);_.W=function ki(a){if(!de(a,69)){return false}return Xt(this.a,$d($d(a,197),69).a)};_.Y=function li(){return jz(this.a)};_.Z=function mi(){return 'safe: "'+this.a+'"'};var Fe=Rs('com.google.gwt.safehtml.shared','SafeUriString',69);var vi=null,wi;var Di=false;var Ii,Ji,Ki,Li;bi(156,192,uB,Yi);_.$=function Zi(a){xi();Ei(a.type)==DB&&!!this.a&&(this.d[NB]='',undefined);Fb(this,a)};var Je=Rs(tB,'Image',156);bi(193,1,{});var He=Rs(tB,'Image/State',193);bi(157,193,{},_i);var Ie=Rs(tB,'Image/UnclippedState',157);bi(166,1,{},cj);var aj;var Oe=Rs(OB,'FocusImpl',166);bi(196,166,{});var Ne=Rs(OB,'FocusImplStandard',196);bi(167,196,{},dj);var Me=Rs(OB,'FocusImplSafari',167);bi(72,7,hB);var eg=Rs(qB,'Error',72);bi(23,72,hB);var $f=Rs(qB,'AssertionError',23);bi(104,23,hB,gj);var Pe=Rs('com.google.gwt.useragent.client','UserAgentAsserter/UserAgentAssertionError',104);bi(64,1,{4:1,35:1,64:1});_.ib=function nj(a){return lj(this,$d(a,64))};_.compareTo=function mj(a){return this.c-a.c};_.equals=function oj(a){return this===a};_.W=function(a){return this.equals(a)};_.hashCode=function pj(){return dz(this)};_.Y=function(){return this.hashCode()};_.name=function qj(){return this.b!=null?this.b:''+this.c};_.ordinal=function rj(){return this.c};_.toString=function sj(){return this.b!=null?this.b:''+this.c};_.Z=function(){return this.toString()};_.c=0;var dg=Rs(qB,'Enum',64);bi(65,64,{65:1,4:1,35:1,64:1},wj);var tj,uj;var Qe=Ss('com.google.gwt.xhr.client','XMLHttpRequest/ResponseType',65,xj);bi(190,1,{});_.jb=function yj(a){};_.lb=function zj(b){var c,d,e;e=b.length;if(e==0){return 0}c=this.kb();if(c==-1){return -1}b[0]=c<<24>>24;d=1;try{for(;d<e;d++){c=this.kb();if(c==-1){break}b[d]=c<<24>>24}}catch(a){a=Ch(a);if(!de(a,6))throw Dh(a)}return d};_.mb=function Aj(){throw Dh(new os('mark/reset not supported'))};_.nb=function Bj(a){var b,c,d,e;c=a;if(Gh(a,0)<=0){return 0}d=Vh(Gh(2048,a)<0?2048:a);e=od(me,UB,8,d,15,1);while(Gh(c,0)>0){b=this.lb(e);if(b<0){break}c=Th(c,b)}return Th(a,c)};var qf=Rs(VB,'InputStream',190);bi(40,190,{40:1},Kj,Lj);_.kb=function Mj(){return Jj(this,this.g)==1?255&this.g[0]:-1};_.lb=function Nj(a){return Jj(this,a)};_.a=0;_.b=0;_.d=null;_.e=false;_.f=0;_.i=null;var Cj;var Re=Rs(VB,'BSInputStream',40);bi(19,1,{19:1},Qj,Rj);_.a=0;var Se=Rs(VB,'BitContext',19);bi(172,1,{});var Tf=Rs(_B,'OutputStream',172);bi(78,172,{},Vj);_.Z=function Wj(){return Wt(this.a,0,this.b,(Gy(),Fy))};_.b=0;var Te=Rs(VB,'ByteArrayOutputStream',78);bi(17,190,{17:1},ik,jk);_.jb=function kk(a){this.d.jb(a)};_.kb=function lk(){return ak(this)};_.lb=function mk(a){return bk(this,a)};_.mb=function nk(){this.d.mb()};_.nb=function ok(a){return hk(this,a)};_.a=0;_.b=null;_.c=0;var Ue=Rs(VB,'CachedInputStream',17);bi(149,1,{39:1},uk);_.ob=function vk(a){pk(this,a)};_.pb=function wk(){return false};_.c=null;var We=Rs(VB,'DjVmDir',149);bi(21,1,{21:1},xk,yk);_.a=0;_.b=null;_.c=0;_.d=0;var Ve=Rs(VB,'DjVmDir/File',21);bi(25,1,{39:1,25:1},Ak);_.ob=function Bk(a){zk(this,a)};_.pb=function Ck(){return false};_.dpi=300;_.height=0;_.a=20;_.width=0;var Xe=Rs(VB,'DjVuInfo',25);var Dk;bi(146,1,{});_.k=null;_.p=null;_.s=null;var Fk;var $e=Rs(VB,'DjVuPage',146);bi(148,1,uB,al);_.$=function bl(a){var b,c,d;xi();if(DB==Ei(a.type)){d=this.a.width;c=this.a.height;b=Lb();b.d.style['width']=d+'px';fd(b.d,d);b.d.style['height']=c+'px';ed(b.d,c);Nb(b.d.getContext('2d'),this.a,0,0);tm(this.b,c,d,null)}};var Ye=Rs(VB,'DjVuPage/1',148);bi(77,1,{77:1},cl);var Ze=Rs(VB,'DjVuPage/CodecInclude',77);bi(145,1,{},nl);_.a=null;_.c=null;_.d=null;var dl;var af=Rs(VB,'Document',145);bi(147,146,{},pl);var _e=Rs(VB,'Document/DocumentDjVuPage',147);bi(98,1,{});_.rb=function ul(){return this.i};_.d=0;_.border=0;_.dataHeight=0;_.dataWidth=0;_.g=0;_.i=0;_.j=false;_.k=0;_.o=0;var ql=4;var cf=Rs(VB,'GMap',98);bi(62,98,{},Gl);_.rb=function Hl(){return this.c};_.a=0;_.b=0;_.c=0;var bf=Rs(VB,'GBitmap',62);bi(37,1,{37:1},Ml,Nl);_.sb=function Ol(){return this.f};_.W=function Pl(a){var b;if(!de(a,37)){return false}b=$d(a,37);return b.sb()==this.sb()&&b.tb()==this.tb()&&b.ub()==this.ub()};_.tb=function Ql(){return this.g};_.Y=function Rl(){return -16777216|(255&this.ub())<<16|(255&this.tb())<<8|255&this.sb()};_.ub=function Sl(){return this.i};_.vb=function Tl(a,b,c){this.wb(a<<24>>24);this.yb(c<<24>>24);this.xb(b<<24>>24)};_.wb=function Ul(a){this.f=a};_.xb=function Vl(a){this.g=a};_.yb=function Wl(a){this.i=a};_.f=0;_.g=0;_.i=0;var Il;var ef=Rs(VB,'GPixel',37);bi(20,37,{37:1},bm,cm);_.sb=function dm(){return this.d.e[this.c+this.a]<<24>>24};_.tb=function em(){return this.d.e[this.c+this.b]<<24>>24};_.ub=function fm(){return this.d.e[this.c+this.e]<<24>>24};_.vb=function gm(a,b,c){Yl(this,a,b,c)};_.wb=function hm(a){Zl(this,a)};_.xb=function im(a){$l(this,a)};_.yb=function jm(a){am(this,a)};_.a=0;_.b=0;_.c=0;_.e=0;var df=Rs(VB,'GPixelReference',20);bi(28,98,{39:1,28:1,83:1},wm);_.Ab=function Am(){return this.e.byteLength};_.ob=function xm(a){throw Dh(new kt)};_.zb=function zm(){return this.k};_.Bb=function Cm(a,b,c){var d,e,f,g,h,i,j,k,l,m;!c&&(c=new wm);tm(c,b.ymax-b.ymin,b.xmax-b.xmin,null);i=new bm(this,0);f=new bm(c,0);for(j=b.xmin;j<b.xmax;j++){for(l=b.ymin;l<b.ymax;l++){_l(f,l-b.ymin,j-b.xmin);h=0;g=0;d=0;e=0;for(k=j*a;k<(j+1)*a&&k<this.i;k++){for(m=l*a;m<(l+1)*a&&m<this.k;m++){_l(i,this.k-m,k);h+=255&i.d.e[i.c+i.e]<<24>>24;g+=255&i.d.e[i.c+i.b]<<24>>24;d+=255&i.d.e[i.c+i.a]<<24>>24;++e}}am(f,(h/e|0)<<24>>24);$l(f,(g/e|0)<<24>>24);Zl(f,(d/e|0)<<24>>24)}}return c};_.Cb=function Dm(){return this.i};_.pb=function Em(){return false};var km,lm,mm=-1,nm;var gf=Rs(VB,'GPixmap',28);bi(159,1,{},Mm);_.a=0;_.b=0;_.c=null;_.d=-1;_.e=-1;_.f=null;_.g=null;_.i=0;_.j=0;_.k=0;_.n=0;_.o=null;_.p=0;_.q=0;var Fm;var ff=Rs(VB,'GPixmapScaler',159);bi(9,1,{9:1},Xm,Ym,Zm);_.W=function $m(a){return Pm(this,a)};_.xmax=0;_.xmin=0;_.ymax=0;_.ymin=0;var hf=Rs(VB,'GRect',9);bi(66,1,{66:1},gn);_.d=null;var _m,an;var jf=Rs(VB,'IFFEnumeration',66);bi(81,1,{81:1},pn);var kn,ln;var lf=Rs(VB,'IWBlock',81);bi(158,1,{},rn);_.a=0;var kf=Rs(VB,'IWBlock/Block',158);bi(79,1,{},An);_.g=0;_.i=0;var sn,tn;var nf=Rs(VB,'IWCodec',79);bi(33,1,{33:1},Bn);_.a=0;_.b=0;var mf=Rs(VB,'IWCodec/Bucket',33);bi(80,1,{},Gn);_.a=0;_.c=0;_.d=0;_.e=0;_.f=0;var of=Rs(VB,'IWMap',80);bi(47,1,{39:1,47:1,83:1},Mn);_.ob=function Nn(a){Kn(this,a)};_.zb=function On(){return this.k?this.k.d:0};_.Ab=function Pn(){var a;a=this.k.f*2500;!!this.b&&(a*=3);return a};_.Bb=function Qn(a,b,c){var d,e,f,g,h,i,j;if(!this.k){return null}!c&&(c=new wm);i=b.xmax-b.xmin;e=b.ymax-b.ymin;h=i*4;d=tm(c,e,i,null).e;Dn(this.k,a,b,0,d,h,false);if(!!this.f&&!!this.b&&this.c>=0){Dn(this.b,a,b,1,d,h,this.d);Dn(this.f,a,b,2,d,h,this.d)}g=new bm(c,0);for(f=0;f<e;){_l(g,f++,0);if(!!this.f&&!!this.b&&this.c>=0){Xl(g,i)}else{for(j=i;j-->0;g.c+=ql){Ll(g,127-d[g.c]<<24>>24)}}}return c};_.Cb=function Rn(){return this.k?this.k.e:0};_.pb=function Sn(){return true};_.a=null;_.b=null;_.c=10;_.d=false;_.e=null;_.f=null;_.g=0;_.i=0;_.j=null;_.k=null;var pf=Rs(VB,'IWPixmap',47);bi(53,1,{53:1},Yn);_.a=0;_.b=0;_.c=0;var rf=Rs(VB,'JB2Blit',53);bi(153,1,{});_.u=false;_.v=false;_.w=0;_.A=0;_.D=0;_.F=0;_.G=0;_.H=0;_.V=0;var Zn;var sf=Rs(VB,'JB2Codec',153);bi(99,153,{},Ao);_.b=0;_.c=null;_.d=null;var tf=Rs(VB,'JB2Decode',99);bi(61,1,{39:1,61:1},Fo);_.ob=function Go(a){var b,c;b=(this.Db(),c=new Ao,zo(c,new jk(a),null,this),c);while(!yo(b));};_.Db=function Ho(){Eo(this,null,false);Vq(this.g)};_.pb=function Io(){return false};_.e=null;_.f=0;var uf=Rs(VB,'JB2Dict',61);bi(41,61,{39:1,61:1,41:1},Oo);_.Db=function Po(){Mo(this)};_.b=0;_.c=false;_.d=0;var vf=Rs(VB,'JB2Image',41);bi(52,1,{52:1},Ro);_.b=0;var wf=Rs(VB,'JB2Shape',52);bi(185,1,{});_.add=function Wo(a){throw Dh(new uu('Add not supported on this collection'))};_.addAll=function Xo(a){var b,c,d;Uy(a);b=false;for(d=a.Eb();d.Rb();){c=d.Sb();b=b|this.add(c)}return b};_.clear=function Yo(){var a;for(a=this.Eb();a.Rb();){a.Sb();a.Tb()}};_.contains=function Zo(a){return So(this,a,false)};_.containsAll=function $o(a){return To(this,a)};_.isEmpty=function _o(){return this.size()==0};_.remove=function ap(a){return So(this,a,true)};_.removeAll=function bp(a){return Uo(this,a)};_.retainAll=function cp(a){var b,c,d;Uy(a);b=false;for(c=this.Eb();c.Rb();){d=c.Sb();if(!a.contains(d)){c.Tb();b=true}}return b};_.toArray=function dp(){return this.Fb(od(pg,$A,1,this.size(),5,1))};_.Fb=function ep(a){var b,c,d,e;e=this.size();a.length<e&&(a=(d=new Array(e),td(d,a)));c=this.Eb();for(b=0;b<e;++b){rd(a,b,c.Sb())}a.length>e&&rd(a,e,null);return a};_.Z=function fp(){return Vo(this)};var Bg=Rs(vC,'AbstractCollection',185);bi(186,185,{38:1});_.addAtIndex=function hp(a,b){throw Dh(new uu('Add not supported on this list'))};_.add=function ip(a){this.addAtIndex(this.size(),a);return true};_.addAllAtIndex=function jp(a,b){var c,d,e;Uy(b);c=false;for(e=b.Eb();e.Rb();){d=e.Sb();this.addAtIndex(a++,d);c=true}return c};_.clear=function kp(){this.Gb(0,this.size())};_.W=function lp(a){var b,c,d,e,f;if(a===this){return true}if(!de(a,38)){return false}f=$d(a,38);if(this.size()!=f.size()){return false}e=f.Eb();for(c=this.Eb();c.Rb();){b=c.Sb();d=e.Sb();if(!(ke(b)===ke(d)||b!=null&&wb(b,d))){return false}}return true};_.Y=function mp(){return yx(this)};_.indexOf=function np(a){return gp(this,a)};_.Eb=function op(){return new Gv(this)};_.lastIndexOf=function pp(a){var b;for(b=this.size()-1;b>-1;--b){if(qy(a,this.getAtIndex(b))){return b}}return -1};_.removeAtIndex=function qp(a){throw Dh(new uu('Remove not supported on this list'))};_.Gb=function rp(a,b){var c,d;d=new Kv(this,a);for(c=a;c<b;++c){Sy(d.a<d.c.size());d.c.getAtIndex(d.b=d.a++);Fv(d)}};_.setAtIndex=function sp(a,b){throw Dh(new uu('Set not supported on this list'))};_.subList=function tp(a,b){return new Mv(this,a,b)};var Ig=Rs(vC,'AbstractList',186);bi(101,186,{38:1},wp,xp);_.add=function yp(a){return this.array.push(a),undefined};_.clear=function zp(){this.array=[]};_.getAtIndex=function Ap(a){return this.array[a]};_.removeAtIndex=function Bp(a){this.array.splice(a,1)};_.setAtIndex=function Cp(a,b){this.array[a]=b};_.size=function Dp(){return this.array.length};var xf=Rs(VB,'JsArrayList',101);bi(16,15,{16:1,4:1,15:1},Jp,Kp);_.W=function Lp(a){return de(a,16)&&this.a==$d(a,16).a};_.Y=function Mp(){return this.a};_.Hb=function Np(){return this.a};_.a=0;var yf=Rs(VB,'NumContext',16);bi(68,1,{39:1,68:1},Pp);_.ob=function Qp(a){var b,c,d,e,f,g,h,i,j,k,l;h=new jk(a);l=ak(h);if((l&127)!=0){throw Dh(new os('bad palette version '+l))}i=ak(h)<<8;i|=ak(h);if(i<0){throw Dh(new os('Bad palette size '+i))}this.b=od(ef,$A,37,i,0,1);for(d=0;d<i;d++){b=ak(h)<<24>>24;g=ak(h)<<24>>24;j=ak(h)<<24>>24;this.b[d]=new Nl(b,g,j)}if((l&128)!=0){f=ak(h)<<16;f|=ak(h)<<8;f|=ak(h);if(f<0){throw Dh(new os('bad palette datasize'))}this.a=od(oe,YB,8,f,15,1);c=Ij(new Kj,h);for(e=0;e<f;e++){k=(Jj(c,c.g)==1?255&c.g[0]:-1)<<8;k|=Jj(c,c.g)==1?255&c.g[0]:-1;if(k<0||k>=i){throw Dh(new os('bad palette data'))}this.a[e]=k}}else{this.a=od(oe,YB,8,0,15,1)}};_.pb=function Rp(){return true};_.a=null;_.b=null;var zf=Rs(VB,'Palette',68);bi(22,190,{22:1},Wp,Xp);_.jb=function Yp(a){this.b=this.c};_.kb=function Zp(){if(this.c<this.a.length){return this.a[this.c++]}return -1};_.lb=function $p(a){var b;b=0;for(;b<a.length&&this.c+b<this.a.length;b++){a[b]=this.a[this.c+b]<<24>>24}if(b==0)return -1;this.c+=b;return b};_.mb=function _p(){this.c=this.b};_.nb=function aq(a){var b;b=this.c;this.c=Vh(Dt(Eh(this.c,a),this.a.length));return this.c-b};_.b=0;_.c=0;var Sp;var Bf=Rs(VB,'URLInputStream',22);bi(144,1,{168:1},bq);_.qb=function cq(){this.a.a=eA(Sp,this.b,null)};var Af=Rs(VB,'URLInputStream/1',144);bi(59,1,{},qq,rq,sq);_.a=0;_.b=0;_.c=0;_.d=0;_.f=0;_.i=0;_.p=0;_.r=0;var eq;var Cf=Rs(VB,'ZPCodec',59);bi(3,1,{3:1},uq);_.a=0;_.b=0;_.c=0;_.d=0;var Df=Rs(VB,'ZPTable',3);bi(100,1,{39:1},Gq);_.ob=function Jq(a){var b,c;if(Xt('ANTz',a.b)){a=Zj(new ik,Ij(new Kj,a));a.b='ANTa'}c=ek(a);if(c!=null&&c.length>0){b=this.a;Dq(this,b!=null?b+(''+c):c)}};_.pb=function Kq(){return false};_.a=null;var vq,wq,xq;var If=Rs(yC,'DjVuAnno',100);bi(24,186,zC,Wq);_.addAtIndex=function Xq(a,b){_q(a,this.b.a.length+1);uw(this.b,a,b)};_.add=function Yq(a){return vw(this.b,a)};_.addAllAtIndex=function Zq(a,b){_q(a,this.b.a.length+1);return ww(this.b,a,b)};_.addAll=function $q(a){return xw(this.b,a)};_.clear=function br(){this.b.a=od(pg,$A,1,0,5,1)};_.contains=function cr(a){return zw(this.b,a,0)!=-1};_.containsAll=function dr(a){return To(this.b,a)};_.getAtIndex=function er(a){return Rq(this,a)};_.indexOf=function fr(a){return zw(this.b,a,0)};_.isEmpty=function gr(){return this.b.a.length==0};_.Eb=function hr(){return new dx(this.b)};_.lastIndexOf=function ir(a){return Aw(this.b,a)};_.removeAtIndex=function jr(a){return _q(a,this.b.a.length),Cw(this.b,a)};_.removeAll=function kr(a){return Uo(this.b,a)};_.Gb=function lr(a,b){Ew(this.b,a,b)};_.setAtIndex=function mr(a,b){return _q(a,this.b.a.length),Fw(this.b,a,b)};_.size=function nr(){return this.b.a.length};_.subList=function or(a,b){return new Mv(this.b,a,b)};_.toArray=function pr(){return Hw(this.b)};_.Fb=function qr(a){return Iw(this.b,a)};_.Z=function rr(){return Vo(this.b)};var eh=Rs(vC,'Vector',24);bi(26,24,AC,sr);var Gf=Rs(yC,'DjVuAnno/NamedVector',26);bi(154,26,AC,ur);var Ff=Rs(yC,'DjVuAnno/LispParser',154);bi(60,1,{},yr);_.b=0;var Ef=Rs(yC,'DjVuAnno/LispParser/Token',60);bi(48,1,{48:1},zr);_.Z=function Ar(){return this.a};
var Hf=Rs(yC,'DjVuAnno/Symbol',48);bi(63,1,{},Dr);_.Ib=function Er(){return this.e};_.Jb=function Fr(){return Tm(this.Ib())};var Mf=Rs(yC,'Rect',63);bi(160,63,{},Hr);_.a=true;_.b=true;var Jf=Rs(yC,'Line',160);bi(162,63,{},Ir);var Kf=Rs(yC,'Oval',162);bi(161,63,{},Nr);_.Ib=function Qr(){var a,b,c,d,e,f,g;c=this.a;if(!this.a){b=$d(Qq(this.d,0%this.d.b.a.length),12);e=b[0];d=e;g=b[1];f=g;for(a=1;a<this.d.b.a.length;a++){b=$d(Qq(this.d,a%this.d.b.a.length),12);e>b[0]?(e=b[0]):d<b[0]&&(d=b[0]);g>b[1]?(g=b[1]):f<b[1]&&(f=b[1])}this.a=c=new Ym(e,g,d-e,f-g)}return c};_.Jb=function Rr(){return !(this.c||Lr(this))};_.a=null;_.b=null;_.c=false;var Lf=Rs(yC,'Poly',161);bi(163,63,{},Tr);var Nf=Rs(yC,'Text',163);bi(58,1,{39:1,58:1},Yr);_.ob=function Zr(b){var c,d;this.a.a=od(pg,$A,1,0,5,1);this.c=null;this.b=null;try{for(c=ck(b);c>0;){c-=Vr(this,b)}Ur(this)}catch(a){a=Ch(a);if(de(a,7)){d=a;Rb(d,(Ek(),Dk),'');ru()}else throw Dh(a)}};_.pb=function $r(){return false};_.b=null;_.c=null;var Of=Rs('com.lizardtech.djvu.outline','Bookmark',58);bi(55,1,{39:1,55:1},is);_.ob=function js(a){ds(this,a)};_.pb=function ks(){return false};_.Z=function ls(){return es(this,this.b.length)};var _r,as;var Qf=Rs(CC,'DjVuText',55);bi(97,9,{9:1},ns);_.a=true;_.c=0;_.d=0;_.e=1;var Pf=Rs(CC,'DjVuText/Zone',97);bi(171,172,{});var Rf=Rs(_B,'FilterOutputStream',171);bi(6,14,{6:1,4:1,14:1,7:1},os);var Sf=Rs(_B,'IOException',6);bi(109,171,{},ps);var Uf=Rs(_B,'PrintStream',109);bi(75,6,{6:1,4:1,75:1,14:1,7:1},qs);var Vf=Rs(_B,'UnsupportedEncodingException',75);bi(76,1,{85:1});_.Z=function ts(){return this.a};var Wf=Rs(qB,'AbstractStringBuilder',76);bi(125,11,iB,us);var Xf=Rs(qB,'ArithmeticException',125);bi(43,11,iB,vs,ws);var ig=Rs(qB,'IndexOutOfBoundsException',43);bi(67,43,iB,xs,ys);var Yf=Rs(qB,'ArrayIndexOutOfBoundsException',67);bi(93,11,iB,zs,As);var Zf=Rs(qB,'ArrayStoreException',93);bi(13,11,EC,it,jt);var gg=Rs(qB,'IllegalArgumentException',13);bi(10,11,{4:1,14:1,10:1,11:1,7:1},kt,lt);var hg=Rs(qB,'IllegalStateException',10);var yt;bi(155,11,iB,Et);var lg=Rs(qB,'NegativeArraySizeException',155);var Jt,Kt,Lt,Mt;bi(29,13,{4:1,14:1,13:1,29:1,11:1,7:1},Ot);var ng=Rs(qB,'NumberFormatException',29);bi(42,1,{4:1,42:1},Pt);_.W=function Qt(a){var b;if(de(a,42)){b=$d(a,42);return this.c==b.c&&this.d==b.d&&this.a==b.a&&this.b==b.b}return false};_.Y=function Rt(){return jx(sd(kd(pg,1),$A,1,5,[xt(this.c),this.a,this.d,this.b]))};_.Z=function St(){return this.a+'.'+this.d+'('+(this.b!=null?this.b:'Unknown Source')+(this.c>=0?':'+this.c:'')+')'};_.c=0;var rg=Rs(qB,'StackTraceElement',42);bi(108,76,{85:1},ku);var sg=Rs(qB,'StringBuffer',108);bi(86,76,{85:1},nu,ou);var tg=Rs(qB,'StringBuilder',86);bi(118,43,iB,pu);var ug=Rs(qB,'StringIndexOutOfBoundsException',118);bi(228,1,{});var qu;bi(54,11,iB,tu,uu);var xg=Rs(qB,'UnsupportedOperationException',54);bi(44,1,FC);_.ib=function wu(a){return vu(this,$d(a,44))};_.W=function xu(a){var b;if(a===this){return true}if(!de(a,44)){return false}b=$d(a,44);return Xt(this.a,b.a)};_.Y=function zu(){return jz(this.a)};_.Z=function Au(){return this.a};var yg=Rs(GC,'Charset',44);bi(107,13,EC,Bu);var zg=Rs(GC,'IllegalCharsetNameException',107);bi(74,13,{4:1,14:1,13:1,11:1,7:1,74:1},Cu);var Ag=Rs(GC,'UnsupportedCharsetException',74);bi(188,1,{102:1});_.getOrDefault=function Mu(a,b){var c;return c=ie(a)?av(this,a):Lu(Mx(this.a,a)),c==null&&!(ie(a)?bv(this,a):!!Mx(this.a,a))?b:c};_.putIfAbsent=function Su(a,b){var c;return c=ie(a)?av(this,a):Lu(Mx(this.a,a)),c!=null?c:ie(a)?dv(this,a,b):Nx(this.a,a,b)};_.replace=function Uu(a,b){return (ie(a)?bv(this,a):!!Mx(this.a,a))?ie(a)?dv(this,a,b):Nx(this.a,a,b):null};_.clear=function Gu(){fv((new sv(this)).a)};_.containsKey=function Hu(a){return !!Eu(this,a,false)};_.containsValue=function Iu(a){var b,c,d;for(c=new Bv((new sv(this)).a);c.b;){b=zv(c);d=b.Vb();if(ke(a)===ke(d)||a!=null&&wb(a,d)){return true}}return false};_.W=function Ju(a){var b,c,d;if(a===this){return true}if(!de(a,31)){return false}d=$d(a,102);if(this.a.c+this.b.c!=d.a.c+d.b.c){return false}for(c=new Bv((new sv(d)).a);c.b;){b=zv(c);if(!Du(this,b)){return false}}return true};_.get=function Ku(a){return Lu(Eu(this,a,false))};_.Y=function Nu(){return xx(new sv(this))};_.isEmpty=function Ou(){return this.a.c+this.b.c==0};_.keySet=function Pu(){return new Sv(this)};_.put=function Qu(a,b){throw Dh(new uu('Put not supported on this map'))};_.putAll=function Ru(a){var b,c;Uy(a);for(c=new Bv((new sv($d(a,31))).a);c.b;){b=zv(c);cv(this,b.Ub(),b.Vb())}};_.remove=function Tu(a){return Lu(Eu(this,a,true))};_.size=function Vu(){return gv((new sv(this)).a)};_.Z=function Wu(){var a,b,c;c=new ty('{','}');for(b=new Bv((new sv(this)).a);b.b;){a=zv(b);sy(c,Fu(this,a.Ub())+'='+Fu(this,a.Vb()))}return !c.a?c.c:c.e.length==0?c.a.a:c.a.a+(''+c.e)};_.values=function Xu(){return new aw(this)};var Qg=Rs(vC,'AbstractMap',188);bi(129,188,{102:1});_.clear=function hv(){fv(this)};_.containsKey=function iv(a){return Yu(this,a)};_.containsValue=function jv(a){return Zu(this,a)};_.get=function kv(a){return _u(this,a)};_.put=function lv(a,b){return cv(this,a,b)};_.remove=function mv(a){return ev(this,a)};_.size=function nv(){return gv(this)};var Eg=Rs(vC,'AbstractHashMap',129);bi(187,185,{82:1});_.W=function ov(a){var b;if(a===this){return true}if(!de(a,82)){return false}b=$d(a,82);if(b.size()!=this.size()){return false}return To(this,b)};_.Y=function pv(){return xx(this)};_.removeAll=function qv(a){var b,c,d,e;Uy(a);e=this.size();if(e<a.size()){for(b=this.Eb();b.Rb();){c=b.Sb();a.contains(c)&&b.Tb()}}else{for(d=a.Eb();d.Rb();){c=d.Sb();this.remove(c)}}return e!=this.size()};var Rg=Rs(vC,'AbstractSet',187);bi(36,187,{82:1},sv);_.clear=function tv(){fv(this.a)};_.contains=function uv(a){return rv(this,a)};_.Eb=function vv(){return new Bv(this.a)};_.remove=function wv(a){var b;if(rv(this,a)){b=$d(a,34).Ub();ev(this.a,b);return true}return false};_.size=function xv(){return gv(this.a)};var Dg=Rs(vC,'AbstractHashMap/EntrySet',36);bi(46,1,{},Bv);_.Sb=function Dv(){return zv(this)};_.Rb=function Cv(){return this.b};_.Tb=function Ev(){Av(this)};_.b=false;var Cg=Rs(vC,'AbstractHashMap/EntrySetIterator',46);bi(90,1,{},Gv);_.Rb=function Hv(){return this.a<this.c.size()};_.Sb=function Iv(){return Sy(this.a<this.c.size()),this.c.getAtIndex(this.b=this.a++)};_.Tb=function Jv(){Fv(this)};_.a=0;_.b=-1;var Fg=Rs(vC,'AbstractList/IteratorImpl',90);bi(119,90,{},Kv);_.Tb=function Lv(){Fv(this)};var Gg=Rs(vC,'AbstractList/ListIteratorImpl',119);bi(91,186,{38:1},Mv);_.addAtIndex=function Nv(a,b){Wy(a,this.b);this.c.addAtIndex(this.a+a,b);++this.b};_.getAtIndex=function Ov(a){Ty(a,this.b);return this.c.getAtIndex(this.a+a)};_.removeAtIndex=function Pv(a){var b;Ty(a,this.b);b=this.c.removeAtIndex(this.a+a);--this.b;return b};_.setAtIndex=function Qv(a,b){Ty(a,this.b);return this.c.setAtIndex(this.a+a,b)};_.size=function Rv(){return this.b};_.a=0;_.b=0;var Hg=Rs(vC,'AbstractList/SubList',91);bi(121,187,{82:1},Sv);_.clear=function Tv(){fv(this.a)};_.contains=function Uv(a){return Yu(this.a,a)};_.Eb=function Vv(){var a;a=new Bv((new sv(this.a)).a);return new Yv(a)};_.remove=function Wv(a){if(Yu(this.a,a)){ev(this.a,a);return true}return false};_.size=function Xv(){return gv(this.a)};var Kg=Rs(vC,'AbstractMap/1',121);bi(122,1,{},Yv);_.Rb=function Zv(){return this.a.b};_.Sb=function $v(){var a;a=zv(this.a);return a.Ub()};_.Tb=function _v(){Av(this.a)};var Jg=Rs(vC,'AbstractMap/1/1',122);bi(123,185,{},aw);_.clear=function bw(){fv(this.a)};_.contains=function cw(a){return Zu(this.a,a)};_.Eb=function dw(){var a;a=new Bv((new sv(this.a)).a);return new fw(a)};_.size=function ew(){return gv(this.a)};var Mg=Rs(vC,'AbstractMap/2',123);bi(124,1,{},fw);_.Rb=function gw(){return this.a.b};_.Sb=function hw(){var a;a=zv(this.a);return a.Vb()};_.Tb=function iw(){Av(this.a)};var Lg=Rs(vC,'AbstractMap/2/1',124);bi(120,1,IC);_.W=function jw(a){var b;if(!de(a,34)){return false}b=$d(a,34);return qy(this.a,b.Ub())&&qy(this.b,b.Vb())};_.Ub=function kw(){return this.a};_.Vb=function lw(){return this.b};_.Y=function mw(){return ry(this.a)^ry(this.b)};_.Wb=function nw(a){var b;b=this.b;this.b=a;return b};_.Z=function ow(){return this.a+'='+this.b};var Ng=Rs(vC,'AbstractMap/AbstractEntry',120);bi(92,120,IC,pw);var Og=Rs(vC,'AbstractMap/SimpleEntry',92);bi(189,1,IC);_.W=function qw(a){var b;if(!de(a,34)){return false}b=$d(a,34);return qy(this.b.value[0],b.Ub())&&qy(jy(this),b.Vb())};_.Y=function rw(){return ry(this.b.value[0])^ry(jy(this))};_.Z=function sw(){return this.b.value[0]+'='+jy(this)};var Pg=Rs(vC,'AbstractMapEntry',189);bi(27,186,zC,Jw,Kw,Lw);_.addAtIndex=function Mw(a,b){uw(this,a,b)};_.add=function Nw(a){return vw(this,a)};_.addAllAtIndex=function Ow(a,b){return ww(this,a,b)};_.addAll=function Pw(a){return xw(this,a)};_.clear=function Qw(){this.a=od(pg,$A,1,0,5,1)};_.contains=function Rw(a){return zw(this,a,0)!=-1};_.getAtIndex=function Sw(a){return yw(this,a)};_.indexOf=function Tw(a){return zw(this,a,0)};_.isEmpty=function Uw(){return this.a.length==0};_.Eb=function Vw(){return new dx(this)};_.lastIndexOf=function Ww(a){return Aw(this,a)};_.removeAtIndex=function Xw(a){return Cw(this,a)};_.remove=function Yw(a){return Dw(this,a)};_.Gb=function Zw(a,b){Ew(this,a,b)};_.setAtIndex=function $w(a,b){return Fw(this,a,b)};_.size=function _w(){return this.a.length};_.toArray=function ax(){return Hw(this)};_.Fb=function bx(a){return Iw(this,a)};var Tg=Rs(vC,'ArrayList',27);bi(30,1,{},dx);_.Rb=function ex(){return this.a<this.c.a.length};_.Sb=function fx(){return cx(this)};_.Tb=function gx(){Yy(this.b!=-1);Cw(this.c,this.a=this.b);this.b=-1};_.a=0;_.b=-1;var Sg=Rs(vC,'ArrayList/1',30);bi(135,186,zC,qx);_.contains=function rx(a){return gp(this,a)!=-1};_.getAtIndex=function sx(a){return Ty(a,this.a.length),this.a[a]};_.setAtIndex=function tx(a,b){var c;c=(Ty(a,this.a.length),this.a[a]);rd(this.a,a,b);return c};_.size=function ux(){return this.a.length};_.toArray=function vx(){return px(this,od(pg,$A,1,this.a.length,5,1))};_.Fb=function wx(a){return px(this,a)};var Ug=Rs(vC,'Arrays/ArrayList',135);var zx;bi(152,1,UB,Cx);_.Xb=function Dx(a,b){return Bx($d(a,35),$d(b,35))};_.W=function Ex(a){return this===a};var Vg=Rs(vC,'Comparators/NaturalOrderComparator',152);bi(151,11,iB,Hx);var Wg=Rs(vC,'ConcurrentModificationException',151);bi(31,129,{4:1,31:1,102:1},Jx);var Xg=Rs(vC,'HashMap',31);bi(134,1,{},Px);_.Eb=function Qx(){return new Rx(this)};_.c=0;var Zg=Rs(vC,'InternalHashCodeMap',134);bi(95,1,{},Rx);_.Sb=function Tx(){return this.d=this.a[this.c++],this.d};_.Rb=function Sx(){var a;if(this.c<this.a.length){return true}a=this.b.next();if(!a.done){this.a=a.value[1];this.c=0;return true}return false};_.Tb=function Ux(){Ox(this.e,this.d.Ub());this.c!=0&&--this.c};_.c=0;_.d=null;var Yg=Rs(vC,'InternalHashCodeMap/1',95);var Vx;bi(131,1,{},dy);_.Eb=function ey(){return new fy(this)};_.c=0;_.d=0;var ah=Rs(vC,'InternalStringMap',131);bi(94,1,{},fy);_.Sb=function hy(){return this.c=this.a,this.a=this.b.next(),new ky(this.d,this.c,this.d.d)};_.Rb=function gy(){return !this.a.done};_.Tb=function iy(){cy(this.d,this.c.value[0])};var $g=Rs(vC,'InternalStringMap/1',94);bi(132,189,IC,ky);_.Ub=function ly(){return this.b.value[0]};_.Vb=function my(){return jy(this)};_.Wb=function ny(a){return by(this.a,this.b.value[0],a)};_.c=0;var _g=Rs(vC,'InternalStringMap/2',132);bi(96,11,iB,oy,py);var bh=Rs(vC,'NoSuchElementException',96);bi(87,1,{},ty);_.Z=function uy(){return !this.a?this.c:this.e.length==0?this.a.a:this.a.a+(''+this.e)};var dh=Rs(vC,'StringJoiner',87);bi(150,1,{},vy);var fh=Rs('java.util.logging','Logger',150);bi(88,44,FC);var Dy,Ey,Fy;var ih=Rs(KC,'EmulatedCharset',88);bi(89,88,FC,Iy);_.Yb=function Jy(a,b,c){var d,e;d=od(ne,UB,8,c,15,1);for(e=0;e<c;++e){d[e]=a[b+e]&255&mB}return d};var gh=Rs(KC,'EmulatedCharset/LatinCharset',89);bi(114,88,FC,Ky);_.Yb=function Ly(a,b,c){var d,e,f,g,h,i,j,k;f=0;for(j=0;j<c;){++f;e=a[b+j];if((e&192)==128){throw Dh(new jt(LC))}else if((e&128)==0){++j}else if((e&224)==192){j+=2}else if((e&240)==224){j+=3}else if((e&248)==240){j+=4}else{throw Dh(new jt(LC))}if(j>c){throw Dh(new ws(LC))}}g=od(ne,UB,8,f,15,1);k=0;h=0;for(i=0;i<c;){e=a[b+i++];if((e&128)==0){h=1;e&=127}else if((e&224)==192){h=2;e&=31}else if((e&240)==224){h=3;e&=15}else if((e&248)==240){h=4;e&=7}else if((e&252)==248){h=5;e&=3}while(--h>0){d=a[b+i++];if((d&192)!=128){throw Dh(new jt('Invalid UTF8 sequence at '+(b+i-1)+', byte='+(d>>>0).toString(16)))}e=e<<6|d&63}k+=Ks(e,g,k)}return g};var hh=Rs(KC,'EmulatedCharset/UtfCharset',114);bi(110,1,{},pz);_.db=function qz(){var b;try{return lz(this)}catch(a){a=Ch(a);if(de(a,14)){b=a;wz(this.a,FB);Uz();console.log('background processing failed: '+b);return this.c=false}else throw Dh(a)}};_.b=false;_.c=false;_.d=0;_.e=false;var kh=Rs(MC,'BackgroundProcessor',110);bi(133,1,{},rz);_.db=function sz(){return nz(this.a)};var jh=Rs(MC,'BackgroundProcessor/lambda$0$Type',133);bi(103,1,{},yz);_.Zb=function zz(a){switch(a.order){case 'context-init':Qz(a.data);Tz(be(a.data2));this.a=new pz(this);break;case 'view-change':Az(a.data);}};_.b=null;var lh=Rs(MC,'BackgroundWorker/Slave',103);bi(173,1,{},Bz);_.push=function Dz(){Az(this)};_.page=0;_.subsample=0;var mh=Rs(MC,'BackgroundWorker/ViewState',173);bi(45,1,{},Ez,Fz);var nh=Rs(MC,'BackgroundWorker/WorkerMessage',45);var Gz=-1,Hz=1,Iz,Jz,Kz;bi(136,1,{},lA);_.c=0;_.f=0;_.g=0;_.k=0;_.n=false;_.o=false;var uh=Rs(MC,'PageDecoder',136);bi(137,1,{106:1},mA);_.Qb=function nA(){jA(this.a)};var oh=Rs(MC,'PageDecoder/0methodref$pageChanged$Type',137);bi(140,1,{},oA);_.hb=function pA(a){var b;if(a.readyState==4){--this.a.c;if(a.status==200){b=dA(this.a,this.b);b.a=ui(a.response);b.b=b.a.byteLength;this.a.f=Eh(this.a.f,b.b);Vz(this.a);oz(this.a.a.a);cA(this.a,this.b);Xz(this.a)}else{wz(this.a.a,FB);$d(av(this.a.d,this.b),49).c=false}}};var ph=Rs(MC,'PageDecoder/1',140);bi(49,1,{49:1},qA);_.b=0;_.c=false;var qh=Rs(MC,'PageDecoder/FileItem',49);bi(32,1,{35:1,32:1},uA);_.ib=function vA(a){return rA(this,$d(a,32))};_.b=false;_.c=0;_.e=0;_.f=10000;var rh=Rs(MC,'PageDecoder/PageItem',32);bi(138,1,{168:1},wA);_.qb=function xA(){hA(this.a,this.b)};var sh=Rs(MC,'PageDecoder/lambda$0$Type',138);bi(139,1,{},yA);_.hb=function zA(a){iA(this.a,this.b,a)};var th=Rs(MC,'PageDecoder/lambda$1$Type',139);bi(141,1,{},IA);_.c=-1;_.e=0;_.k=0;_.n=0;var zh=Rs(MC,'TileRenderer',141);bi(142,1,{106:1},JA);_.Qb=function KA(){HA(this.a)};var vh=Rs(MC,'TileRenderer/0methodref$viewChanged$Type',142);bi(51,1,{51:1},LA);_.a=false;_.b=0;var wh=Rs(MC,'TileRenderer/CachedItem',51);bi(50,1,{50:1},OA,QA);_.W=function RA(a){var b;if(this===a)return true;if(a==null)return false;if(xh!=yb(a))return false;b=$d(a,50);if(this.a!=b.a)return false;if(this.c!=b.c)return false;if(this.d!=b.d)return false;if(this.b!=b.b)return false;return true};_.Y=function SA(){var a;a=31+this.a;a=31*a+this.c;a=31*a+this.d;a=31*a+this.b;return a};_.a=0;_.b=0;_.c=0;_.d=0;var xh=Rs(MC,'TileRenderer/TileInfo',50);bi(143,1,{},TA);_.W=function VA(a){return this===a};_.Xb=function UA(a,b){var c;return c=Th($d(a.Vb(),51).b,$d(b.Vb(),51).b),Gh(c,0)>0?1:Gh(c,0)<0?-1:0};var yh=Rs(MC,'TileRenderer/lambda$0$Type',143);var ne=Ts('char','C');var oe=Ts('int','I');var pe=Ts('long','J');var me=Ts('byte','B');var Ah=Ts('short','S');Bs();_=ei('java.lang.Boolean');_.$isInstance=Ds;_=ei('java.lang.CharSequence');_.$isInstance=Gs;_=ei('java.lang.Comparable');_.$isInstance=et;_=ei('java.lang.Double');_.$isInstance=gt;_=ei('java.lang.Number');_.$isInstance=Ep;_=ei('java.lang.String');_.$isInstance=Zt;_=ei('java.lang.Throwable');_.of=Zb;_=ei('pl.djvuhtml5.client.BackgroundWorker.ViewState',Bz);_.cast=Cz;var WA=(pc(),sc);var gwtOnLoad=gwtOnLoad=Zh;Xh(hi);$h('permProps',[[['locale',xC],['user.agent',PB]]]);