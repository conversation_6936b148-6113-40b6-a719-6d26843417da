.sm2-bar-ui {
  font-size: 20px;
}

.sm2-bar-ui.compact {
  max-width: 90%;
}

.sm2-progress .sm2-progress-ball {
  width: 0.5333em;
  height: 1.9333em;
  border-radius: 0;
}

.sm2-progress .sm2-progress-track {
  height: 0.15em;
  background: white;
}

.sm2-bar-ui .sm2-main-controls,
.sm2-bar-ui .sm2-playlist-drawer {
  background-color: transparent;
}

.sm2-bar-ui .sm2-inline-texture {
  background: transparent;
}

.rating .glyphicon-star {
  color: gray;
}

.rating .glyphicon-star.good {
  color: white;
}

body {
  overflow: hidden;
  background: #272b30;
  color: #aaa;
}

#main {
  position: absolute;
  width: 100%;
  height: 100%;
}

#area {
  width: 80%;
  height: 80%;
  margin: 5% auto;
  max-width: 1250px;
  overflow: hidden;
}

#area iframe {
  border: none;
}

#prev {
  left: 40px;
}

#next {
  right: 40px;
}

pre {
  display: block;
  margin: 1em 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -moz-fixed, sans-serif;
  column-count: 2;
  -webkit-columns: 2;
  -moz-columns: 2;
  column-gap: 20px;
  -moz-column-gap: 20px;
  -webkit-column-gap: 20px;
  position: relative;
}
