#
# Copyright 2015 <PERSON>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# Source: https://github.com/JordanMilne/Advocate

from urllib3 import HTTPConnectionPool, HTTPSConnectionPool

from .connection import (
    ValidatingHTTPConnection,
    ValidatingHTTPSConnection,
)

# Don't silently break if the private API changes across urllib3 versions
assert(hasattr(HTTPConnectionPool, 'ConnectionCls'))
assert(hasattr(HTTPSConnectionPool, 'ConnectionCls'))
assert(hasattr(HTTPConnectionPool, 'scheme'))
assert(hasattr(HTTPSConnectionPool, 'scheme'))


class ValidatingHTTPConnectionPool(HTTPConnectionPool):
    scheme = 'http'
    ConnectionCls = ValidatingHTTPConnection


class ValidatingHTTPSConnectionPool(HTTPSConnectionPool):
    scheme = 'https'
    ConnectionCls = ValidatingHTTPSConnection
