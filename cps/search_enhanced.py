# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced Search Features
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

import json
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple

from flask import Blueprint, request, jsonify, render_template
from flask_babel import gettext as _
from sqlalchemy.sql.expression import func, and_, or_

from . import logger, db, calibre_db, config
from .cw_login import current_user
from .usermanagement import login_required_if_no_ano
from .render_template import render_title_template
from .pagination import Pagination
from .elasticsearch_service import elasticsearch_service

search_enhanced = Blueprint('search_enhanced', __name__)
log = logger.create()


class SearchQueryBuilder:
    """Advanced search query builder for both SQL and Elasticsearch"""
    
    def __init__(self):
        self.filters = {}
        self.sort_options = []
        self.facets = []
    
    def add_text_filter(self, field: str, value: str, operator: str = "contains"):
        """Add text-based filter"""
        if value and value.strip():
            self.filters[field] = {
                'value': value.strip(),
                'operator': operator
            }
    
    def add_range_filter(self, field: str, min_val: Any = None, max_val: Any = None):
        """Add range filter (for dates, ratings, etc.)"""
        if min_val is not None or max_val is not None:
            self.filters[field] = {
                'min': min_val,
                'max': max_val,
                'type': 'range'
            }
    
    def add_list_filter(self, field: str, values: List[str], include: bool = True):
        """Add list-based filter (for tags, authors, etc.)"""
        if values:
            self.filters[field] = {
                'values': values,
                'include': include,
                'type': 'list'
            }
    
    def add_sort(self, field: str, direction: str = "asc"):
        """Add sort option"""
        self.sort_options.append({
            'field': field,
            'direction': direction
        })
    
    def build_elasticsearch_query(self) -> Dict[str, Any]:
        """Build Elasticsearch query from filters"""
        query = {
            "query": {
                "bool": {
                    "must": [],
                    "should": [],
                    "filter": []
                }
            }
        }
        
        # Add text filters
        for field, filter_data in self.filters.items():
            if filter_data.get('type') == 'range':
                # Range filter
                range_query = {}
                if filter_data.get('min') is not None:
                    range_query['gte'] = filter_data['min']
                if filter_data.get('max') is not None:
                    range_query['lte'] = filter_data['max']
                
                query["query"]["bool"]["filter"].append({
                    "range": {field: range_query}
                })
            
            elif filter_data.get('type') == 'list':
                # List filter
                if filter_data.get('include', True):
                    query["query"]["bool"]["filter"].append({
                        "terms": {field: filter_data['values']}
                    })
                else:
                    query["query"]["bool"]["must_not"] = query["query"]["bool"].get("must_not", [])
                    query["query"]["bool"]["must_not"].append({
                        "terms": {field: filter_data['values']}
                    })
            
            else:
                # Text filter
                operator = filter_data.get('operator', 'contains')
                value = filter_data['value']
                
                if operator == 'exact':
                    query["query"]["bool"]["filter"].append({
                        "term": {f"{field}.keyword": value}
                    })
                elif operator == 'starts_with':
                    query["query"]["bool"]["must"].append({
                        "prefix": {field: value.lower()}
                    })
                else:  # contains (default)
                    query["query"]["bool"]["must"].append({
                        "match": {field: value}
                    })
        
        # Add sorting
        if self.sort_options:
            query["sort"] = []
            for sort_opt in self.sort_options:
                field = sort_opt['field']
                direction = sort_opt['direction']
                
                # Use keyword field for exact sorting where available
                if field in ['title', 'authors', 'series', 'publisher']:
                    field = f"{field}.keyword"
                
                query["sort"].append({field: {"order": direction}})
        else:
            # Default sort by relevance, then title
            query["sort"] = [
                {"_score": {"order": "desc"}},
                {"title.keyword": {"order": "asc"}}
            ]
        
        return query


@search_enhanced.route("/search/advanced", methods=["GET"])
@login_required_if_no_ano
def advanced_search_interface():
    """Enhanced advanced search interface"""
    # Get available options for dropdowns
    tags = calibre_db.session.query(db.Tags).order_by(db.Tags.name).all()
    authors = calibre_db.session.query(db.Authors).order_by(db.Authors.name).all()
    series = calibre_db.session.query(db.Series).order_by(db.Series.name).all()
    publishers = calibre_db.session.query(db.Publishers).order_by(db.Publishers.name).all()
    languages = calibre_db.speaking_language() if current_user.filter_language() == "all" else None
    
    # Get available formats
    formats = calibre_db.session.query(db.Data.format)\
        .group_by(db.Data.format)\
        .order_by(db.Data.format).all()
    
    return render_title_template('search_enhanced.html',
                                 title=_("Advanced Search"),
                                 tags=tags,
                                 authors=authors,
                                 series=series,
                                 publishers=publishers,
                                 languages=languages,
                                 formats=formats,
                                 page="search_enhanced")


@search_enhanced.route("/search/execute", methods=["POST"])
@login_required_if_no_ano
def execute_enhanced_search():
    """Execute enhanced search with advanced filters"""
    try:
        search_data = request.get_json()
        if not search_data:
            return jsonify({'error': 'No search data provided'}), 400
        
        # Build search query
        builder = SearchQueryBuilder()
        
        # Text filters
        if search_data.get('title'):
            builder.add_text_filter('title', search_data['title'], 
                                  search_data.get('title_operator', 'contains'))
        
        if search_data.get('author'):
            builder.add_text_filter('authors', search_data['author'],
                                  search_data.get('author_operator', 'contains'))
        
        if search_data.get('description'):
            builder.add_text_filter('description', search_data['description'])
        
        # List filters
        if search_data.get('tags'):
            builder.add_list_filter('tags', search_data['tags'], 
                                  search_data.get('include_tags', True))
        
        if search_data.get('series'):
            builder.add_list_filter('series', search_data['series'])
        
        if search_data.get('publishers'):
            builder.add_list_filter('publisher', search_data['publishers'])
        
        if search_data.get('languages'):
            builder.add_list_filter('languages', search_data['languages'])
        
        if search_data.get('formats'):
            builder.add_list_filter('formats', search_data['formats'])
        
        # Range filters
        if search_data.get('rating_min') or search_data.get('rating_max'):
            builder.add_range_filter('rating', 
                                   search_data.get('rating_min'),
                                   search_data.get('rating_max'))
        
        if search_data.get('date_from') or search_data.get('date_to'):
            builder.add_range_filter('pubdate',
                                   search_data.get('date_from'),
                                   search_data.get('date_to'))
        
        # Sorting
        sort_field = search_data.get('sort_field', 'relevance')
        sort_direction = search_data.get('sort_direction', 'desc')
        
        if sort_field != 'relevance':
            builder.add_sort(sort_field, sort_direction)
        
        # Pagination
        page = int(search_data.get('page', 1))
        per_page = int(search_data.get('per_page', config.config_books_per_page))
        offset = (page - 1) * per_page
        
        # Execute search
        if elasticsearch_service.is_available():
            # Use Elasticsearch
            es_query = builder.build_elasticsearch_query()
            es_query['from'] = offset
            es_query['size'] = per_page
            
            book_ids, total_count = elasticsearch_service.search_books_advanced(es_query)
            
            if book_ids:
                # Get books from database
                books = calibre_db.session.query(db.Books)\
                    .filter(db.Books.id.in_(book_ids))\
                    .all()
                
                # Maintain order from Elasticsearch
                books_dict = {book.id: book for book in books}
                ordered_books = [books_dict[book_id] for book_id in book_ids if book_id in books_dict]
                
                entries = calibre_db.order_authors(ordered_books, list_return=True, combined=True)
            else:
                entries = []
                total_count = 0
        else:
            # Fallback to SQL search (simplified)
            entries, total_count = _execute_sql_search(builder, offset, per_page)
        
        # Prepare response
        results = []
        for entry in entries:
            book = entry.Books if hasattr(entry, 'Books') else entry
            results.append({
                'id': book.id,
                'title': book.title,
                'authors': [author.name for author in book.authors],
                'series': book.series[0].name if book.series else None,
                'rating': book.ratings[0].rating if book.ratings else 0,
                'tags': [tag.name for tag in book.tags],
                'has_cover': book.has_cover,
                'formats': [data.format for data in book.data]
            })
        
        # Calculate pagination
        total_pages = (total_count + per_page - 1) // per_page
        
        return jsonify({
            'results': results,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'total_pages': total_pages,
                'has_prev': page > 1,
                'has_next': page < total_pages
            },
            'search_info': {
                'elasticsearch_used': elasticsearch_service.is_available(),
                'query_time': 0  # Could add timing if needed
            }
        })
        
    except Exception as e:
        log.error("Enhanced search failed: %s", e)
        return jsonify({'error': str(e)}), 500


def _execute_sql_search(builder: SearchQueryBuilder, offset: int, limit: int) -> Tuple[List, int]:
    """Fallback SQL search implementation"""
    # This is a simplified fallback - in a real implementation,
    # you'd convert the SearchQueryBuilder filters to SQL WHERE clauses
    query = calibre_db.session.query(db.Books)
    
    # Apply basic filters (simplified example)
    for field, filter_data in builder.filters.items():
        if field == 'title' and filter_data.get('value'):
            query = query.filter(func.lower(db.Books.title).like(f"%{filter_data['value'].lower()}%"))
        elif field == 'authors' and filter_data.get('value'):
            query = query.filter(db.Books.authors.any(
                func.lower(db.Authors.name).like(f"%{filter_data['value'].lower()}%")
            ))
    
    total_count = query.count()
    results = query.offset(offset).limit(limit).all()
    
    return calibre_db.order_authors(results, list_return=True, combined=True), total_count


@search_enhanced.route("/search/suggestions", methods=["GET"])
@login_required_if_no_ano
def search_suggestions():
    """Get search suggestions for autocomplete"""
    query = request.args.get('q', '').strip()
    field = request.args.get('field', 'title')
    limit = int(request.args.get('limit', 10))
    
    if not query or len(query) < 2:
        return jsonify([])
    
    suggestions = []
    
    try:
        if field == 'title':
            results = calibre_db.session.query(db.Books.title)\
                .filter(func.lower(db.Books.title).like(f"%{query.lower()}%"))\
                .distinct().limit(limit).all()
            suggestions = [r[0] for r in results]
        
        elif field == 'author':
            results = calibre_db.session.query(db.Authors.name)\
                .filter(func.lower(db.Authors.name).like(f"%{query.lower()}%"))\
                .distinct().limit(limit).all()
            suggestions = [r[0] for r in results]
        
        elif field == 'tag':
            results = calibre_db.session.query(db.Tags.name)\
                .filter(func.lower(db.Tags.name).like(f"%{query.lower()}%"))\
                .distinct().limit(limit).all()
            suggestions = [r[0] for r in results]
        
        elif field == 'series':
            results = calibre_db.session.query(db.Series.name)\
                .filter(func.lower(db.Series.name).like(f"%{query.lower()}%"))\
                .distinct().limit(limit).all()
            suggestions = [r[0] for r in results]
    
    except Exception as e:
        log.error("Failed to get search suggestions: %s", e)
    
    return jsonify(suggestions)
