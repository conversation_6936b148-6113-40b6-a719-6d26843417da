# -*- coding: utf-8 -*-

#  This file is part of the Calibre-Web (https://github.com/janeczku/calibre-web)
#    Copyright (C) 2024 Enhanced by AI Assistant
#
#  This program is free software: you can redistribute it and/or modify
#  it under the terms of the GNU General Public License as published by
#  the Free Software Foundation, either version 3 of the License, or
#  (at your option) any later version.
#
#  This program is distributed in the hope that it will be useful,
#  but WITHOUT ANY WARRANTY; without even the implied warranty of
#  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#  GNU General Public License for more details.
#
#  You should have received a copy of the GNU General Public License
#  along with this program. If not, see <http://www.gnu.org/licenses/>.

from typing import Dict, Any, List
from datetime import datetime

from . import logger, calibre_db, db
from .elasticsearch_service import elasticsearch_service

log = logger.create()


class BookIndexer:
    """Handles indexing of books into Elasticsearch"""
    
    def __init__(self):
        self.elasticsearch = elasticsearch_service
    
    def extract_book_data(self, book) -> Dict[str, Any]:
        """Extract book data for indexing"""
        try:
            # Extract basic book information
            book_data = {
                "id": book.id,
                "title": book.title or "",
                "sort": book.sort or "",
                "path": book.path or "",
                "has_cover": book.has_cover or False,
                "uuid": book.uuid or "",
                "isbn": "",
                "description": "",
                "pubdate": None,
                "rating": 0,
                "authors": [],
                "tags": [],
                "series": "",
                "series_index": 0,
                "publisher": "",
                "languages": [],
                "formats": []
            }
            
            # Extract authors
            if book.authors:
                book_data["authors"] = [author.name for author in book.authors]
            
            # Extract tags
            if book.tags:
                book_data["tags"] = [tag.name for tag in book.tags]
            
            # Extract series information
            if book.series:
                series = book.series[0] if book.series else None
                if series:
                    book_data["series"] = series.name
                    # Get series index from the link table
                    series_link = calibre_db.session.query(db.books_series_link).filter(
                        db.books_series_link.c.book == book.id,
                        db.books_series_link.c.series == series.id
                    ).first()
                    if series_link:
                        book_data["series_index"] = series_link.series_index or 0
            
            # Extract publisher
            if book.publishers:
                publisher = book.publishers[0] if book.publishers else None
                if publisher:
                    book_data["publisher"] = publisher.name
            
            # Extract languages
            if book.languages:
                book_data["languages"] = [lang.lang_code for lang in book.languages]
            
            # Extract publication date
            if book.pubdate:
                book_data["pubdate"] = book.pubdate.isoformat() if book.pubdate else None
            
            # Extract rating
            if book.ratings:
                rating = book.ratings[0] if book.ratings else None
                if rating:
                    book_data["rating"] = rating.rating / 2.0  # Convert from 0-10 to 0-5 scale
            
            # Extract identifiers (ISBN, etc.)
            if book.identifiers:
                for identifier in book.identifiers:
                    if identifier.type.lower() == 'isbn':
                        book_data["isbn"] = identifier.val
                        break
            
            # Extract comments/description
            if book.comments:
                comment = book.comments[0] if book.comments else None
                if comment:
                    book_data["description"] = comment.text or ""
            
            # Extract formats
            if book.data:
                book_data["formats"] = [data.format.lower() for data in book.data]
            
            return book_data
            
        except Exception as e:
            log.error("Failed to extract book data for book %s: %s", book.id, e)
            return None
    
    def index_book(self, book_id: int) -> bool:
        """Index a single book by ID"""
        try:
            book = calibre_db.get_book(book_id)
            if not book:
                log.warning("Book not found: %s", book_id)
                return False
            
            book_data = self.extract_book_data(book)
            if not book_data:
                return False
            
            return self.elasticsearch.index_book(book_data)
            
        except Exception as e:
            log.error("Failed to index book %s: %s", book_id, e)
            return False
    
    def index_all_books(self) -> bool:
        """Index all books in the database"""
        try:
            if not self.elasticsearch.is_available():
                log.warning("Elasticsearch not available for indexing")
                return False
            
            log.info("Starting full book indexing...")
            
            # Get all books from the database
            books = calibre_db.session.query(db.Books).all()
            books_data = []
            
            for book in books:
                book_data = self.extract_book_data(book)
                if book_data:
                    books_data.append(book_data)
            
            # Bulk index all books
            success = self.elasticsearch.reindex_all_books(books_data)
            
            if success:
                log.info("Successfully indexed %d books", len(books_data))
            else:
                log.error("Failed to index books")
            
            return success
            
        except Exception as e:
            log.error("Failed to index all books: %s", e)
            return False
    
    def update_book_index(self, book_id: int) -> bool:
        """Update index for a specific book (called when book is modified)"""
        return self.index_book(book_id)
    
    def remove_book_from_index(self, book_id: int) -> bool:
        """Remove a book from the index (called when book is deleted)"""
        try:
            return self.elasticsearch.delete_book(book_id)
        except Exception as e:
            log.error("Failed to remove book %s from index: %s", book_id, e)
            return False
    
    def get_index_status(self) -> Dict[str, Any]:
        """Get status information about the Elasticsearch index"""
        try:
            if not self.elasticsearch.is_available():
                return {
                    "available": False,
                    "connected": False,
                    "error": "Elasticsearch not available or not configured"
                }
            
            # Get index statistics
            stats = {}
            if self.elasticsearch.client and self.elasticsearch.index_name:
                try:
                    index_stats = self.elasticsearch.client.indices.stats(index=self.elasticsearch.index_name)
                    stats = {
                        "available": True,
                        "connected": True,
                        "index_name": self.elasticsearch.index_name,
                        "document_count": index_stats["indices"][self.elasticsearch.index_name]["total"]["docs"]["count"],
                        "index_size": index_stats["indices"][self.elasticsearch.index_name]["total"]["store"]["size_in_bytes"]
                    }
                except Exception as e:
                    stats = {
                        "available": True,
                        "connected": True,
                        "error": f"Failed to get index stats: {e}"
                    }
            
            return stats
            
        except Exception as e:
            return {
                "available": False,
                "connected": False,
                "error": str(e)
            }


# Global instance
book_indexer = BookIndexer()
