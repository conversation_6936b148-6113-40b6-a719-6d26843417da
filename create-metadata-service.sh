#!/bin/bash

# Create Metadata Fetching Service
echo "🔧 Creating Metadata Fetching Service"
echo "===================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Install required Python packages for metadata fetching
print_info "Installing metadata fetching dependencies..."
sudo docker exec $CONTAINER_NAME pip install requests beautifulsoup4 lxml isbnlib

# Create metadata fetching service
print_info "Creating metadata fetching service..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/metadata_fetcher.py << '"'"'EOF'"'"'
"""
Metadata Fetching Service for Calibre-Web
Fetches metadata from external sources and updates book information
"""

import requests
import json
import re
import logging
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import quote_plus
import time

try:
    import isbnlib
    ISBN_AVAILABLE = True
except ImportError:
    ISBN_AVAILABLE = False

from . import logger

class MetadataFetcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": "Calibre-Web Metadata Fetcher 1.0"
        })
        
    def fetch_from_openlibrary(self, title=None, author=None, isbn=None):
        """Fetch metadata from OpenLibrary"""
        try:
            if isbn:
                url = f"https://openlibrary.org/api/books?bibkeys=ISBN:{isbn}&format=json&jscmd=data"
            else:
                # Search by title and author
                query = f"title:{title}" if title else ""
                if author:
                    query += f" author:{author}" if query else f"author:{author}"
                url = f"https://openlibrary.org/search.json?q={quote_plus(query)}&limit=1"
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if isbn and f"ISBN:{isbn}" in data:
                book_data = data[f"ISBN:{isbn}"]
                return self._parse_openlibrary_data(book_data)
            elif "docs" in data and data["docs"]:
                book_data = data["docs"][0]
                return self._parse_openlibrary_search_data(book_data)
                
        except Exception as e:
            logger.error(f"OpenLibrary fetch error: {e}")
            
        return None
    
    def fetch_from_google_books(self, title=None, author=None, isbn=None):
        """Fetch metadata from Google Books API"""
        try:
            if isbn:
                query = f"isbn:{isbn}"
            else:
                query = ""
                if title:
                    query += f"intitle:{title}"
                if author:
                    query += f" inauthor:{author}" if query else f"inauthor:{author}"
            
            url = f"https://www.googleapis.com/books/v1/volumes?q={quote_plus(query)}&maxResults=1"
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            data = response.json()
            
            if "items" in data and data["items"]:
                book_data = data["items"][0]["volumeInfo"]
                return self._parse_google_books_data(book_data)
                
        except Exception as e:
            logger.error(f"Google Books fetch error: {e}")
            
        return None
    
    def fetch_from_goodreads_scrape(self, title=None, author=None):
        """Scrape basic metadata from Goodreads (limited due to anti-scraping)"""
        try:
            if not title:
                return None
                
            query = title
            if author:
                query += f" {author}"
                
            # Search Goodreads
            search_url = f"https://www.goodreads.com/search?q={quote_plus(query)}"
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, "html.parser")
            
            # Find first book result
            book_link = soup.find("a", class_="bookTitle")
            if not book_link:
                return None
                
            book_url = "https://www.goodreads.com" + book_link["href"]
            
            # Get book page
            book_response = self.session.get(book_url, timeout=10)
            book_response.raise_for_status()
            
            book_soup = BeautifulSoup(book_response.content, "html.parser")
            
            return self._parse_goodreads_data(book_soup)
            
        except Exception as e:
            logger.error(f"Goodreads scrape error: {e}")
            
        return None
    
    def _parse_openlibrary_data(self, data):
        """Parse OpenLibrary API response"""
        metadata = {}
        
        if "title" in data:
            metadata["title"] = data["title"]
        if "authors" in data:
            metadata["authors"] = [author["name"] for author in data["authors"]]
        if "publishers" in data:
            metadata["publishers"] = [pub["name"] for pub in data["publishers"]]
        if "publish_date" in data:
            metadata["publish_date"] = data["publish_date"]
        if "subjects" in data:
            metadata["tags"] = [subject["name"] for subject in data["subjects"][:10]]
        if "excerpts" in data and data["excerpts"]:
            metadata["description"] = data["excerpts"][0]["text"]
        if "number_of_pages" in data:
            metadata["pages"] = data["number_of_pages"]
            
        return metadata
    
    def _parse_openlibrary_search_data(self, data):
        """Parse OpenLibrary search response"""
        metadata = {}
        
        if "title" in data:
            metadata["title"] = data["title"]
        if "author_name" in data:
            metadata["authors"] = data["author_name"]
        if "publisher" in data:
            metadata["publishers"] = data["publisher"]
        if "first_publish_year" in data:
            metadata["publish_date"] = str(data["first_publish_year"])
        if "subject" in data:
            metadata["tags"] = data["subject"][:10]  # Limit to 10 tags
        if "isbn" in data:
            metadata["isbn"] = data["isbn"][0] if data["isbn"] else None
            
        return metadata
    
    def _parse_google_books_data(self, data):
        """Parse Google Books API response"""
        metadata = {}
        
        if "title" in data:
            metadata["title"] = data["title"]
        if "authors" in data:
            metadata["authors"] = data["authors"]
        if "publisher" in data:
            metadata["publishers"] = [data["publisher"]]
        if "publishedDate" in data:
            metadata["publish_date"] = data["publishedDate"]
        if "description" in data:
            metadata["description"] = data["description"]
        if "categories" in data:
            metadata["tags"] = data["categories"]
        if "pageCount" in data:
            metadata["pages"] = data["pageCount"]
        if "averageRating" in data:
            metadata["rating"] = data["averageRating"]
        if "industryIdentifiers" in data:
            for identifier in data["industryIdentifiers"]:
                if identifier["type"] in ["ISBN_13", "ISBN_10"]:
                    metadata["isbn"] = identifier["identifier"]
                    break
                    
        return metadata
    
    def _parse_goodreads_data(self, soup):
        """Parse Goodreads page (basic scraping)"""
        metadata = {}
        
        try:
            # Title
            title_elem = soup.find("h1", {"data-testid": "bookTitle"})
            if title_elem:
                metadata["title"] = title_elem.get_text(strip=True)
            
            # Author
            author_elem = soup.find("span", {"data-testid": "name"})
            if author_elem:
                metadata["authors"] = [author_elem.get_text(strip=True)]
            
            # Rating
            rating_elem = soup.find("div", class_="RatingStatistics__rating")
            if rating_elem:
                rating_text = rating_elem.get_text(strip=True)
                rating_match = re.search(r"(\d+\.?\d*)", rating_text)
                if rating_match:
                    metadata["rating"] = float(rating_match.group(1))
            
            # Description
            desc_elem = soup.find("div", {"data-testid": "description"})
            if desc_elem:
                metadata["description"] = desc_elem.get_text(strip=True)
            
            # Genres/Tags
            genre_elems = soup.find_all("span", class_="BookPageMetadataSection__genreButton")
            if genre_elems:
                metadata["tags"] = [elem.get_text(strip=True) for elem in genre_elems[:10]]
                
        except Exception as e:
            logger.error(f"Error parsing Goodreads data: {e}")
            
        return metadata
    
    def fetch_comprehensive_metadata(self, title=None, author=None, isbn=None):
        """Fetch metadata from multiple sources and combine"""
        all_metadata = {}
        sources_used = []
        
        # Try OpenLibrary first
        ol_data = self.fetch_from_openlibrary(title, author, isbn)
        if ol_data:
            all_metadata.update(ol_data)
            sources_used.append("OpenLibrary")
            time.sleep(1)  # Rate limiting
        
        # Try Google Books
        gb_data = self.fetch_from_google_books(title, author, isbn)
        if gb_data:
            # Merge data, preferring more complete information
            for key, value in gb_data.items():
                if key not in all_metadata or (value and len(str(value)) > len(str(all_metadata.get(key, "")))):
                    all_metadata[key] = value
            sources_used.append("Google Books")
            time.sleep(1)  # Rate limiting
        
        # Try Goodreads (be careful with rate limiting)
        if title:  # Only try if we have a title
            gr_data = self.fetch_from_goodreads_scrape(title, author)
            if gr_data:
                # Merge tags and rating specifically
                if "tags" in gr_data:
                    existing_tags = all_metadata.get("tags", [])
                    new_tags = gr_data["tags"]
                    combined_tags = list(set(existing_tags + new_tags))[:15]  # Limit to 15 tags
                    all_metadata["tags"] = combined_tags
                if "rating" in gr_data and "rating" not in all_metadata:
                    all_metadata["rating"] = gr_data["rating"]
                sources_used.append("Goodreads")
                time.sleep(2)  # Longer delay for Goodreads
        
        all_metadata["sources_used"] = sources_used
        all_metadata["fetch_timestamp"] = datetime.now().isoformat()
        
        return all_metadata if all_metadata else None

# Global instance
metadata_fetcher = MetadataFetcher()
EOF'

print_status "Metadata fetching service created"

# Create metadata update service
print_info "Creating metadata update service..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/metadata_updater.py << '"'"'EOF'"'"'
"""
Metadata Update Service for Calibre-Web
Updates book metadata in the database
"""

import os
import logging
from datetime import datetime
from . import logger
from .db import CalibreDB, Books, Authors, Tags, Series, Publishers, Comments
from .metadata_fetcher import metadata_fetcher

class MetadataUpdater:
    def __init__(self):
        self.calibre_db = CalibreDB()

    def update_book_metadata(self, book_id, metadata):
        """Update book metadata in the database"""
        try:
            book = self.calibre_db.session.query(Books).filter(Books.id == book_id).first()
            if not book:
                return {"success": False, "error": "Book not found"}

            updates_made = []

            # Update title
            if "title" in metadata and metadata["title"]:
                old_title = book.title
                book.title = metadata["title"]
                updates_made.append(f"Title: {old_title} -> {book.title}")

            # Update authors
            if "authors" in metadata and metadata["authors"]:
                self._update_authors(book, metadata["authors"])
                updates_made.append(f"Authors updated: {metadata['"'"'authors'"'"']}")

            # Update publishers
            if "publishers" in metadata and metadata["publishers"]:
                self._update_publishers(book, metadata["publishers"])
                updates_made.append(f"Publishers updated: {metadata['"'"'publishers'"'"']}")

            # Update tags
            if "tags" in metadata and metadata["tags"]:
                self._update_tags(book, metadata["tags"])
                updates_made.append(f"Tags updated: {metadata['"'"'tags'"'"']}")

            # Update series
            if "series" in metadata and metadata["series"]:
                self._update_series(book, metadata["series"])
                updates_made.append(f"Series updated: {metadata['"'"'series'"'"']}")

            # Update description/comments
            if "description" in metadata and metadata["description"]:
                self._update_comments(book, metadata["description"])
                updates_made.append("Description updated")

            # Update publish date
            if "publish_date" in metadata and metadata["publish_date"]:
                try:
                    # Try to parse the date
                    if isinstance(metadata["publish_date"], str):
                        if len(metadata["publish_date"]) == 4:  # Just year
                            book.pubdate = datetime(int(metadata["publish_date"]), 1, 1)
                        else:
                            # Try to parse full date
                            book.pubdate = datetime.strptime(metadata["publish_date"], "%Y-%m-%d")
                    updates_made.append(f"Publish date updated: {metadata['"'"'publish_date'"'"']}")
                except:
                    logger.warning(f"Could not parse publish date: {metadata['"'"'publish_date'"'"']}")

            # Commit changes
            self.calibre_db.session.commit()

            return {
                "success": True,
                "updates_made": updates_made,
                "sources_used": metadata.get("sources_used", [])
            }

        except Exception as e:
            self.calibre_db.session.rollback()
            logger.error(f"Error updating book metadata: {e}")
            return {"success": False, "error": str(e)}

    def _update_authors(self, book, authors):
        """Update book authors"""
        # Clear existing authors
        book.authors.clear()

        for author_name in authors:
            author = self.calibre_db.session.query(Authors).filter(Authors.name == author_name).first()
            if not author:
                author = Authors(name=author_name, sort=author_name)
                self.calibre_db.session.add(author)
            book.authors.append(author)

    def _update_publishers(self, book, publishers):
        """Update book publishers"""
        # Clear existing publishers
        book.publishers.clear()

        for publisher_name in publishers:
            publisher = self.calibre_db.session.query(Publishers).filter(Publishers.name == publisher_name).first()
            if not publisher:
                publisher = Publishers(name=publisher_name)
                self.calibre_db.session.add(publisher)
            book.publishers.append(publisher)

    def _update_tags(self, book, tags):
        """Update book tags"""
        # Clear existing tags
        book.tags.clear()

        for tag_name in tags:
            # Clean tag name
            tag_name = tag_name.strip().title()
            if not tag_name:
                continue

            tag = self.calibre_db.session.query(Tags).filter(Tags.name == tag_name).first()
            if not tag:
                tag = Tags(name=tag_name)
                self.calibre_db.session.add(tag)
            book.tags.append(tag)

    def _update_series(self, book, series_name):
        """Update book series"""
        # Clear existing series
        book.series.clear()

        if series_name:
            series = self.calibre_db.session.query(Series).filter(Series.name == series_name).first()
            if not series:
                series = Series(name=series_name, sort=series_name)
                self.calibre_db.session.add(series)
            book.series.append(series)

    def _update_comments(self, book, description):
        """Update book description/comments"""
        # Remove existing comments
        existing_comments = self.calibre_db.session.query(Comments).filter(Comments.book == book.id).all()
        for comment in existing_comments:
            self.calibre_db.session.delete(comment)

        # Add new comment
        if description:
            comment = Comments(text=description, book=book.id)
            self.calibre_db.session.add(comment)

    def fetch_and_update_metadata(self, book_id, title=None, author=None, isbn=None):
        """Fetch metadata from external sources and update book"""
        try:
            # Fetch metadata
            metadata = metadata_fetcher.fetch_comprehensive_metadata(title, author, isbn)

            if not metadata:
                return {"success": False, "error": "No metadata found from external sources"}

            # Update book with fetched metadata
            result = self.update_book_metadata(book_id, metadata)

            if result["success"]:
                result["metadata_fetched"] = metadata

            return result

        except Exception as e:
            logger.error(f"Error in fetch_and_update_metadata: {e}")
            return {"success": False, "error": str(e)}

# Global instance
metadata_updater = MetadataUpdater()
EOF'

print_status "Metadata update service created"

# Add metadata routes to web.py
print_info "Adding metadata routes to web.py..."
sudo docker exec $CONTAINER_NAME bash -c 'cat >> /app/calibre-web/cps/web.py << '"'"'EOF'"'"'

# Metadata Fetching Routes
@app.route("/metadata/fetch/<int:book_id>")
@login_required_if_no_ano
def metadata_fetch_page(book_id):
    """Metadata fetching page for a specific book"""
    from .db import CalibreDB

    calibre_db = CalibreDB()
    book = calibre_db.session.query(db.Books).filter(db.Books.id == book_id).first()

    if not book:
        flash("Book not found", category="error")
        return redirect(url_for("web.index"))

    return render_template("metadata_fetch.html", book=book, title="Fetch Metadata")

@app.route("/api/metadata/fetch", methods=["POST"])
@login_required_if_no_ano
def api_fetch_metadata():
    """API endpoint to fetch and update metadata"""
    try:
        from .metadata_updater import metadata_updater

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        book_id = data.get("book_id")
        title = data.get("title")
        author = data.get("author")
        isbn = data.get("isbn")

        if not book_id:
            return jsonify({"success": False, "error": "Book ID required"}), 400

        # Fetch and update metadata
        result = metadata_updater.fetch_and_update_metadata(book_id, title, author, isbn)

        return jsonify(result)

    except Exception as e:
        log.error(f"Metadata fetch API error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/api/metadata/preview", methods=["POST"])
@login_required_if_no_ano
def api_preview_metadata():
    """API endpoint to preview metadata without updating"""
    try:
        from .metadata_fetcher import metadata_fetcher

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        title = data.get("title")
        author = data.get("author")
        isbn = data.get("isbn")

        # Fetch metadata
        metadata = metadata_fetcher.fetch_comprehensive_metadata(title, author, isbn)

        if metadata:
            return jsonify({"success": True, "metadata": metadata})
        else:
            return jsonify({"success": False, "error": "No metadata found"})

    except Exception as e:
        log.error(f"Metadata preview API error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route("/metadata/bulk")
@login_required_if_no_ano
def metadata_bulk_page():
    """Bulk metadata fetching page"""
    return render_template("metadata_bulk.html", title="Bulk Metadata Fetch")

@app.route("/api/metadata/bulk", methods=["POST"])
@login_required_if_no_ano
def api_bulk_fetch_metadata():
    """API endpoint for bulk metadata fetching"""
    try:
        from .metadata_updater import metadata_updater
        from .db import CalibreDB

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        book_ids = data.get("book_ids", [])
        if not book_ids:
            return jsonify({"success": False, "error": "No book IDs provided"}), 400

        calibre_db = CalibreDB()
        results = []

        for book_id in book_ids:
            try:
                book = calibre_db.session.query(db.Books).filter(db.Books.id == book_id).first()
                if not book:
                    results.append({"book_id": book_id, "success": False, "error": "Book not found"})
                    continue

                # Get book info for fetching
                title = book.title
                author = book.authors[0].name if book.authors else None

                # Fetch and update
                result = metadata_updater.fetch_and_update_metadata(book_id, title, author)
                result["book_id"] = book_id
                result["book_title"] = title
                results.append(result)

                # Rate limiting
                time.sleep(2)

            except Exception as e:
                results.append({"book_id": book_id, "success": False, "error": str(e)})

        return jsonify({"success": True, "results": results})

    except Exception as e:
        log.error(f"Bulk metadata fetch API error: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
EOF'

print_status "Metadata routes added to web.py"
