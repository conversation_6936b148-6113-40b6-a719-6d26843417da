# Use Python slim image as base
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Clone Calibre-Web
RUN git clone https://github.com/janeczku/calibre-web.git .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install Elasticsearch client for enhanced search
RUN pip install --no-cache-dir "elasticsearch>=8.0.0,<9.0.0"

# Copy our enhanced search modules
COPY cps/search_enhanced.py ./cps/
COPY cps/templates/search_enhanced.html ./cps/templates/
COPY cps/elasticsearch_service.py ./cps/
COPY cps/templates/layout.html ./cps/templates/

# Copy and run the patch script to add enhanced search to main.py
COPY main_patch.py ./
RUN python main_patch.py

# Create necessary directories
RUN mkdir -p /config /books

# Expose port
EXPOSE 8083

# Set environment variables
ENV CALIBRE_DBPATH=/config
ENV CALIBRE_PORT=8083

# Start command
CMD ["python", "cps.py"]
