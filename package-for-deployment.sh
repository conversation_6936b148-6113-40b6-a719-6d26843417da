#!/bin/bash

# Package Enhanced Calibre-Web for Deployment
# This script creates a clean deployment package

echo "📦 Creating Enhanced Calibre-Web Deployment Package"
echo "=================================================="

PACKAGE_NAME="enhanced-calibre-web-$(date +%Y%m%d)"
PACKAGE_DIR="/tmp/$PACKAGE_NAME"

# Create package directory
mkdir -p "$PACKAGE_DIR"

echo "✅ Created package directory: $PACKAGE_DIR"

# Copy essential deployment files
cp docker-compose.production.yml "$PACKAGE_DIR/"
cp deploy-enhanced-calibre.sh "$PACKAGE_DIR/"
cp main_patch.py "$PACKAGE_DIR/"
cp requirements.txt "$PACKAGE_DIR/"
cp README_DEPLOYMENT.md "$PACKAGE_DIR/"
cp README_ENHANCED_SEARCH.md "$PACKAGE_DIR/"
cp LICENSE "$PACKAGE_DIR/"

# Copy enhanced source code
cp -r cps "$PACKAGE_DIR/"

# Create empty directories for volumes
mkdir -p "$PACKAGE_DIR"/{books,cache,config}

# Create a simple README for the package
cat > "$PACKAGE_DIR/README.md" << 'EOF'
# Enhanced Calibre-Web Deployment Package

## Quick Start
1. Extract this package to your server
2. Run: `chmod +x deploy-enhanced-calibre.sh`
3. Run: `./deploy-enhanced-calibre.sh`
4. Access: http://localhost:5595

## Features
- Advanced search with Elasticsearch
- Modern responsive UI
- Smart filtering and sorting
- Multi-field search capabilities

See README_DEPLOYMENT.md for detailed instructions.
EOF

# Create archive
cd /tmp
tar -czf "$PACKAGE_NAME.tar.gz" "$PACKAGE_NAME"

echo "✅ Package created: /tmp/$PACKAGE_NAME.tar.gz"
echo "📁 Package size: $(du -h /tmp/$PACKAGE_NAME.tar.gz | cut -f1)"
echo ""
echo "🚀 To deploy on another PC:"
echo "1. Copy /tmp/$PACKAGE_NAME.tar.gz to the target machine"
echo "2. Extract: tar -xzf $PACKAGE_NAME.tar.gz"
echo "3. cd $PACKAGE_NAME"
echo "4. ./deploy-enhanced-calibre.sh"
echo ""
echo "📦 Package ready for deployment!"
