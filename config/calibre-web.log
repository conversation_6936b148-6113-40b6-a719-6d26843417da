[2025-08-07 16:56:27,822]  WARN {cps.config_sql:364} Log path /config/calibre-web.log not valid, falling back to default
[2025-08-07 16:56:27,831]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-07 16:56:27,834]  WARN {cps.config_sql:400} invalidating configuration
[2025-08-07 16:56:27,834]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-07 16:56:28,564]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-07 16:56:28,565]  WARN {cps.config_sql:400} invalidating configuration
[2025-08-07 16:56:28,565]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-07 16:56:28,584]  INFO {cps:178} Starting Calibre Web...
[2025-08-07 16:56:28,791]  WARN {py.warnings:110} /lsiopy/lib/python3.12/site-packages/flask_limiter/extension.py:333: UserWarning: Using the in-memory storage for tracking rate limits as no storage was explicitly specified. This is not recommended for production use. See: https://flask-limiter.readthedocs.io#configuring-a-storage-backend for documentation about configuring the storage backend.
  warnings.warn(

[2025-08-07 16:56:28,900]  INFO {apscheduler.scheduler:181} Scheduler started
[2025-08-07 16:56:28,900]  INFO {apscheduler.scheduler:895} Added job "delete temp" to job store "default"
[2025-08-07 16:56:28,900]  INFO {apscheduler.scheduler:895} Added job "end scheduled task" to job store "default"
[2025-08-07 16:56:28,900]  INFO {apscheduler.scheduler:895} Added job "immediately delete temp" to job store "default"
[2025-08-07 16:56:28,901]  INFO {apscheduler.executors.default:123} Running job "immediately delete temp (trigger: date[2025-08-07 16:56:28 UTC], next run at: 2025-08-07 16:56:28 UTC)" (scheduled at 2025-08-07 16:56:28.900835+00:00)
[2025-08-07 16:56:28,901]  INFO {apscheduler.scheduler:641} Removed job 3f38203f938c426381263be3ad313f60
[2025-08-07 16:56:28,912]  INFO {apscheduler.executors.default:144} Job "immediately delete temp (trigger: date[2025-08-07 16:56:28 UTC], next run at: 2025-08-07 16:56:28 UTC)" executed successfully
[2025-08-07 16:56:28,985]  INFO {cps.ub:764} github Blueprint Created
[2025-08-07 16:56:28,988]  INFO {cps.ub:764} google Blueprint Created
[2025-08-07 16:56:29,674]  INFO {cps.server:218} Starting Gevent server on [::]:8083
[2025-08-07 16:57:10,171]  WARN {cps.web:1446} Login failed for user "admin" IP-address: ::ffff:**********
[2025-08-07 16:57:24,671]  WARN {cps.web:1446} Login failed for user "admin" IP-address: ::ffff:**********
[2025-08-07 17:02:59,644]  INFO {cps.server:319} webserver stop (restart=False)
[2025-08-07 17:02:59,644]  INFO {apscheduler.scheduler:212} Scheduler has been shut down
[2025-08-07 17:03:00,646]  INFO {cps.server:298} Performing shutdown of Calibre-Web
[2025-08-07 17:03:04,956]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-07 17:03:04,957]  WARN {cps.config_sql:400} invalidating configuration
[2025-08-07 17:03:04,957]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-07 17:03:04,972]  INFO {cps:178} Starting Calibre Web...
[2025-08-07 17:03:05,173]  WARN {py.warnings:110} /lsiopy/lib/python3.12/site-packages/flask_limiter/extension.py:333: UserWarning: Using the in-memory storage for tracking rate limits as no storage was explicitly specified. This is not recommended for production use. See: https://flask-limiter.readthedocs.io#configuring-a-storage-backend for documentation about configuring the storage backend.
  warnings.warn(

[2025-08-07 17:03:05,271]  INFO {apscheduler.scheduler:181} Scheduler started
[2025-08-07 17:03:05,271]  INFO {apscheduler.scheduler:895} Added job "delete temp" to job store "default"
[2025-08-07 17:03:05,271]  INFO {apscheduler.scheduler:895} Added job "end scheduled task" to job store "default"
[2025-08-07 17:03:05,271]  INFO {apscheduler.scheduler:895} Added job "immediately delete temp" to job store "default"
[2025-08-07 17:03:05,272]  INFO {apscheduler.executors.default:123} Running job "immediately delete temp (trigger: date[2025-08-07 17:03:05 UTC], next run at: 2025-08-07 17:03:05 UTC)" (scheduled at 2025-08-07 17:03:05.271681+00:00)
[2025-08-07 17:03:05,272]  INFO {apscheduler.scheduler:641} Removed job 494b1c26dd344da4aee3da6dba064404
[2025-08-07 17:03:05,283]  INFO {apscheduler.executors.default:144} Job "immediately delete temp (trigger: date[2025-08-07 17:03:05 UTC], next run at: 2025-08-07 17:03:05 UTC)" executed successfully
[2025-08-07 17:03:06,092]  INFO {cps.server:218} Starting Gevent server on [::]:8083
[2025-08-07 17:07:13,833] ERROR {cps.admin:1910} client_secrets.json is not configured for web application
[2025-08-07 17:07:13,833] ERROR {cps.admin:1917} DB Location is not Valid, Please Enter Correct Path
[2025-08-07 17:07:13,834]  WARN {cps.config_sql:364} Log path  not valid, falling back to default
[2025-08-08 04:00:00,001]  INFO {apscheduler.executors.default:123} Running job "delete temp (trigger: cron[hour='4'], next run at: 2025-08-09 04:00:00 UTC)" (scheduled at 2025-08-08 04:00:00+00:00)
[2025-08-08 04:00:00,005]  INFO {apscheduler.executors.default:144} Job "delete temp (trigger: cron[hour='4'], next run at: 2025-08-09 04:00:00 UTC)" executed successfully
[2025-08-08 04:10:00,001]  INFO {apscheduler.executors.default:123} Running job "end scheduled task (trigger: cron[hour='4', minute='10'], next run at: 2025-08-09 04:10:00 UTC)" (scheduled at 2025-08-08 04:10:00+00:00)
[2025-08-08 04:10:00,002]  INFO {apscheduler.executors.default:144} Job "end scheduled task (trigger: cron[hour='4', minute='10'], next run at: 2025-08-09 04:10:00 UTC)" executed successfully
