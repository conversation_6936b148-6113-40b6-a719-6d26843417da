# This is a patch to add enhanced search to the original main.py
# We'll use this to modify the original file during Docker build

import re

def patch_main_py():
    # Read the original main.py
    with open('/app/calibre-web/cps/main.py', 'r') as f:
        content = f.read()

    # Add our enhanced search import after the search import
    search_import = "from .search import search"
    enhanced_import = "from .search_enhanced import search_enhanced"

    if enhanced_import not in content:
        content = content.replace(search_import, f"{search_import}\n    {enhanced_import}")

    # Add our enhanced search blueprint registration after the search registration
    search_register = "app.register_blueprint(search)"
    enhanced_register = "app.register_blueprint(search_enhanced)"

    if enhanced_register not in content:
        content = content.replace(search_register, f"{search_register}\n    {enhanced_register}")

    # Write the modified content back
    with open('/app/calibre-web/cps/main.py', 'w') as f:
        f.write(content)

if __name__ == "__main__":
    patch_main_py()
    print("Enhanced search successfully added to main.py")
