#!/bin/bash

# Fix jQuery <PERSON>r in Advanced Search
echo "🔧 Fixing jQuery <PERSON>rror in Advanced Search"
echo "========================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Fix the enhanced search template to use vanilla JavaScript instead of jQuery
print_info "Updating enhanced search template to fix JavaScript errors..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/templates/enhanced_search.html << '"'"'EOF'"'"'
{% extends "layout.html" %}
{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-3 col-lg-2 sidebar">
            {% include 'sidebar.html' %}
        </div>
        <div class="col-sm-9 col-lg-10 main">
            <h1>{{_("Enhanced Search")}}</h1>
            
            <div class="row">
                <div class="col-md-8">
                    <form id="enhanced-search-form" action="/search" method="GET">
                        <div class="form-group">
                            <label for="query">{{_("Search Query")}}</label>
                            <input type="text" class="form-control" id="query" name="query" 
                                   placeholder="Enter your search terms..." required>
                        </div>
                        
                        <div class="form-group">
                            <label for="include_tag">{{_("Include Tags")}}</label>
                            <input type="text" class="form-control" id="include_tag" name="include_tag" 
                                   placeholder="Tags to include (comma separated)">
                            <small class="form-text text-muted">Example: fiction, science, adventure</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="exclude_tag">{{_("Exclude Tags")}}</label>
                            <input type="text" class="form-control" id="exclude_tag" name="exclude_tag" 
                                   placeholder="Tags to exclude (comma separated)">
                            <small class="form-text text-muted">Example: romance, horror</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="include_serie">{{_("Series")}}</label>
                            <input type="text" class="form-control" id="include_serie" name="include_serie" 
                                   placeholder="Series name">
                        </div>
                        
                        <div class="form-group">
                            <label for="include_language">{{_("Language")}}</label>
                            <select class="form-control" id="include_language" name="include_language">
                                <option value="">{{_("Any Language")}}</option>
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="it">Italian</option>
                                <option value="pt">Portuguese</option>
                                <option value="ru">Russian</option>
                                <option value="ja">Japanese</option>
                                <option value="zh">Chinese</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="book_title">{{_("Book Title")}}</label>
                            <input type="text" class="form-control" id="book_title" name="book_title" 
                                   placeholder="Specific book title">
                        </div>
                        
                        <div class="form-group">
                            <label for="author">{{_("Author")}}</label>
                            <input type="text" class="form-control" id="author" name="author" 
                                   placeholder="Author name">
                        </div>
                        
                        <div class="form-group">
                            <label for="publisher">{{_("Publisher")}}</label>
                            <input type="text" class="form-control" id="publisher" name="publisher" 
                                   placeholder="Publisher name">
                        </div>
                        
                        <div class="form-group">
                            <label for="rating_min">{{_("Minimum Rating")}}</label>
                            <select class="form-control" id="rating_min" name="rating_min">
                                <option value="">{{_("Any Rating")}}</option>
                                <option value="1">1 Star</option>
                                <option value="2">2 Stars</option>
                                <option value="3">3 Stars</option>
                                <option value="4">4 Stars</option>
                                <option value="5">5 Stars</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">{{_("Enhanced Search")}}</button>
                        <button type="button" class="btn btn-secondary" onclick="clearForm()">{{_("Clear")}}</button>
                        <a href="/search" class="btn btn-info">{{_("Basic Search")}}</a>
                    </form>
                </div>
                
                <div class="col-md-4">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4>{{_("Search Tips")}}</h4>
                        </div>
                        <div class="panel-body">
                            <ul>
                                <li>{{_("Use multiple fields for more precise results")}}</li>
                                <li>{{_("Tags: separate multiple tags with commas")}}</li>
                                <li>{{_("Leave fields empty to ignore them")}}</li>
                                <li>{{_("Search is case-insensitive")}}</li>
                                <li>{{_("Partial matches are supported")}}</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4>{{_("Quick Searches")}}</h4>
                        </div>
                        <div class="panel-body">
                            <p><a href="/search?query=*" class="btn btn-sm btn-default">{{_("All Books")}}</a></p>
                            <p><a href="/search?include_tag=fiction" class="btn btn-sm btn-default">{{_("Fiction")}}</a></p>
                            <p><a href="/search?include_tag=non-fiction" class="btn btn-sm btn-default">{{_("Non-Fiction")}}</a></p>
                            <p><a href="/search?include_tag=science" class="btn btn-sm btn-default">{{_("Science")}}</a></p>
                            <p><a href="/search?author=*" class="btn btn-sm btn-default">{{_("Browse Authors")}}</a></p>
                        </div>
                    </div>
                    
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>{{_("Example Searches")}}</h4>
                        </div>
                        <div class="panel-body">
                            <small>
                                <p><strong>Fiction books by specific author:</strong><br>
                                Query: <em>fantasy</em>, Include Tags: <em>fiction</em>, Author: <em>tolkien</em></p>
                                
                                <p><strong>Science books excluding textbooks:</strong><br>
                                Query: <em>physics</em>, Include Tags: <em>science</em>, Exclude Tags: <em>textbook</em></p>
                                
                                <p><strong>High-rated books in a series:</strong><br>
                                Series: <em>harry potter</em>, Min Rating: <em>4 Stars</em></p>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Use vanilla JavaScript instead of jQuery to avoid dependency issues
function clearForm() {
    document.getElementById("enhanced-search-form").reset();
}

// Auto-submit when Enter is pressed in any field
document.addEventListener("DOMContentLoaded", function() {
    var inputs = document.querySelectorAll("input");
    inputs.forEach(function(input) {
        input.addEventListener("keypress", function(e) {
            if (e.key === "Enter") {
                document.getElementById("enhanced-search-form").submit();
            }
        });
    });
});

// Add some visual feedback
document.addEventListener("DOMContentLoaded", function() {
    var form = document.getElementById("enhanced-search-form");
    form.addEventListener("submit", function() {
        var submitBtn = form.querySelector("button[type=submit]");
        submitBtn.innerHTML = "Searching...";
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
EOF'

print_status "Enhanced search template updated with vanilla JavaScript"

# Also check if there are any JavaScript errors in the advanced search template
print_info "Checking for JavaScript issues in advanced search template..."
sudo docker exec $CONTAINER_NAME bash -c 'if [ -f "/app/calibre-web/cps/templates/search.html" ]; then
    # Backup the original search template
    cp /app/calibre-web/cps/templates/search.html /app/calibre-web/cps/templates/search.html.backup
    
    # Check if there are jQuery dependencies that need fixing
    if grep -q "\\$(" /app/calibre-web/cps/templates/search.html; then
        echo "Found jQuery usage in search.html - this may need fixing"
    else
        echo "No obvious jQuery issues in search.html"
    fi
else
    echo "search.html template not found"
fi'

# Restart the container to apply changes
print_info "Restarting Calibre-Web to apply JavaScript fixes..."
sudo docker restart $CONTAINER_NAME

print_info "Waiting for restart..."
sleep 25

# Test the enhanced search page
print_info "Testing enhanced search page..."
if curl -s "http://localhost:8083/enhanced-search" | grep -q "Enhanced Search"; then
    print_status "Enhanced search page is working!"
else
    print_warning "Enhanced search page may need more time"
fi

echo ""
echo "🎉 JavaScript Errors Fixed!"
echo "=========================="
echo ""
print_info "Changes made:"
echo "  ✅ Replaced jQuery with vanilla JavaScript"
echo "  ✅ Added proper event listeners"
echo "  ✅ Improved form functionality"
echo "  ✅ Added visual feedback"
echo "  ✅ Added more search examples"
echo ""
print_info "Access the enhanced search at:"
echo "  🔍 http://*************:8083/enhanced-search"
echo ""
print_status "JavaScript errors should now be resolved! 🚀"
