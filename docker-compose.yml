version: '3.8'

services:
  calibre-web:
    image: calibre-web-enhanced:latest
    container_name: calibre-web-enhanced
    restart: unless-stopped
    privileged: true
    ports:
      - "5595:8083"
    environment:
      # Basic Configuration
      - CALIBRE_DBPATH=/config
      - CALIBRE_PORT=5595
      - CALIBRE_RECONNECT=1
      - CALIBRE_LOCALHOST=1
      
      # Authentication Configuration (Enhanced)
      - CALIBRE_DEFAULT_PASSWORD=admin123
      - CALIBRE_SIMPLIFIED_AUTH=false
      - CALIBRE_PASSWORD_FREE_MODE=false
      
      # Elasticsearch Configuration
      - ELASTICSEARCH_ENABLED=true
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - ELASTICSEARCH_INDEX=calibre-books
      
      # Optional: Security
      - SECRET_KEY=your-secret-key-change-this
      - COOKIE_PREFIX=calibre_
      
      # Optional: Performance
      - WORKERS=1
      - TIMEOUT=120
      
    volumes:
      - ./config:/config
      - ./books:/books
      - ./cache:/app/cps/cache
    depends_on:
      - elasticsearch
    networks:
      - calibre-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5595/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: calibre-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - calibre-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Optional: Kibana for Elasticsearch management
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: calibre-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - calibre-network
    profiles:
      - kibana

volumes:
  elasticsearch_data:
    driver: local

networks:
  calibre-network:
    driver: bridge
