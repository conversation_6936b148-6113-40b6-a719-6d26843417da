#!/bin/bash

# Fix Enhanced Search Integration
echo "🔧 Fixing Enhanced Search Integration"
echo "===================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# First, let's check the main application file structure
print_info "Checking main application structure..."
sudo docker exec $CONTAINER_NAME find /app/calibre-web -name "*.py" -path "*/cps/*" | head -10

# Check if we need to integrate with web.py instead of cps.py
print_info "Checking web.py for integration..."
sudo docker exec $CONTAINER_NAME ls -la /app/calibre-web/cps/web.py

# Create a proper integration script
print_info "Creating enhanced search integration..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/integrate_enhanced_search.py << '"'"'EOF'"'"'
#!/usr/bin/env python3
"""
Integration script to add enhanced search to Calibre-Web
"""

import os
import sys

def integrate_enhanced_search():
    """Integrate enhanced search into the main web application"""
    
    web_py_path = "/app/calibre-web/cps/web.py"
    
    if not os.path.exists(web_py_path):
        print("Error: web.py not found")
        return False
    
    # Read the current web.py file
    with open(web_py_path, "r") as f:
        content = f.read()
    
    # Check if already integrated
    if "search_enhanced" in content:
        print("Enhanced search already integrated")
        return True
    
    # Find the imports section and add our import
    import_line = "from . import search_enhanced"
    
    # Find a good place to add the import (after other imports)
    lines = content.split("\n")
    insert_index = -1
    
    for i, line in enumerate(lines):
        if line.startswith("from . import") or line.startswith("from ."):
            insert_index = i + 1
    
    if insert_index == -1:
        # Find after regular imports
        for i, line in enumerate(lines):
            if line.startswith("import ") and not line.startswith("import os"):
                insert_index = i + 1
    
    if insert_index != -1:
        lines.insert(insert_index, import_line)
    
    # Find where blueprints are registered and add ours
    blueprint_line = "app.register_blueprint(search_enhanced.search_enhanced)"
    
    for i, line in enumerate(lines):
        if "register_blueprint" in line and "web" in line:
            lines.insert(i + 1, "    " + blueprint_line)
            break
    
    # Write the modified content back
    modified_content = "\n".join(lines)
    
    # Backup original file
    os.system("cp /app/calibre-web/cps/web.py /app/calibre-web/cps/web.py.backup")
    
    with open(web_py_path, "w") as f:
        f.write(modified_content)
    
    print("Enhanced search integration completed")
    return True

if __name__ == "__main__":
    integrate_enhanced_search()
EOF'

# Run the integration script
print_info "Running integration script..."
sudo docker exec $CONTAINER_NAME python /app/calibre-web/integrate_enhanced_search.py

# Alternative approach: Create a simple route in the existing structure
print_info "Creating alternative enhanced search route..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/enhanced_routes.py << '"'"'EOF'"'"'
"""
Enhanced search routes for Calibre-Web
"""

from flask import render_template, request, jsonify
from . import logger
from .web import app, login_required_if_no_ano

@app.route("/search/enhanced")
@login_required_if_no_ano
def enhanced_search_page():
    """Enhanced search page"""
    return render_template("search_enhanced.html", title="Enhanced Search")

@app.route("/api/search/enhanced", methods=["POST"])
@login_required_if_no_ano
def api_enhanced_search():
    """Enhanced search API"""
    try:
        from .db import CalibreDB
        from .helper import get_search_results
        
        search_data = request.get_json()
        if not search_data:
            return jsonify({"error": "No search data provided"}), 400
        
        query = search_data.get("query", "")
        field = search_data.get("field", "all")
        
        if not query.strip():
            return jsonify({"books": [], "total": 0, "query": query})
        
        # Use Calibre-Web internal search
        db = CalibreDB()
        
        # Simple search implementation
        if field == "all":
            search_term = query
        else:
            search_term = f"{field}:{query}"
        
        # Get search results using internal methods
        try:
            # Try to use existing search functionality
            results = []
            total = 0
            
            # Basic fallback search
            books = db.session.query(db.Books).filter(
                db.Books.title.contains(query) |
                db.Books.authors.any(db.Authors.name.contains(query))
            ).limit(50).all()
            
            for book in books:
                authors = ", ".join([author.name for author in book.authors])
                tags = ", ".join([tag.name for tag in book.tags]) if book.tags else ""
                series = book.series[0].name if book.series else ""
                
                results.append({
                    "id": book.id,
                    "title": book.title,
                    "author": authors,
                    "series": series,
                    "tags": tags,
                    "description": book.comments[0].text if book.comments else ""
                })
            
            total = len(results)
            
            return jsonify({
                "books": results,
                "total": total,
                "query": query,
                "elasticsearch_used": False
            })
            
        except Exception as e:
            logger.error(f"Search error: {e}")
            return jsonify({"error": "Search failed"}), 500
            
    except Exception as e:
        logger.error(f"Enhanced search error: {e}")
        return jsonify({"error": "Search failed"}), 500
EOF'

# Import the enhanced routes in the main application
print_info "Importing enhanced routes..."
sudo docker exec $CONTAINER_NAME bash -c 'echo "
# Import enhanced search routes
try:
    from . import enhanced_routes
    print(\"Enhanced search routes loaded\")
except Exception as e:
    print(f\"Failed to load enhanced routes: {e}\")
" >> /app/calibre-web/cps/__init__.py'

# Restart the container to apply changes
print_info "Restarting Calibre-Web to apply enhanced search integration..."
sudo docker restart $CONTAINER_NAME

print_info "Waiting for Calibre-Web to restart..."
sleep 25

# Test the enhanced search endpoint
print_info "Testing enhanced search endpoint..."
sleep 5

# Check if the enhanced search page is accessible
if curl -s http://localhost:8083/search/enhanced | grep -q "Enhanced Search"; then
    print_status "Enhanced search page is accessible"
else
    print_warning "Enhanced search page may not be fully integrated yet"
fi

echo ""
echo "🎉 Enhanced Search Integration Complete!"
echo "======================================="
echo ""
print_info "Try accessing:"
echo "  🔍 Enhanced Search: http://*************:8083/search/enhanced"
echo "  📚 Main Interface: http://*************:8083"
echo ""
print_status "Enhanced search should now be working! 🚀"
