# Calibre-Web Enhanced Search

This enhanced version of Calibre-Web includes advanced search capabilities powered by Elasticsearch, providing faster and more sophisticated search functionality.

## 🚀 Features

### Enhanced Search Interface
- **Advanced Search Form**: Comprehensive search interface with multiple filter options
- **Text Search**: Search by title, author, and description with different operators (contains, exact, starts with)
- **Multi-Select Filters**: Filter by tags, series, publishers, languages, and formats
- **Range Filters**: Filter by rating range and publication date range
- **Flexible Sorting**: Sort results by relevance, title, author, publication date, rating, or date added
- **Real-time Suggestions**: Autocomplete suggestions for search fields

### Elasticsearch Integration
- **High-Performance Search**: Leverages Elasticsearch for fast, full-text search
- **Fuzzy Matching**: Finds results even with typos or partial matches
- **Relevance Scoring**: Results ranked by relevance with customizable boost factors
- **Faceted Search**: Advanced filtering capabilities
- **Fallback Support**: Gracefully falls back to SQL search when Elasticsearch is unavailable

### User Experience
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **AJAX-Powered**: Search results load without page refresh
- **Pagination**: Navigate through large result sets efficiently
- **Visual Feedback**: Loading indicators and search status information
- **Save Searches**: (Planned) Save and reuse complex search queries

## 🛠 Installation

### Using Docker (Recommended)

1. **Build the Enhanced Image**:
   ```bash
   docker build -t calibre-web-enhanced .
   ```

2. **Start the Services**:
   ```bash
   docker-compose up -d
   ```

3. **Access the Application**:
   - Calibre-Web: http://localhost:5595
   - Elasticsearch: http://localhost:9200
   - Kibana (optional): http://localhost:5601

### Manual Installation

1. **Install Dependencies**:
   ```bash
   pip install "elasticsearch>=8.0.0,<9.0.0"
   ```

2. **Copy Enhanced Files**:
   - Copy `cps/search_enhanced.py` to your Calibre-Web installation
   - Copy `cps/templates/search_enhanced.html` to your templates directory
   - Update `cps/main.py` to register the enhanced search blueprint

3. **Configure Elasticsearch** (optional):
   - Install and start Elasticsearch
   - Configure connection settings in Calibre-Web admin panel

## ⚙️ Configuration

### Elasticsearch Settings

Access the Calibre-Web admin panel and configure Elasticsearch:

1. Go to **Admin** → **Configuration** → **Elasticsearch Configuration**
2. Enable Elasticsearch
3. Set connection details:
   - **Host**: `elasticsearch` (for Docker) or `localhost`
   - **Port**: `9200`
   - **Index**: `calibre-books`

### Environment Variables

For Docker deployments, you can use these environment variables:

```yaml
environment:
  - ELASTICSEARCH_ENABLED=true
  - ELASTICSEARCH_HOST=elasticsearch
  - ELASTICSEARCH_PORT=9200
  - ELASTICSEARCH_INDEX=calibre-books
```

## 📖 Usage

### Accessing Enhanced Search

1. **Navigation**: Click "Enhanced Search" in the top navigation bar
2. **Search Interface**: Use the comprehensive search form with multiple filters
3. **Results**: View paginated results with detailed book information
4. **Refinement**: Adjust filters and re-search as needed

### Search Features

#### Text Search
- **Title Search**: Find books by title with exact, contains, or starts-with matching
- **Author Search**: Search for books by author name
- **Description Search**: Full-text search in book descriptions

#### Filters
- **Tags**: Include or exclude specific tags
- **Series**: Filter by book series
- **Publishers**: Filter by publisher
- **Languages**: Filter by book language
- **Formats**: Filter by file format (PDF, EPUB, etc.)
- **Rating Range**: Filter by rating (0-5 stars)
- **Date Range**: Filter by publication date

#### Sorting
- **Relevance**: Best matches first (Elasticsearch only)
- **Title**: Alphabetical by title
- **Author**: Alphabetical by author
- **Publication Date**: Newest or oldest first
- **Rating**: Highest or lowest rated first
- **Date Added**: Recently added first

## 🔧 Technical Details

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Browser   │◄──►│  Calibre-Web     │◄──►│  Elasticsearch  │
│                 │    │  Enhanced        │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  SQLite/MySQL    │
                       │  Database        │
                       └──────────────────┘
```

### Components

- **SearchQueryBuilder**: Builds complex search queries for both Elasticsearch and SQL
- **Enhanced Search Routes**: RESTful API endpoints for search operations
- **Elasticsearch Service**: Manages Elasticsearch connection and indexing
- **Responsive UI**: Modern search interface with AJAX functionality

### Performance

- **Elasticsearch**: Sub-second search response times for large libraries
- **Caching**: Intelligent caching of search results and metadata
- **Pagination**: Efficient handling of large result sets
- **Fallback**: Graceful degradation to SQL search when needed

## 🧪 Testing

Run the test suite to verify functionality:

```bash
python test_enhanced_search.py
```

Tests cover:
- Search query building
- Elasticsearch integration
- Template rendering
- Docker configuration

## 🤝 Contributing

Contributions are welcome! Please:

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📝 License

This enhanced search functionality is released under the same license as Calibre-Web (GPL v3).

## 🆘 Support

For issues and questions:

1. Check the existing Calibre-Web documentation
2. Review Elasticsearch configuration
3. Check Docker logs: `docker-compose logs calibre-web`
4. Verify Elasticsearch health: `curl http://localhost:9200/_cluster/health`

## 🔮 Future Enhancements

- **Saved Searches**: Save and manage complex search queries
- **Search Analytics**: Track popular searches and improve recommendations
- **Advanced Faceting**: More sophisticated filtering options
- **Search Highlighting**: Highlight matching terms in results
- **Export Results**: Export search results to various formats
- **Search API**: RESTful API for external integrations
