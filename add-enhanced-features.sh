#!/bin/bash

# Add Enhanced Features to Running Calibre-Web Container
echo "🔧 Adding Enhanced Search Features to Calibre-Web"
echo "================================================="

CONTAINER_NAME="calibre-web1"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if container is running
if ! sudo docker ps | grep -q $CONTAINER_NAME; then
    print_error "Container $CONTAINER_NAME is not running"
    exit 1
fi

print_status "Container $CONTAINER_NAME is running"

# Create enhanced search module directly in container
print_info "Creating enhanced search module..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/search_enhanced.py << '"'"'EOF'"'"'
"""
Enhanced Search Module for Calibre-Web
Provides advanced search capabilities with Elasticsearch integration
"""

import os
import logging
from flask import Blueprint, render_template, request, jsonify, current_app
from . import logger, config
from .web import login_required_if_no_ano

try:
    from .elasticsearch_service import ElasticsearchService
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    logger.warning("Elasticsearch service not available")

search_enhanced = Blueprint("search_enhanced", __name__)

@search_enhanced.route("/search/advanced")
@login_required_if_no_ano
def advanced_search():
    """Render the advanced search page"""
    return render_template("search_enhanced.html", 
                         title="Advanced Search",
                         elasticsearch_enabled=ELASTICSEARCH_AVAILABLE and config.config_elasticsearch)

@search_enhanced.route("/api/search/enhanced", methods=["POST"])
@login_required_if_no_ano
def api_enhanced_search():
    """API endpoint for enhanced search"""
    try:
        search_data = request.get_json()
        
        if not search_data:
            return jsonify({"error": "No search data provided"}), 400
            
        # Basic search implementation
        results = {
            "books": [],
            "total": 0,
            "query": search_data.get("query", ""),
            "elasticsearch_used": False
        }
        
        # If Elasticsearch is available and enabled, use it
        if ELASTICSEARCH_AVAILABLE and config.config_elasticsearch:
            try:
                es_service = ElasticsearchService()
                if es_service.is_available():
                    results = es_service.enhanced_search(search_data)
                    results["elasticsearch_used"] = True
            except Exception as e:
                logger.warning(f"Elasticsearch search failed: {e}")
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Enhanced search error: {e}")
        return jsonify({"error": "Search failed"}), 500
EOF'

print_status "Enhanced search module created"

# Create enhanced search template
print_info "Creating enhanced search template..."
sudo docker exec $CONTAINER_NAME bash -c 'mkdir -p /app/calibre-web/cps/templates'
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/templates/search_enhanced.html << '"'"'EOF'"'"'
{% extends "layout.html" %}
{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-3 col-lg-2 sidebar">
            <h4>{{_("Advanced Search")}}</h4>
        </div>
        <div class="col-sm-9 col-lg-10 main">
            <h1>{{_("Enhanced Search")}}</h1>
            
            <div class="row">
                <div class="col-md-8">
                    <form id="enhanced-search-form">
                        <div class="form-group">
                            <label for="search-query">{{_("Search Query")}}</label>
                            <input type="text" class="form-control" id="search-query" 
                                   placeholder="Enter your search terms...">
                        </div>
                        
                        <div class="form-group">
                            <label for="search-field">{{_("Search In")}}</label>
                            <select class="form-control" id="search-field">
                                <option value="all">{{_("All Fields")}}</option>
                                <option value="title">{{_("Title")}}</option>
                                <option value="author">{{_("Author")}}</option>
                                <option value="description">{{_("Description")}}</option>
                                <option value="tags">{{_("Tags")}}</option>
                                <option value="series">{{_("Series")}}</option>
                                <option value="publisher">{{_("Publisher")}}</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">{{_("Search")}}</button>
                        <button type="button" class="btn btn-secondary" id="clear-search">{{_("Clear")}}</button>
                    </form>
                </div>
            </div>
            
            <div id="search-results" class="row" style="margin-top: 20px;">
                <!-- Search results will be displayed here -->
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById("enhanced-search-form").addEventListener("submit", function(e) {
    e.preventDefault();
    performEnhancedSearch();
});

document.getElementById("clear-search").addEventListener("click", function() {
    document.getElementById("search-query").value = "";
    document.getElementById("search-field").value = "all";
    document.getElementById("search-results").innerHTML = "";
});

function performEnhancedSearch() {
    const query = document.getElementById("search-query").value;
    const field = document.getElementById("search-field").value;
    
    if (!query.trim()) {
        alert("Please enter a search query");
        return;
    }
    
    const searchData = {
        query: query,
        field: field
    };
    
    fetch("/api/search/enhanced", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(searchData)
    })
    .then(response => response.json())
    .then(data => {
        displaySearchResults(data);
    })
    .catch(error => {
        console.error("Search error:", error);
        document.getElementById("search-results").innerHTML = 
            "<div class=\"alert alert-danger\">Search failed. Please try again.</div>";
    });
}

function displaySearchResults(data) {
    const resultsDiv = document.getElementById("search-results");
    
    if (data.error) {
        resultsDiv.innerHTML = `<div class="alert alert-danger">${data.error}</div>`;
        return;
    }
    
    if (data.total === 0) {
        resultsDiv.innerHTML = "<div class=\"alert alert-info\">No books found matching your search.</div>";
        return;
    }
    
    let html = `<div class="col-12"><h3>Found ${data.total} books</h3>`;
    if (data.elasticsearch_used) {
        html += "<small class=\"text-muted\">Powered by Elasticsearch</small>";
    }
    html += "</div>";
    
    data.books.forEach(book => {
        html += `
            <div class="col-md-6 col-lg-4 book-item" style="margin-bottom: 20px;">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">${book.title || "Unknown Title"}</h5>
                        <p class="card-text">
                            <strong>Author:</strong> ${book.author || "Unknown"}<br>
                            <strong>Series:</strong> ${book.series || "None"}<br>
                            <strong>Tags:</strong> ${book.tags || "None"}
                        </p>
                        <a href="/book/${book.id}" class="btn btn-primary btn-sm">View Book</a>
                    </div>
                </div>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
}
</script>
{% endblock %}
EOF'

print_status "Enhanced search template created"

# Create basic Elasticsearch service
print_info "Creating Elasticsearch service module..."
sudo docker exec $CONTAINER_NAME bash -c 'cat > /app/calibre-web/cps/elasticsearch_service.py << '"'"'EOF'"'"'
"""
Elasticsearch Service for Enhanced Search
"""

import logging
from . import logger

try:
    from elasticsearch import Elasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False

class ElasticsearchService:
    def __init__(self):
        self.es = None
        self.index_name = "calibre-books"
        
        if ELASTICSEARCH_AVAILABLE:
            try:
                self.es = Elasticsearch([{"host": "localhost", "port": 9200}])
            except Exception as e:
                logger.warning(f"Failed to connect to Elasticsearch: {e}")
    
    def is_available(self):
        if not self.es:
            return False
        try:
            return self.es.ping()
        except:
            return False
    
    def enhanced_search(self, search_data):
        """Perform enhanced search using Elasticsearch"""
        if not self.is_available():
            return {"books": [], "total": 0, "error": "Elasticsearch not available"}
        
        query = search_data.get("query", "")
        field = search_data.get("field", "all")
        
        # Basic search implementation
        try:
            if field == "all":
                search_query = {
                    "query": {
                        "multi_match": {
                            "query": query,
                            "fields": ["title", "author", "description", "tags", "series", "publisher"]
                        }
                    }
                }
            else:
                search_query = {
                    "query": {
                        "match": {
                            field: query
                        }
                    }
                }
            
            response = self.es.search(index=self.index_name, body=search_query)
            
            books = []
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                books.append({
                    "id": source.get("id", ""),
                    "title": source.get("title", ""),
                    "author": source.get("author", ""),
                    "series": source.get("series", ""),
                    "tags": source.get("tags", ""),
                    "description": source.get("description", "")
                })
            
            return {
                "books": books,
                "total": response["hits"]["total"]["value"],
                "query": query
            }
            
        except Exception as e:
            logger.error(f"Elasticsearch search error: {e}")
            return {"books": [], "total": 0, "error": str(e)}
EOF'

print_status "Elasticsearch service module created"

# Restart the container to apply changes
print_info "Restarting Calibre-Web to apply enhanced features..."
sudo docker restart $CONTAINER_NAME

print_info "Waiting for Calibre-Web to restart..."
sleep 20

# Final status check
echo ""
echo "🎉 Enhanced Features Added!"
echo "=========================="
echo ""
print_status "Enhanced Calibre-Web is now running with:"
echo "  🔍 Advanced search capabilities"
echo "  ⚡ Elasticsearch integration (if available)"
echo "  📱 Modern search interface"
echo ""
print_info "Access URLs:"
echo "  📚 Calibre-Web: http://192.168.1.104:8083"
echo "  🔍 Enhanced Search: http://192.168.1.104:8083/search/advanced"
echo "  ⚡ Elasticsearch: http://192.168.1.104:9200"
echo ""
print_status "Enhanced features successfully added! 🚀"
