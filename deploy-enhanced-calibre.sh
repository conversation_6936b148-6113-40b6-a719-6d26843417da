#!/bin/bash

# Enhanced Calibre-Web Deployment Script
# This script deploys Calibre-Web with enhanced search features

set -e

echo "🚀 Enhanced Calibre-Web Deployment Script"
echo "=========================================="

# Configuration
DEFAULT_DATA_DIR="/media/disks/disk2/calibre_rsync"
CONTAINER_NAME="calibre-web1"
ELASTICSEARCH_CONTAINER="calibre-elasticsearch"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are available"

# Get data directory from user
echo ""
read -p "Enter the directory for Calibre data (default: $DEFAULT_DATA_DIR): " DATA_DIR
DATA_DIR=${DATA_DIR:-$DEFAULT_DATA_DIR}

# Create data directories
print_info "Creating data directories..."
sudo mkdir -p "$DATA_DIR"/{calibre,elasticsearch}
sudo chown -R $USER:$USER "$DATA_DIR"
chmod -R 755 "$DATA_DIR"

print_status "Data directories created at: $DATA_DIR"

# Update docker-compose file with user's data directory
print_info "Updating docker-compose configuration..."
sed "s|/media/disks/disk2/calibre_rsync|$DATA_DIR|g" docker-compose.production.yml > docker-compose.yml

print_status "Docker Compose configuration updated"

# Start the services
print_info "Starting Elasticsearch..."
docker-compose up -d elasticsearch

print_info "Waiting for Elasticsearch to be ready..."
sleep 30

# Check if Elasticsearch is healthy
if curl -s http://localhost:9200/_cluster/health | grep -q '"status":"green\|yellow"'; then
    print_status "Elasticsearch is running and healthy"
else
    print_warning "Elasticsearch may still be starting up"
fi

# Start Calibre-Web
print_info "Starting Calibre-Web..."
docker-compose up -d calibre-web

print_info "Waiting for Calibre-Web to start..."
sleep 20

# Install enhanced search features
print_info "Installing enhanced search features..."

# Copy enhanced search files to the container
docker cp cps/search_enhanced.py $CONTAINER_NAME:/app/calibre-web/cps/ 2>/dev/null || print_warning "search_enhanced.py not found in current directory"
docker cp cps/templates/search_enhanced.html $CONTAINER_NAME:/app/calibre-web/cps/templates/ 2>/dev/null || print_warning "search_enhanced.html not found"
docker cp cps/elasticsearch_service.py $CONTAINER_NAME:/app/calibre-web/cps/ 2>/dev/null || print_warning "elasticsearch_service.py not found"
docker cp cps/templates/layout.html $CONTAINER_NAME:/app/calibre-web/cps/templates/ 2>/dev/null || print_warning "layout.html not found"

# Install Elasticsearch client
print_info "Installing Elasticsearch Python client..."
docker exec $CONTAINER_NAME pip install "elasticsearch>=8.0.0,<9.0.0" 2>/dev/null || print_warning "Could not install Elasticsearch client"

# Apply main.py patch
if [ -f "main_patch.py" ]; then
    docker cp main_patch.py $CONTAINER_NAME:/app/
    docker exec $CONTAINER_NAME python /app/main_patch.py 2>/dev/null || print_warning "Could not apply main.py patch"
    print_status "Enhanced search features integrated"
else
    print_warning "main_patch.py not found - enhanced search may not be available"
fi

# Restart Calibre-Web to apply changes
print_info "Restarting Calibre-Web to apply enhancements..."
docker-compose restart calibre-web

print_info "Waiting for services to be ready..."
sleep 30

# Final status check
echo ""
echo "🎉 Deployment Complete!"
echo "======================"
echo ""
print_info "Services Status:"

if docker ps | grep -q $CONTAINER_NAME; then
    print_status "Calibre-Web is running"
else
    print_error "Calibre-Web is not running"
fi

if docker ps | grep -q $ELASTICSEARCH_CONTAINER; then
    print_status "Elasticsearch is running"
else
    print_error "Elasticsearch is not running"
fi

echo ""
print_info "Access URLs:"
echo "  📚 Calibre-Web: http://localhost:5595"
echo "  🔍 Enhanced Search: http://localhost:5595/search/advanced"
echo "  ⚡ Elasticsearch: http://localhost:9200"
echo ""
print_info "Data Location: $DATA_DIR"
echo "  📁 Books: $DATA_DIR/books"
echo "  🗄️  Config: $DATA_DIR/config"
echo "  💾 Cache: $DATA_DIR/cache"
echo "  📊 Elasticsearch: $DATA_DIR/elasticsearch"
echo ""
print_info "Default Login (if first time setup):"
echo "  👤 Username: admin"
echo "  🔑 Password: admin123"
echo ""
print_warning "Remember to:"
echo "  1. Complete the initial setup in Calibre-Web"
echo "  2. Configure your book library path"
echo "  3. Add books to your library"
echo "  4. Try the Enhanced Search features!"
echo ""
print_status "Enhanced Calibre-Web deployment completed successfully! 🚀"
