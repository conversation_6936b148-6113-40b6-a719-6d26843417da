#!/bin/bash

# Enhanced Calibre-Web Maintenance Script
# Quick maintenance operations for daily use

set -e

echo "🔧 Enhanced Calibre-Web Maintenance"
echo "=================================="

# Configuration
CONTAINER_NAME="calibre-web1"
ELASTICSEARCH_CONTAINER="calibre-elasticsearch"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_info() {
    echo -e "\033[1;34mℹ️  $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

# Quick status check
quick_status() {
    echo "📊 Quick Status Check:"
    echo "===================="
    
    # Check containers
    if docker ps | grep -q $CONTAINER_NAME; then
        print_status "Calibre-Web: Running"
    else
        print_error "Calibre-Web: Not running"
    fi
    
    if docker ps | grep -q $ELASTICSEARCH_CONTAINER; then
        print_status "Elasticsearch: Running"
    else
        print_error "Elasticsearch: Not running"
    fi
    
    # Check services
    if curl -s http://localhost:5595 >/dev/null 2>&1; then
        print_status "Web interface: Accessible"
    else
        print_error "Web interface: Not accessible"
    fi
    
    if curl -s http://localhost:9200 >/dev/null 2>&1; then
        print_status "Elasticsearch API: Accessible"
    else
        print_error "Elasticsearch API: Not accessible"
    fi
}

# Restart services
restart_services() {
    print_info "Restarting services..."
    docker-compose restart
    sleep 10
    print_status "Services restarted"
}

# View logs
view_logs() {
    echo "📋 Recent Logs:"
    echo "=============="
    echo ""
    echo "🔍 Calibre-Web logs (last 20 lines):"
    docker logs $CONTAINER_NAME --tail=20 2>/dev/null || print_warning "Could not fetch Calibre-Web logs"
    echo ""
    echo "⚡ Elasticsearch logs (last 10 lines):"
    docker logs $ELASTICSEARCH_CONTAINER --tail=10 2>/dev/null || print_warning "Could not fetch Elasticsearch logs"
}

# Clean cache
clean_cache() {
    print_info "Cleaning cache..."
    rm -rf cache/* 2>/dev/null && print_status "Cache cleaned" || print_info "Cache already clean"
    docker exec $CONTAINER_NAME rm -rf /tmp/* 2>/dev/null && print_status "Container temp files cleaned" || print_info "Container temp already clean"
}

# Update containers
update_containers() {
    print_info "Updating containers..."
    docker-compose pull
    docker-compose up -d
    print_status "Containers updated"
}

# Backup configuration
backup_config() {
    BACKUP_FILE="config-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    tar -czf "$BACKUP_FILE" config/ 2>/dev/null && print_status "Config backed up to $BACKUP_FILE" || print_error "Backup failed"
}

# Show help
show_help() {
    echo "🔧 Maintenance Commands:"
    echo "======================"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  status     - Quick status check"
    echo "  restart    - Restart all services"
    echo "  logs       - View recent logs"
    echo "  cache      - Clean cache files"
    echo "  update     - Update container images"
    echo "  backup     - Backup configuration"
    echo "  help       - Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 status"
    echo "  $0 restart"
    echo "  $0 logs"
}

# Main logic
case "${1:-status}" in
    "status")
        quick_status
        ;;
    "restart")
        restart_services
        quick_status
        ;;
    "logs")
        view_logs
        ;;
    "cache")
        clean_cache
        ;;
    "update")
        update_containers
        quick_status
        ;;
    "backup")
        backup_config
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
